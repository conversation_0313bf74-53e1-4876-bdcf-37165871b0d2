import { dirname } from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends('next/core-web-vitals', 'next/typescript'),
  // Add Prettier config
  ...compat.extends('prettier'),
  {
    ignores: ['node_modules', '.next', 'out', 'build', 'dist'],
  },
  {
    // Use object format for plugins in flat config
    plugins: {
      prettier: require('eslint-plugin-prettier'),
    },
    rules: {
      // Prettier rules
      'prettier/prettier': 'error',
      // Avoid conflicts between ESLint and Prettier
      'arrow-body-style': ['error', 'as-needed'],
      'prefer-arrow-callback': ['error', { allowNamedFunctions: false }],
    },
  },
];

export default eslintConfig;
