# Feedback API Documentation

## Overview

The Feedback API provides a comprehensive system for creating, managing, and responding to feedback within organizations. It supports different types of feedback (private, task, department, organization) with role-based authorization and user assignment capabilities.

## Database Schema

### Tables

#### `feedback_types`
Master data table containing feedback type definitions.

| Column | Type | Description |
|--------|------|-------------|
| id | SERIAL | Primary key |
| name | TEXT | Unique identifier (private, task, department, organization) |
| display_name | TEXT | Human-readable name |
| description | TEXT | Optional description |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

#### `feedbacks`
Main feedback records.

| Column | Type | Description |
|--------|------|-------------|
| id | SERIAL | Primary key |
| feedback_type_id | INTEGER | Foreign key to feedback_types |
| create_from_id | INTEGER | Foreign key to users (creator) |
| situation | TEXT | Situation description |
| behavior | TEXT | Behavior description |
| impact | TEXT | Impact description |
| actionable | TEXT | Actionable feedback |
| appreciation | TEXT | Appreciation message |
| growth_token | INTEGER | Growth token value |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

#### `feedback_users`
User assignments and responses to feedback.

| Column | Type | Description |
|--------|------|-------------|
| id | SERIAL | Primary key |
| feedback_id | INTEGER | Foreign key to feedbacks |
| user_id | INTEGER | Foreign key to users |
| is_accept | BOOLEAN | Whether user accepted feedback |
| is_discard | BOOLEAN | Whether user discarded feedback (default: false) |
| reflection | TEXT | User's reflection on feedback |
| is_share | BOOLEAN | Whether user shared feedback (default: false) |
| created_at | TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Relationships

- `feedbacks.feedback_type_id` → `feedback_types.id`
- `feedbacks.create_from_id` → `users.id`
- `feedback_users.feedback_id` → `feedbacks.id` (CASCADE DELETE)
- `feedback_users.user_id` → `users.id`

## API Endpoints

### 1. Feedback Types

#### GET `/api/v1/feedback-type`

Retrieve all available feedback types (master data).

**Authentication:** Required

**Response:**
```json
{
  "message": "Feedback types retrieved successfully",
  "feedbackTypes": [
    {
      "id": 1,
      "name": "private",
      "displayName": "Private Feedback",
      "description": "Private feedback between users within organization"
    },
    {
      "id": 2,
      "name": "task",
      "displayName": "Task Feedback", 
      "description": "Feedback related to specific tasks"
    },
    {
      "id": 3,
      "name": "department",
      "displayName": "Department Feedback",
      "description": "Feedback for all members in a department"
    },
    {
      "id": 4,
      "name": "organization",
      "displayName": "Organization Feedback",
      "description": "Feedback for all members in an organization"
    }
  ]
}
```

### 2. Feedback Management

#### GET `/api/v1/feedback`

Retrieve feedback records with multiple view modes.

**Authentication:** Required

**Query Parameters:**

##### Single Feedback
- `id` (number): Get specific feedback by ID

**Example:** `GET /api/v1/feedback?id=123`

##### User's Own Feedback
- `view=my`: Get user's created, assigned, and shared feedback

**Example:** `GET /api/v1/feedback?view=my`

**Response:**
```json
{
  "created": [...],    // Feedback created by user
  "assigned": [...],   // Feedback assigned to user
  "shared": [...]      // Feedback shared with user
}
```

##### Admin/Leader View
- `view=all&organizationId=123`: Get all feedback for organization (admin only)
- `view=all&departmentId=123`: Get all feedback for department (admin/leader only)

**Example:** `GET /api/v1/feedback?view=all&organizationId=1`

#### POST `/api/v1/feedback`

Create new feedback with user assignments.

**Authentication:** Required

**Request Body:**
```json
{
  "feedbackTypeId": 1,
  "situation": "During the team meeting...",
  "behavior": "You demonstrated excellent leadership...",
  "impact": "This helped the team stay focused...",
  "actionable": "Continue this approach in future meetings",
  "appreciation": "Thank you for your leadership",
  "growthToken": 5,
  "userIds": [2, 3, 4],
  "organizationId": 1,    // Required for private/organization feedback
  "departmentId": 2,      // Required for department feedback
  "taskId": 3             // Required for task feedback
}
```

**Response:**
```json
{
  "message": "Feedback created successfully",
  "feedback": {
    "id": 1,
    "feedbackTypeId": 1,
    "createFromId": 1,
    "situation": "During the team meeting...",
    // ... other fields
  },
  "assignedUsers": 3
}
```

#### PUT `/api/v1/feedback`

Update existing feedback (creator only).

**Authentication:** Required

**Request Body:**
```json
{
  "id": 1,
  "situation": "Updated situation...",
  "behavior": "Updated behavior...",
  "impact": "Updated impact...",
  "actionable": "Updated actionable feedback",
  "appreciation": "Updated appreciation",
  "growthToken": 7,
  "userIds": [2, 3, 5],   // Optional: update user assignments
  "organizationId": 1,
  "departmentId": 2,
  "taskId": 3
}
```

#### DELETE `/api/v1/feedback?id=123`

Delete feedback (creator or owner only).

**Authentication:** Required

**Query Parameters:**
- `id` (number): Feedback ID to delete

### 3. Feedback User Management

#### GET `/api/v1/feedback-user`

Get users assigned to feedback.

**Authentication:** Required

**Query Parameters:**

##### All Users for Feedback
- `feedbackId` (number): Get all users assigned to feedback

**Example:** `GET /api/v1/feedback-user?feedbackId=123`

##### Specific User Details
- `feedbackId` (number): Feedback ID
- `userId` (number): User ID

**Example:** `GET /api/v1/feedback-user?feedbackId=123&userId=456`

#### POST `/api/v1/feedback-user`

Add users to existing feedback (creator only).

**Authentication:** Required

**Request Body:**
```json
{
  "feedbackId": 1,
  "userIds": [5, 6, 7]
}
```

#### PUT `/api/v1/feedback-user`

Update user's feedback status (user can only update their own status).

**Authentication:** Required

**Request Body:**
```json
{
  "feedbackId": 1,
  "userId": 2,
  "isAccept": true,
  "isDiscard": false,
  "reflection": "Thank you for this valuable feedback. I will work on improving my communication skills.",
  "isShare": true
}
```

#### DELETE `/api/v1/feedback-user?feedbackId=123&userId=456`

Remove user from feedback (creator only).

**Authentication:** Required

**Query Parameters:**
- `feedbackId` (number): Feedback ID
- `userId` (number): User ID to remove

## Authorization Rules

### Feedback Creation Permissions

| Feedback Type | Who Can Create | Target Users |
|---------------|----------------|--------------|
| **Private** | Owner, Admin, Member Leader | Organization users (Owner/Admin), Department users (Leader) |
| **Task** | Owner, Admin, Member Leader | Task assigned members (non-leaders only) |
| **Department** | Owner, Admin | Department members |
| **Organization** | Owner only | Organization members |

### Feedback Management Permissions

- **Update**: Only the feedback creator can update feedback content and user assignments
- **Delete**: Only the feedback creator or organization owner can delete feedback
- **View**: Creator, assigned users, owners, and admins can view feedback

### User Status Management

- **Accept/Discard/Share/Reflection**: Users can only update their own feedback status
- **Add/Remove Users**: Only the feedback creator can manage user assignments

## Error Responses

### Common Error Codes

- `400 Bad Request`: Invalid request parameters or missing required fields
- `401 Unauthorized`: Authentication required or invalid token
- `403 Forbidden`: Insufficient permissions for the requested action
- `404 Not Found`: Requested resource not found
- `500 Internal Server Error`: Server error

### Example Error Response
```json
{
  "error": "You do not have permission to create this type of feedback"
}
```

## Usage Examples

### Creating Private Feedback
```javascript
// Owner/Admin creating private feedback for organization users
const response = await fetch('/api/v1/feedback', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    feedbackTypeId: 1, // private
    situation: "During the client presentation...",
    behavior: "You handled the difficult questions very well",
    impact: "The client was impressed and we secured the deal",
    actionable: "Continue developing your presentation skills",
    appreciation: "Great job on closing this important deal!",
    growthToken: 8,
    userIds: [5, 6],
    organizationId: 1
  })
});
```

### Accepting Feedback
```javascript
// User accepting feedback and adding reflection
const response = await fetch('/api/v1/feedback-user', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    feedbackId: 1,
    userId: 5, // Must be the authenticated user's ID
    isAccept: true,
    reflection: "Thank you for this feedback. I will continue to work on my presentation skills and look for more opportunities to practice.",
    isShare: true
  })
});
```

### Getting User's Feedback
```javascript
// Get all feedback for the authenticated user
const response = await fetch('/api/v1/feedback?view=my', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});

const data = await response.json();
// data.created - feedback created by user
// data.assigned - feedback assigned to user  
// data.shared - feedback shared with user
```
