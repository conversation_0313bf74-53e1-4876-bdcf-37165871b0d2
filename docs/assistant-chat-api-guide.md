# Assistant Chat API Guide

This guide provides documentation for the AI Assistant Chat System API endpoints.

## Overview

The Assistant Chat System consists of three main models:
- **AssistantMessageType**: Defines message types (System, User, Assistant)
- **AssistantChatUser**: Links users to AI assistant chat sessions
- **AssistantMessage**: Stores individual messages in AI assistant conversations

## Authentication

All endpoints require authentication via Bear<PERSON> token:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## API Endpoints

### AssistantChatUser APIs

#### 1. GET All AssistantChatUsers
**Endpoint**: `GET /api/v1/assistant-chat-user`

**Query Parameters**:
- `userId` (optional): Filter by user ID
- `chatId` (optional): Filter by chat ID
- `isActive` (optional): Filter by active status (true/false)

**Example Request**:
```bash
curl -X GET "http://localhost:3000/api/v1/assistant-chat-user?userId=123&chatId=456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response**:
```json
{
  "assistantChatUsers": [
    {
      "id": 1,
      "name": "AI Assistant Session",
      "chatId": 456,
      "userId": 123,
      "isActive": true,
      "createdAt": "2024-01-01T12:00:00.000Z",
      "chat": {
        "id": 456,
        "name": "AI Chat",
        "chatType": "PRIVATE",
        "isBot": true
      },
      "user": {
        "id": 123,
        "firstName": "John",
        "lastName": "Doe",
        "userRole": {
          "isOwner": false,
          "isAdmin": false,
          "isMember": true
        }
      }
    }
  ],
  "userRole": {
    "isOwner": false,
    "isAdmin": false,
    "isMember": true
  }
}
```

#### 2. GET AssistantChatUser by ID
**Endpoint**: `GET /api/v1/assistant-chat-user?id=123`

**Example Request**:
```bash
curl -X GET "http://localhost:3000/api/v1/assistant-chat-user?id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 3. DELETE AssistantChatUser
**Endpoint**: `DELETE /api/v1/assistant-chat-user`

**Request Body**:
```json
{
  "id": 123
}
```

**Example Request**:
```bash
curl -X DELETE "http://localhost:3000/api/v1/assistant-chat-user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"id": 123}'
```

### AssistantMessageType APIs

#### 4. GET All AssistantMessageTypes
**Endpoint**: `GET /api/v1/assistant-message-type`

**Example Request**:
```bash
curl -X GET "http://localhost:3000/api/v1/assistant-message-type" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response**:
```json
{
  "assistantMessageTypes": [
    {
      "id": 1,
      "name": "System",
      "description": "System/instruction messages"
    },
    {
      "id": 2,
      "name": "User",
      "description": "Messages sent by users"
    },
    {
      "id": 3,
      "name": "Assistant",
      "description": "Response messages from AI assistant"
    }
  ],
  "userRole": {
    "isOwner": false,
    "isAdmin": false,
    "isMember": true
  }
}
```

### AssistantMessage APIs

#### 5. GET All AssistantMessages
**Endpoint**: `GET /api/v1/assistant-message`

**Query Parameters**:
- `assistantChatUserId` (required): Filter by AssistantChatUser ID
- `page` (optional): Page number (default: 1)
- `limit` (optional): Messages per page (default: 50, max: 100)
- `messageTypeId` (optional): Filter by message type ID

**Example Request**:
```bash
curl -X GET "http://localhost:3000/api/v1/assistant-message?assistantChatUserId=123&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response**:
```json
{
  "assistantMessages": [
    {
      "id": 1,
      "content": "Hello, how can I help you today?",
      "metadata": null,
      "completionTokens": 10,
      "promptTokens": 5,
      "totalTokens": 15,
      "createdAt": "2024-01-01T12:00:00.000Z",
      "assistantChatUser": {
        "id": 123,
        "name": "AI Session",
        "userId": 456,
        "chatId": 789
      },
      "assistantMessageType": {
        "id": 3,
        "name": "Assistant",
        "description": "Response messages from AI assistant"
      }
    }
  ],
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 20,
    "totalPages": 3
  },
  "userRole": {
    "isOwner": false,
    "isAdmin": false,
    "isMember": true
  }
}
```

#### 6. CREATE AssistantMessage
**Endpoint**: `POST /api/v1/assistant-message`

**Request Body**:
```json
{
  "assistantChatUserId": 123,
  "assistantMessageTypeId": 2,
  "content": "What is the weather like today?",
  "metadata": {
    "model": "gpt-4",
    "temperature": 0.7
  },
  "completionTokens": 15,
  "promptTokens": 8,
  "totalTokens": 23
}
```

**Example Request**:
```bash
curl -X POST "http://localhost:3000/api/v1/assistant-message" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "assistantChatUserId": 123,
    "assistantMessageTypeId": 2,
    "content": "What is the weather like today?"
  }'
```

#### 7. DELETE AssistantMessage
**Endpoint**: `DELETE /api/v1/assistant-message`

**Request Body**:
```json
{
  "id": 123
}
```

**Example Request**:
```bash
curl -X DELETE "http://localhost:3000/api/v1/assistant-message" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"id": 123}'
```

## Real-time Updates

All CREATE, UPDATE, and DELETE operations emit socket notifications for real-time updates:

```javascript
// Listen for assistant-related notifications
socket.on('send_notification', (data) => {
  if (data.service === 'assistant') {
    console.log(`Assistant record ${data.id} was ${data.action}`);
    // Refresh your data or update UI accordingly
  }
});
```

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

- `400 Bad Request`: Invalid input or missing required fields
- `401 Unauthorized`: Missing or invalid authentication token
- `403 Forbidden`: User lacks permission to access the resource
- `404 Not Found`: Requested resource does not exist
- `500 Internal Server Error`: Server-side error

**Example Error Response**:
```json
{
  "error": "AssistantChatUser ID is required"
}
```

## User Role Indicators

All successful responses include user role information for UI distinction:

```json
{
  "userRole": {
    "isOwner": false,
    "isAdmin": true,
    "isMember": true
  }
}
```

## Permissions

- **Regular Users**: Can only access their own AssistantChatUser and AssistantMessage records
- **Admins**: Can access all records within their organization
- **Owners**: Can access all records across the system
