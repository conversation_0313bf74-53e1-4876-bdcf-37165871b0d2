# Feedback API Quick Reference

## 🚀 Quick Start

### 1. Get Feedback Types
```bash
curl -X GET "http://localhost:3000/api/v1/feedback-type" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Create Private Feedback
```bash
curl -X POST "http://localhost:3000/api/v1/feedback" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "feedbackTypeId": 1,
    "situation": "During the team meeting",
    "behavior": "You showed great leadership",
    "impact": "Team stayed focused and productive",
    "actionable": "Continue this approach",
    "appreciation": "Thank you for your leadership",
    "growthToken": 5,
    "userIds": [2, 3],
    "organizationId": 1
  }'
```

### 3. Get My Feedback
```bash
curl -X GET "http://localhost:3000/api/v1/feedback?view=my" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. Accept Feedback
```bash
curl -X PUT "http://localhost:3000/api/v1/feedback-user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "feedbackId": 1,
    "userId": 2,
    "isAccept": true,
    "reflection": "Thank you for this valuable feedback",
    "isShare": true
  }'
```

## 📋 Endpoint Summary

| Method | Endpoint | Purpose |
|--------|----------|---------|
| `GET` | `/api/v1/feedback-type` | Get all feedback types |
| `GET` | `/api/v1/feedback?id=123` | Get single feedback |
| `GET` | `/api/v1/feedback?view=my` | Get user's feedback |
| `GET` | `/api/v1/feedback?view=all&organizationId=1` | Admin view |
| `POST` | `/api/v1/feedback` | Create feedback |
| `PUT` | `/api/v1/feedback` | Update feedback |
| `DELETE` | `/api/v1/feedback?id=123` | Delete feedback |
| `GET` | `/api/v1/feedback-user?feedbackId=123` | Get feedback users |
| `POST` | `/api/v1/feedback-user` | Add users to feedback |
| `PUT` | `/api/v1/feedback-user` | Update user status |
| `DELETE` | `/api/v1/feedback-user?feedbackId=123&userId=456` | Remove user |

## 🔐 Permission Quick Check

### Who Can Create What?

| Role | Private | Task | Department | Organization |
|------|---------|------|------------|--------------|
| Owner | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | ❌ |
| Member Leader | ✅ | ✅ | ❌ | ❌ |
| Member | ❌ | ❌ | ❌ | ❌ |

### Target Users by Feedback Type

- **Private**: Organization users (Owner/Admin) or Department users (Leader)
- **Task**: Task assigned members (non-leaders only)
- **Department**: All department members
- **Organization**: All organization members

## 📊 Response Formats

### Feedback Object
```json
{
  "id": 1,
  "feedbackTypeId": 1,
  "createFromId": 1,
  "situation": "During the team meeting...",
  "behavior": "You demonstrated excellent leadership...",
  "impact": "This helped the team stay focused...",
  "actionable": "Continue this approach...",
  "appreciation": "Thank you for your leadership",
  "growthToken": 5,
  "createdAt": "2025-07-31T08:25:29.000Z",
  "updatedAt": "2025-07-31T08:25:29.000Z",
  "feedbackType": {
    "id": 1,
    "name": "private",
    "displayName": "Private Feedback"
  },
  "createFrom": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>"
  },
  "feedbackUsers": [
    {
      "id": 1,
      "feedbackId": 1,
      "userId": 2,
      "isAccept": true,
      "isDiscard": false,
      "reflection": "Thank you for this feedback",
      "isShare": true,
      "user": {
        "id": 2,
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>"
      }
    }
  ]
}
```

### Error Response
```json
{
  "error": "You do not have permission to create this type of feedback"
}
```

## 🛠️ Common Use Cases

### 1. Manager Giving Task Feedback
```javascript
// Manager (member leader) giving feedback to task members
const taskFeedback = {
  feedbackTypeId: 2, // task
  situation: "During the sprint planning",
  behavior: "You asked thoughtful questions about requirements",
  impact: "This helped clarify the scope and prevent issues later",
  actionable: "Continue being proactive in asking clarifying questions",
  growthToken: 6,
  userIds: [5, 6], // Task assigned members
  taskId: 10
};
```

### 2. Admin Creating Department Feedback
```javascript
// Admin creating feedback for entire department
const deptFeedback = {
  feedbackTypeId: 3, // department
  situation: "During the quarterly review",
  behavior: "The team exceeded all performance targets",
  impact: "This contributed significantly to company goals",
  appreciation: "Excellent work everyone!",
  growthToken: 8,
  userIds: [2, 3, 4, 5], // All department members
  departmentId: 2
};
```

### 3. User Responding to Feedback
```javascript
// User accepting feedback and adding reflection
const response = {
  feedbackId: 1,
  userId: 2, // Must be authenticated user
  isAccept: true,
  reflection: "I appreciate this feedback and will work on improving my communication skills in future meetings.",
  isShare: true // Allow others to see this reflection
};
```

### 4. Getting Feedback for Dashboard
```javascript
// Get user's feedback for dashboard display
const myFeedback = await fetch('/api/v1/feedback?view=my');
const data = await myFeedback.json();

// data.created - feedback I created
// data.assigned - feedback assigned to me
// data.shared - feedback shared with me
```

## ⚠️ Common Errors

### 400 Bad Request
- Missing required fields (`feedbackTypeId`, `userIds`)
- Invalid user assignments (users not assignable)
- Invalid ID formats

### 403 Forbidden
- Insufficient permissions for feedback type
- Trying to update/delete others' feedback
- Trying to update another user's status

### 404 Not Found
- Feedback not found
- User not assigned to feedback
- Invalid feedback type

## 🔧 Development Tips

### 1. Permission Testing
```javascript
// Test permission before UI actions
const canCreate = await checkPermission(userId, 'private');
if (canCreate) {
  // Show create feedback button
}
```

### 2. Batch Operations
```javascript
// Add multiple users efficiently
const addUsers = {
  feedbackId: 1,
  userIds: [5, 6, 7, 8] // Batch add
};
```

### 3. Error Handling
```javascript
try {
  const response = await createFeedback(data);
  if (!response.ok) {
    const error = await response.json();
    showError(error.error);
  }
} catch (error) {
  showError('Network error occurred');
}
```

### 4. Real-time Updates (Future)
```javascript
// Listen for feedback updates
socket.on('feedback_notification', (data) => {
  if (data.action === 'CREATE') {
    refreshFeedbackList();
  }
});
```

## 📝 Database Queries

### Get User's Feedback Count
```sql
SELECT 
  COUNT(*) as total_received,
  COUNT(CASE WHEN is_accept = true THEN 1 END) as accepted,
  COUNT(CASE WHEN is_discard = true THEN 1 END) as discarded
FROM feedback_users 
WHERE user_id = $1;
```

### Get Feedback by Type
```sql
SELECT f.*, ft.name as feedback_type_name
FROM feedbacks f
JOIN feedback_types ft ON f.feedback_type_id = ft.id
WHERE ft.name = $1
ORDER BY f.created_at DESC;
```

### Get Department Feedback Stats
```sql
SELECT 
  ft.display_name,
  COUNT(*) as feedback_count,
  AVG(f.growth_token) as avg_growth_token
FROM feedbacks f
JOIN feedback_types ft ON f.feedback_type_id = ft.id
WHERE ft.name = 'department'
GROUP BY ft.id, ft.display_name;
```

## 🎯 Testing Checklist

- [ ] Can create feedback with proper permissions
- [ ] Cannot create feedback without permissions
- [ ] Can assign users based on feedback type
- [ ] Cannot assign invalid users
- [ ] Can update own feedback status
- [ ] Cannot update others' feedback status
- [ ] Can delete own feedback
- [ ] Cannot delete others' feedback (unless owner)
- [ ] Proper error messages for all scenarios
- [ ] Database constraints work correctly
