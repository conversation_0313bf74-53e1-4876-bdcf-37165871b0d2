# TextEditor Image Resizing Feature

## Overview

The TextEditor component now includes automatic image resizing functionality to improve performance and user experience by preventing oversized images from being inserted into the editor while maintaining visual quality.

## Features

- **Automatic Image Resizing**: Images are automatically resized to a maximum width of 600px (configurable) while maintaining aspect ratio
- **Smart Resizing**: Only resizes images that are larger than the specified maximum dimensions
- **Quality Control**: Configurable image compression quality (default: 85%)
- **Format Support**: Supports JPEG, PNG, WebP, and GIF formats
- **Loading Indicator**: Shows a loading spinner during the resize process
- **Fallback Handling**: Gracefully falls back to original image if resizing fails
- **Backward Compatibility**: Existing TextEditor usage continues to work without changes

## Usage

### Basic Usage (Automatic Resizing Enabled)

```tsx
import TextEditor from '@/components/TextEditor';
import { createImageUploadFunction } from '@/utils/imageUpload';

function MyComponent() {
  const [content, setContent] = useState('');
  
  const uploadFunction = useMemo(() => {
    return createImageUploadFunction(token); // Now includes automatic resizing
  }, [token]);

  return (
    <TextEditor
      value={content}
      onChange={setContent}
      uploadImage={uploadFunction}
    />
  );
}
```

### Custom Resize Options

```tsx
import TextEditor from '@/components/TextEditor';
import { createImageUploadFunction, type ImageResizeOptions } from '@/utils/imageUpload';

function MyComponent() {
  const [content, setContent] = useState('');
  
  const resizeOptions: ImageResizeOptions = {
    maxWidth: 1200,
    maxHeight: 800,
    quality: 0.9,
    format: 'jpeg'
  };
  
  const uploadFunction = useMemo(() => {
    return createImageUploadFunction(token, resizeOptions);
  }, [token]);

  return (
    <TextEditor
      value={content}
      onChange={setContent}
      uploadImage={uploadFunction}
      imageResizeOptions={resizeOptions}
      enableImageResize={true}
    />
  );
}
```

### Disable Automatic Resizing

```tsx
import TextEditor from '@/components/TextEditor';
import { createLegacyImageUploadFunction } from '@/utils/imageUpload';

function MyComponent() {
  const [content, setContent] = useState('');
  
  const uploadFunction = useMemo(() => {
    return createLegacyImageUploadFunction(token); // No resizing
  }, [token]);

  return (
    <TextEditor
      value={content}
      onChange={setContent}
      uploadImage={uploadFunction}
      enableImageResize={false}
    />
  );
}
```

## Configuration Options

### ImageResizeOptions

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `maxWidth` | number | 600 | Maximum width in pixels |
| `maxHeight` | number | 450 | Maximum height in pixels |
| `quality` | number | 0.85 | Image quality (0.1 - 1.0) |
| `format` | 'jpeg' \| 'png' \| 'webp' | 'jpeg' | Output format |

### TextEditor Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `imageResizeOptions` | ImageResizeOptions | `{ maxWidth: 600, quality: 0.85 }` | Resize configuration |
| `enableImageResize` | boolean | true | Enable/disable automatic resizing |

## API Reference

### Utility Functions

#### `resizeImage(file: File, options?: ImageResizeOptions): Promise<File>`

Resizes an image file while maintaining aspect ratio.

```tsx
import { resizeImage } from '@/utils/imageUpload';

const resizedFile = await resizeImage(originalFile, {
  maxWidth: 600,
  maxHeight: 450,
  quality: 0.85,
  format: 'jpeg'
});
```

#### `isSupportedImageFormat(file: File): boolean`

Checks if a file is a supported image format.

```tsx
import { isSupportedImageFormat } from '@/utils/imageUpload';

if (isSupportedImageFormat(file)) {
  // Process the image
}
```

#### `resizeAndUploadImage(file: File, token: string, options?: ImageResizeOptions): Promise<string>`

Resizes and uploads an image to the server.

```tsx
import { resizeAndUploadImage } from '@/utils/imageUpload';

const imageUrl = await resizeAndUploadImage(file, token, {
  maxWidth: 600,
  quality: 0.85
});
```

#### `createImageUploadFunction(token: string, options?: ImageResizeOptions)`

Creates an upload function with automatic resizing for TextEditor.

```tsx
import { createImageUploadFunction } from '@/utils/imageUpload';

const uploadFunction = createImageUploadFunction(token, {
  maxWidth: 1200,
  quality: 0.9
});
```

#### `createLegacyImageUploadFunction(token: string)`

Creates an upload function without resizing (legacy behavior).

```tsx
import { createLegacyImageUploadFunction } from '@/utils/imageUpload';

const uploadFunction = createLegacyImageUploadFunction(token);
```

## Implementation Details

### Resize Algorithm

1. **Format Check**: Validates that the image is in a supported format
2. **Size Check**: Only resizes images larger than the specified maximum dimensions
3. **Aspect Ratio**: Maintains original aspect ratio by calculating the appropriate scale factor
4. **Canvas Rendering**: Uses HTML5 Canvas API to resize the image
5. **Quality Control**: Applies specified compression quality during blob conversion
6. **File Creation**: Creates a new File object with the resized image data

### Performance Considerations

- **Client-side Processing**: Resizing happens in the browser to reduce server load
- **Memory Efficient**: Uses canvas-based resizing which is optimized for performance
- **Progressive Enhancement**: Falls back gracefully if resizing fails
- **Lazy Loading**: Only processes images when they're actually uploaded

### Browser Compatibility

- **Modern Browsers**: Full support in all modern browsers
- **Canvas API**: Requires HTML5 Canvas support
- **File API**: Requires File API support
- **Blob API**: Requires Blob constructor support

## Migration Guide

### Existing Implementations

No changes required! Existing TextEditor usage will automatically benefit from the new resizing functionality:

```tsx
// This code continues to work unchanged
<TextEditor
  value={content}
  onChange={setContent}
  uploadImage={createImageUploadFunction(token)}
/>
```

### Opt-out of Resizing

If you need to disable resizing for specific use cases:

```tsx
<TextEditor
  value={content}
  onChange={setContent}
  uploadImage={createLegacyImageUploadFunction(token)}
  enableImageResize={false}
/>
```

## Troubleshooting

### Common Issues

1. **Images not resizing**: Check that `enableImageResize` is true and `imageResizeOptions` are provided
2. **Poor image quality**: Increase the `quality` value in resize options
3. **Images too small**: Adjust `maxWidth` and `maxHeight` values
4. **Unsupported format error**: Ensure the image is in JPEG, PNG, WebP, or GIF format

### Debug Mode

Enable console logging to debug resize operations:

```tsx
// The resize function logs warnings for failed operations
// Check browser console for detailed error messages
```

## Best Practices

1. **Quality vs Size**: Balance image quality with file size by adjusting the quality setting
2. **Responsive Design**: Consider different max widths for different screen sizes
3. **Format Selection**: Use JPEG for photos, PNG for graphics with transparency
4. **Error Handling**: Always provide fallback behavior for failed uploads
5. **User Feedback**: The loading indicator keeps users informed during processing

## Examples

See `src/components/TextEditor/TextEditorExample.tsx` for a complete interactive example demonstrating all features.
