# Feedback API Implementation Guide

## Overview

This guide provides technical details for developers working with the Feedback API implementation, including database setup, permission system, and integration patterns.

## Database Setup

### Migration Applied
```sql
-- Migration: 20250731082529_add_feedback_system

-- Feedback Types (Master Data)
CREATE TABLE "feedback_types" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "feedback_types_pkey" PRIMARY KEY ("id")
);

-- Main Feedback Records
CREATE TABLE "feedbacks" (
    "id" SERIAL NOT NULL,
    "feedback_type_id" INTEGER NOT NULL,
    "create_from_id" INTEGER NOT NULL,
    "situation" TEXT,
    "behavior" TEXT,
    "impact" TEXT,
    "actionable" TEXT,
    "appreciation" TEXT,
    "growth_token" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "feedbacks_pkey" PRIMARY KEY ("id")
);

-- User Assignments and Responses
CREATE TABLE "feedback_users" (
    "id" SERIAL NOT NULL,
    "feedback_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "is_accept" BOOLEAN,
    "is_discard" BOOLEAN NOT NULL DEFAULT false,
    "reflection" TEXT,
    "is_share" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    CONSTRAINT "feedback_users_pkey" PRIMARY KEY ("id")
);

-- Indexes and Constraints
CREATE UNIQUE INDEX "feedback_types_name_key" ON "feedback_types"("name");
CREATE UNIQUE INDEX "feedback_users_feedback_id_user_id_key" ON "feedback_users"("feedback_id", "user_id");
```

### Seed Data
```typescript
// Feedback types added to prisma/seed.ts
const feedbackTypes = [
  {
    id: 1,
    name: 'private',
    displayName: 'Private Feedback',
    description: 'Private feedback between users within organization',
  },
  {
    id: 2,
    name: 'task',
    displayName: 'Task Feedback',
    description: 'Feedback related to specific tasks',
  },
  {
    id: 3,
    name: 'department',
    displayName: 'Department Feedback',
    description: 'Feedback for all members in a department',
  },
  {
    id: 4,
    name: 'organization',
    displayName: 'Organization Feedback',
    description: 'Feedback for all members in an organization',
  },
];
```

## Permission System

### Core Permission Functions

Located in `src/lib/permissions.ts`:

#### `canCreateFeedback(userId, feedbackType, targetId?)`
Determines if a user can create feedback of a specific type.

```typescript
// Usage example
const canCreate = await canCreateFeedback(userId, 'private');
if (!canCreate) {
  return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
}
```

#### `canDeleteFeedback(userId, feedbackId)`
Checks if a user can delete specific feedback (creator or owner only).

#### `getAssignableFeedbackUsers(userId, feedbackType, organizationId?, departmentId?, taskId?)`
Returns array of user IDs that can be assigned to feedback based on type and permissions.

### Permission Matrix

| User Role | Private | Task | Department | Organization |
|-----------|---------|------|------------|--------------|
| **Owner** | ✅ Org users | ✅ Task members | ✅ Dept members | ✅ Org members |
| **Admin** | ✅ Org users | ✅ Task members | ✅ Dept members | ❌ |
| **Member Leader** | ✅ Dept users | ✅ Task members | ❌ | ❌ |
| **Member** | ❌ | ❌ | ❌ | ❌ |

## API Implementation Details

### Authentication Pattern
All endpoints use the `authenticateUser` helper:

```typescript
import { authenticateUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  const auth = await authenticateUser(request);
  if ('error' in auth) {
    return NextResponse.json({ error: auth.error }, { status: auth.status });
  }
  
  // auth.userId, auth.isOwner, auth.isAdmin, auth.isMember available
}
```

### Transaction Usage
Complex operations use Prisma transactions:

```typescript
const result = await prisma.$transaction(async (tx) => {
  // Create feedback
  const feedback = await tx.feedback.create({
    data: feedbackData,
  });

  // Create user assignments
  const feedbackUsers = await Promise.all(
    userIds.map(userId =>
      tx.feedbackUser.create({
        data: { feedbackId: feedback.id, userId },
      })
    )
  );

  return { feedback, feedbackUsers };
});
```

### Error Handling Pattern
Consistent error responses across all endpoints:

```typescript
try {
  // API logic
} catch (error) {
  console.error('Error description:', error);
  const errorMessage = error instanceof Error ? error.message : 'Generic error message';
  return NextResponse.json({ error: errorMessage }, { status: 500 });
}
```

## File Structure

```
src/
├── app/api/v1/
│   ├── feedback/
│   │   └── route.ts              # Main CRUD operations
│   ├── feedback-type/
│   │   └── route.ts              # Master data endpoint
│   └── feedback-user/
│       └── route.ts              # User assignment management
├── lib/
│   └── permissions.ts            # Enhanced with feedback permissions
└── generated/prisma/             # Auto-generated Prisma client

prisma/
├── schema.prisma                 # Updated with feedback models
├── seed.ts                       # Updated with feedback types
└── migrations/
    └── 20250731082529_add_feedback_system/
        └── migration.sql         # Database migration
```

## Integration Patterns

### Frontend Integration Example

```typescript
// React hook for feedback management
import { useState, useEffect } from 'react';

export const useFeedback = () => {
  const [feedbackTypes, setFeedbackTypes] = useState([]);
  const [userFeedback, setUserFeedback] = useState(null);

  const fetchFeedbackTypes = async () => {
    const response = await fetch('/api/v1/feedback-type', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await response.json();
    setFeedbackTypes(data.feedbackTypes);
  };

  const fetchUserFeedback = async () => {
    const response = await fetch('/api/v1/feedback?view=my', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await response.json();
    setUserFeedback(data);
  };

  const createFeedback = async (feedbackData) => {
    const response = await fetch('/api/v1/feedback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(feedbackData)
    });
    
    if (response.ok) {
      await fetchUserFeedback(); // Refresh data
    }
    
    return response;
  };

  return {
    feedbackTypes,
    userFeedback,
    fetchFeedbackTypes,
    fetchUserFeedback,
    createFeedback
  };
};
```

### Socket Integration (Future Enhancement)
The API is ready for real-time updates. Add socket emissions:

```typescript
// In feedback creation/update/delete operations
import socketServerService from '@/lib/socket-server';

// After successful feedback operation
socketServerService.emitNotification('feedback', feedbackId, 'CREATE');
socketServerService.emitNotification('feedback', feedbackId, 'UPDATE');
socketServerService.emitNotification('feedback', feedbackId, 'DELETE');
```

## Testing

### Test Structure
Tests follow the existing pattern in the codebase:

```typescript
// Mock setup
jest.mock('@/lib/prisma');
jest.mock('@/lib/jwt');
jest.mock('@/lib/permissions');

// Test structure
describe('/api/v1/feedback', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Setup common mocks
  });

  describe('GET - Get feedback', () => {
    it('should get single feedback by ID', async () => {
      // Test implementation
    });
  });
});
```

### Running Tests
```bash
# Install test dependencies first
npm install --save-dev jest @types/jest ts-jest jest-environment-node

# Run tests
npm test
npm run test:watch
npm run test:coverage
```

## Performance Considerations

### Database Indexes
- `feedback_types.name` - Unique index for fast type lookups
- `feedback_users(feedback_id, user_id)` - Composite unique index for assignments
- Consider adding indexes on `feedbacks.create_from_id` and `feedbacks.feedback_type_id` for large datasets

### Query Optimization
- Use `select` clauses to limit returned fields
- Implement pagination for large result sets
- Consider caching feedback types (master data)

### Scalability
- Feedback creation uses transactions to ensure data consistency
- User assignment operations are batched
- Permission checks are optimized to minimize database queries

## Security Considerations

### Input Validation
- All user inputs are validated before database operations
- User ID arrays are checked against assignable users
- Feedback type IDs are validated against existing types

### Authorization
- Multi-layer permission checks (role-based + resource-based)
- Users can only update their own feedback status
- Creators have full control over their feedback
- Owners have delete privileges across all feedback

### Data Privacy
- Users only see feedback they created, are assigned to, or have admin privileges for
- Reflection and status data is private to the assigned user
- Sharing is controlled by the user's `isShare` flag

## Monitoring and Logging

### Error Logging
All endpoints include comprehensive error logging:

```typescript
console.error('Error creating feedback:', error);
```

### Metrics to Track
- Feedback creation rates by type
- User engagement (accept/discard rates)
- Permission denial rates
- API response times

## Future Enhancements

### Potential Features
1. **Feedback Templates**: Pre-defined feedback structures
2. **Feedback Analytics**: Dashboards and reporting
3. **Notification System**: Email/push notifications for new feedback
4. **Feedback Workflows**: Approval processes for sensitive feedback
5. **Bulk Operations**: Mass feedback creation/assignment
6. **Feedback Categories**: Sub-categorization within types
7. **Anonymous Feedback**: Option for anonymous feedback submission

### API Versioning
The current implementation uses `/api/v1/` prefix, allowing for future API versions without breaking existing integrations.
