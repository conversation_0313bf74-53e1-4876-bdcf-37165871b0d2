# Read Message Feature Testing Guide

This guide provides step-by-step instructions for manually testing the read message functionality.

## Prerequisites

1. Ensure the application is running with the latest code changes
2. Have at least two user accounts for testing
3. Access to the chat interface
4. Browser developer tools for monitoring network requests

## Test Scenarios

### 1. Basic Read Status Functionality

#### Test 1.1: Message Read Indicators
**Objective**: Verify that messages show correct read status indicators

**Steps**:
1. <PERSON><PERSON> as User A
2. Navigate to a chat with User B
3. Send a message to User B
4. Verify the message shows "delivered" status (single check mark)
5. <PERSON><PERSON> as User B in another browser/tab
6. Open the same chat
7. Verify User A's message is automatically marked as read
8. Switch back to User A's browser
9. Verify the message now shows "read" status (double check mark)

**Expected Result**: Messages display correct status indicators and update in real-time

#### Test 1.2: Unread Count Badges
**Objective**: Verify unread count badges appear and update correctly

**Steps**:
1. <PERSON><PERSON> as User A
2. Send multiple messages to User B in different chats
3. <PERSON><PERSON> as User B
4. Verify unread count badges appear on chat rooms in sidebar
5. Open one chat room
6. Verify unread count for that chat becomes 0
7. Verify other chat rooms still show unread counts
8. Navigate between chat rooms
9. Verify unread counts update correctly

**Expected Result**: Unread count badges display accurate numbers and update when messages are read

### 2. API Endpoint Testing

#### Test 2.1: Mark Single Message as Read
**Objective**: Test the mark-read API endpoint for individual messages

**API Call**:
```bash
curl -X POST http://localhost:3000/api/v1/chat-message/mark-read \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"messageId": 123}'
```

**Expected Response**:
```json
{
  "message": "Message marked as read",
  "messageRead": {
    "id": 1,
    "messageId": 123,
    "userId": 1,
    "readAt": "2024-01-01T12:00:00.000Z"
  }
}
```

#### Test 2.2: Mark All Messages in Chat as Read
**Objective**: Test bulk marking messages as read

**API Call**:
```bash
curl -X POST http://localhost:3000/api/v1/chat-message/mark-read \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"chatId": 456}'
```

**Expected Response**:
```json
{
  "message": "Messages marked as read",
  "markedCount": 5,
  "totalMessages": 5
}
```

#### Test 2.3: Get Unread Counts
**Objective**: Test unread count retrieval

**API Call**:
```bash
curl -X GET "http://localhost:3000/api/v1/chat-message/unread-count" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Expected Response**:
```json
{
  "unreadCounts": [
    {
      "chatId": 1,
      "chatName": "Chat 1",
      "chatType": "PRIVATE",
      "unreadCount": 3
    },
    {
      "chatId": 2,
      "chatName": "Chat 2", 
      "chatType": "TASK",
      "unreadCount": 0
    }
  ]
}
```

### 3. Real-time Updates Testing

#### Test 3.1: Socket Events
**Objective**: Verify real-time updates work correctly

**Steps**:
1. Open browser developer tools and monitor WebSocket messages
2. Login as User A and User B in separate browsers
3. Send a message from User A to User B
4. Verify User B receives `new_message` socket event
5. Open the chat as User B
6. Verify User A receives `messages_read` socket event
7. Check that message status updates to "read" in User A's interface

**Expected Result**: Socket events are fired correctly and UI updates in real-time

### 4. Edge Cases Testing

#### Test 4.1: Own Messages
**Objective**: Verify users cannot mark their own messages as read

**Steps**:
1. Login as User A
2. Send a message in a chat
3. Try to mark your own message as read via API
4. Verify the API returns an error

**Expected Result**: API returns error "Cannot mark your own message as read"

#### Test 4.2: Non-participant Access
**Objective**: Verify users cannot mark messages in chats they're not part of

**Steps**:
1. Login as User A
2. Try to mark messages as read in a chat User A is not part of
3. Verify the API returns an error

**Expected Result**: API returns error "You are not a participant in this chat"

#### Test 4.3: Large Message Volume
**Objective**: Test performance with many messages

**Steps**:
1. Create a chat with 100+ messages
2. Mark all messages as read
3. Verify the operation completes in reasonable time
4. Check unread count updates correctly

**Expected Result**: Bulk operations perform efficiently

### 5. User Experience Testing

#### Test 5.1: Auto-mark on Scroll
**Objective**: Verify messages are automatically marked as read when scrolled into view

**Steps**:
1. Login as User B
2. Have User A send multiple messages
3. Open the chat as User B
4. Scroll through messages slowly
5. Verify messages are marked as read as they come into view
6. Check that User A sees read status updates

**Expected Result**: Messages automatically marked as read when viewed

#### Test 5.2: Mobile Responsiveness
**Objective**: Verify read status works on mobile devices

**Steps**:
1. Test the chat interface on mobile devices
2. Verify unread badges are visible and appropriately sized
3. Test touch interactions for marking messages as read
4. Verify real-time updates work on mobile

**Expected Result**: Feature works seamlessly on mobile devices

## Performance Considerations

### Database Query Optimization
- Monitor database query performance for unread count calculations
- Verify indexes are being used effectively
- Test with large datasets (1000+ messages per chat)

### Real-time Update Efficiency
- Monitor WebSocket message frequency
- Verify socket events don't cause excessive network traffic
- Test with multiple concurrent users

## Troubleshooting

### Common Issues
1. **Unread counts not updating**: Check socket connection status
2. **Read status not syncing**: Verify API authentication
3. **Performance issues**: Check database indexes and query optimization
4. **UI not updating**: Verify React state management and re-rendering

### Debug Tools
- Browser developer tools for network monitoring
- Database query logs for performance analysis
- Socket.IO debug logs for real-time event tracking

## Test Completion Checklist

- [ ] All API endpoints return correct responses
- [ ] Unread count badges display and update correctly
- [ ] Message read indicators work properly
- [ ] Real-time updates function across multiple clients
- [ ] Auto-mark on scroll works as expected
- [ ] Error handling works for edge cases
- [ ] Performance is acceptable with large message volumes
- [ ] Mobile interface works correctly
- [ ] Socket events fire and are handled properly
- [ ] Database queries are optimized
