# AI Assistant Chat System Schema

This document describes the three Prisma models designed for the AI assistant chat system: `AssistantMessageType`, `AssistantChatUser`, and `AssistantMessage`.

## Models Overview

### 1. AssistantMessageType
An enumeration model that defines the three types of messages in AI assistant conversations.

**Fixed Values:**
- `System` - System/instruction messages that provide context and guidelines to the AI assistant
- `User` - Messages sent by users as input to the AI assistant  
- `Assistant` - Response messages generated by the AI assistant

**Fields:**
- `id` - Primary key (auto-increment)
- `name` - Unique message type name
- `description` - Optional description of the message type
- `createdAt` / `updatedAt` - Standard timestamps

### 2. AssistantChatUser
A junction/relationship table that links users to their specific AI assistant chat sessions.

**Purpose:**
- Links users to their AI assistant conversations
- Contains foreign key relations to both Chat and User models
- Identifies which AI assistant conversations belong to which user and chat session
- Supports session grouping and conversation management

**Key Fields:**
- `chatId` - Foreign key to Chat model
- `userId` - Foreign key to User model  
- `sessionId` - Optional session identifier for grouping conversations
- `isActive` - Boolean flag for active conversations
- `lastInteractionAt` - Timestamp of last user interaction

**Indexes:**
- Unique constraint on `[chatId, userId]`
- Index on `[userId, isActive]` for efficient user conversation queries
- Index on `[chatId, isActive]` for chat-specific queries
- Index on `sessionId` for session-based queries
- Index on `lastInteractionAt` for chronological sorting

### 3. AssistantMessage
Stores individual messages within AI assistant conversations.

**Purpose:**
- Stores message content and metadata
- Links to AssistantChatUser for conversation context
- References AssistantMessageType for message classification
- Supports message threading and conversation flow

**Key Fields:**
- `assistantChatUserId` - Foreign key to AssistantChatUser
- `messageTypeId` - Foreign key to AssistantMessageType
- `content` - The actual message content
- `metadata` - JSON field for additional message data (tokens, model info, etc.)
- `parentMessageId` - Self-referencing FK for message threading
- `sequenceNumber` - Integer for ordering messages within conversations
- `tokenCount` - Optional token usage tracking
- `processingTimeMs` - Optional performance tracking

**Indexes:**
- Index on `[assistantChatUserId, sequenceNumber]` for ordered conversation retrieval
- Index on `[assistantChatUserId, createdAt]` for chronological queries
- Index on `messageTypeId` for filtering by message type
- Index on `parentMessageId` for threading queries
- Index on `createdAt` for global chronological queries

## Relationships

```
User (1) ←→ (N) AssistantChatUser (N) ←→ (1) Chat
                        ↓ (1)
                        ↓
                   (N) AssistantMessage (N) ←→ (1) AssistantMessageType
                        ↓ (1)
                        ↓
                   (N) AssistantMessage (self-referencing for threading)
```

## Usage Examples

### Creating a new AI assistant conversation:
```typescript
// 1. Create or get existing chat
const chat = await prisma.chat.create({
  data: {
    name: "AI Assistant Chat",
    chatType: "PRIVATE", // or appropriate type
    // ... other chat fields
  }
});

// 2. Create AssistantChatUser junction
const assistantChatUser = await prisma.assistantChatUser.create({
  data: {
    chatId: chat.id,
    userId: user.id,
    sessionId: generateSessionId(), // optional
  }
});
```

### Adding messages to conversation:
```typescript
// Get message types
const messageTypes = await prisma.assistantMessageType.findMany();
const userType = messageTypes.find(t => t.name === 'User');
const assistantType = messageTypes.find(t => t.name === 'Assistant');

// Add user message
const userMessage = await prisma.assistantMessage.create({
  data: {
    assistantChatUserId: assistantChatUser.id,
    messageTypeId: userType.id,
    content: "Hello, can you help me with...",
    sequenceNumber: 1,
  }
});

// Add assistant response
const assistantMessage = await prisma.assistantMessage.create({
  data: {
    assistantChatUserId: assistantChatUser.id,
    messageTypeId: assistantType.id,
    content: "Of course! I'd be happy to help...",
    parentMessageId: userMessage.id, // threading
    sequenceNumber: 2,
    tokenCount: 150,
    processingTimeMs: 1200,
    metadata: {
      model: "gpt-4",
      temperature: 0.7,
      // ... other metadata
    }
  }
});
```

### Querying conversation history:
```typescript
// Get all messages for a user's conversation
const conversationHistory = await prisma.assistantMessage.findMany({
  where: {
    assistantChatUser: {
      userId: user.id,
      chatId: chat.id,
    }
  },
  include: {
    messageType: true,
    parentMessage: true,
  },
  orderBy: {
    sequenceNumber: 'asc'
  }
});

// Get active conversations for a user
const activeConversations = await prisma.assistantChatUser.findMany({
  where: {
    userId: user.id,
    isActive: true,
  },
  include: {
    chat: true,
    assistantMessages: {
      take: 1,
      orderBy: { createdAt: 'desc' }
    }
  },
  orderBy: {
    lastInteractionAt: 'desc'
  }
});
```

## Migration and Seeding

1. **Generate Migration:**
   ```bash
   npx prisma migrate dev --name add-ai-assistant-chat-models
   ```

2. **Seed Message Types:**
   ```bash
   npx ts-node prisma/seeds/assistant-message-types.ts
   ```

## Best Practices

1. **Always update `lastInteractionAt`** when users interact with conversations
2. **Use `sequenceNumber`** for reliable message ordering within conversations  
3. **Leverage `sessionId`** for grouping related conversations
4. **Store relevant metadata** in the JSON field for analytics and debugging
5. **Use indexes efficiently** - the provided indexes support common query patterns
6. **Consider soft deletes** by using `isActive` flags rather than hard deletes
7. **Implement proper error handling** for foreign key constraints
8. **Monitor token usage** through the `tokenCount` field for cost tracking

## Performance Considerations

- The schema includes strategic indexes for common query patterns
- Use `sequenceNumber` instead of `createdAt` for message ordering within conversations
- Consider pagination for large conversation histories
- The `metadata` JSON field allows flexible data storage without schema changes
- Parent-child message relationships support conversation threading without performance penalties
