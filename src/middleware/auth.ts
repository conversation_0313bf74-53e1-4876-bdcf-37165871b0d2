import { NextRequest, NextResponse } from 'next/server';
import { verifyJwt } from '@/lib/jwt';
import { USER_ROLES, UserRole } from '@/constants/roles';

export interface AuthRequest extends NextRequest {
  user?: {
    userId: number;
    email: string;
    role: UserRole;
  };
}

export async function withAuth(
  req: NextRequest,
  handler: (req: AuthRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Get the authorization header
    const authHeader = req.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized: Missing or invalid token' },
        { status: 401 }
      );
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    // Verify the token
    const payload = verifyJwt(token);

    if (!payload) {
      return NextResponse.json({ error: 'Unauthorized: Invalid token' }, { status: 401 });
    }

    // Add the user to the request
    const authReq = req as AuthRequest;
    authReq.user = {
      userId: payload.userId,
      email: payload.email,
      role: payload.role as UserRole,
    };

    // Call the handler with the authenticated request
    return handler(authReq);
  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 500 });
  }
}
