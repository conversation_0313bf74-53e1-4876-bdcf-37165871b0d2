import { useState, useCallback } from 'react';
import { s3Service, FileType } from '@/services/s3Service';

export interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  url?: string;
  key?: string;
}

export interface UseFileUploadReturn {
  uploads: UploadProgress[];
  isUploading: boolean;
  uploadFile: (file: File, fileType: FileType) => Promise<{
    success: boolean;
    data?: { url: string; key: string; fileName: string; fileType: string; size: number; contentType: string };
    error?: string;
  }>;
  uploadFiles: (files: File[], fileType: FileType) => Promise<void>;
  deleteFile: (key: string) => Promise<{ success: boolean; error?: string }>;
  clearUploads: () => void;
  removeUpload: (fileName: string) => void;
}

export function useFileUpload(): UseFileUploadReturn {
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const uploadFile = useCallback(async (file: File, fileType: FileType) => {
    const uploadId = `${file.name}-${Date.now()}`;
    
    // Add upload to state
    setUploads(prev => [...prev, {
      fileName: file.name,
      progress: 0,
      status: 'uploading',
    }]);

    setIsUploading(true);

    try {
      const result = await s3Service.uploadFile(file, fileType);

      if (result.success && result.data) {
        // Update upload status to completed
        setUploads(prev => prev.map(upload => 
          upload.fileName === file.name 
            ? { 
                ...upload, 
                progress: 100, 
                status: 'completed',
                url: result.data!.url,
                key: result.data!.key,
              }
            : upload
        ));

        return result;
      } else {
        // Update upload status to error
        setUploads(prev => prev.map(upload => 
          upload.fileName === file.name 
            ? { 
                ...upload, 
                status: 'error',
                error: result.error || 'Upload failed',
              }
            : upload
        ));

        return result;
      }
    } catch (error: any) {
      // Update upload status to error
      setUploads(prev => prev.map(upload => 
        upload.fileName === file.name 
          ? { 
              ...upload, 
              status: 'error',
              error: error.message || 'Upload failed',
            }
          : upload
      ));

      return {
        success: false,
        error: error.message || 'Upload failed',
      };
    } finally {
      setIsUploading(false);
    }
  }, []);

  const uploadFiles = useCallback(async (files: File[], fileType: FileType) => {
    setIsUploading(true);

    try {
      const uploadPromises = files.map(file => uploadFile(file, fileType));
      await Promise.all(uploadPromises);
    } finally {
      setIsUploading(false);
    }
  }, [uploadFile]);

  const deleteFile = useCallback(async (key: string) => {
    try {
      const result = await s3Service.deleteFile(key);
      
      if (result.success) {
        // Remove from uploads if it exists
        setUploads(prev => prev.filter(upload => upload.key !== key));
      }

      return result;
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Delete failed',
      };
    }
  }, []);

  const clearUploads = useCallback(() => {
    setUploads([]);
  }, []);

  const removeUpload = useCallback((fileName: string) => {
    setUploads(prev => prev.filter(upload => upload.fileName !== fileName));
  }, []);

  return {
    uploads,
    isUploading,
    uploadFile,
    uploadFiles,
    deleteFile,
    clearUploads,
    removeUpload,
  };
}
