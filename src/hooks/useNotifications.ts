import { useState, useEffect, useCallback } from 'react';
import { getCookie } from 'cookies-next';

interface NotificationType {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  template: string;
  color?: string;
  isActive: boolean;
}

interface Notification {
  id: number;
  typeId: number;
  title: string;
  content: string;
  data?: any;
  entityType?: string;
  entityId?: number;
  createdAt: string;
  updatedAt: string;
  type: NotificationType;
}

interface UserNotification {
  id: number;
  userId: number;
  notificationId: number;
  isRead: boolean;
  readAt: string | null;
  isArchived: boolean;
  archivedAt: string | null;
  createdAt: string;
  updatedAt: string;
  notification: Notification;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface NotificationResponse {
  data: UserNotification[];
  pagination: PaginationData;
}

interface UseNotificationsOptions {
  limit?: number;
  isRead?: boolean;
  isArchived?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const useNotifications = (options: UseNotificationsOptions = {}) => {
  const {
    limit = 10,
    isRead,
    isArchived = false,
    autoRefresh = true,
    refreshInterval = 60000, // 1 minute by default
  } = options;

  const [notifications, setNotifications] = useState<UserNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData | null>(null);

  const fetchNotifications = useCallback(
    async (page = 1) => {
      setLoading(true);
      setError(null);

      try {
        const token = getCookie('access_token');
        if (!token) {
          setError('Authentication required');
          setLoading(false);
          return;
        }

        // Build query parameters
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());

        if (isRead !== undefined) {
          params.append('isRead', isRead.toString());
        }

        if (isArchived !== undefined) {
          params.append('isArchived', isArchived.toString());
        }

        const response = await fetch(`/api/v1/notification?${params.toString()}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch notifications');
        }

        const data: NotificationResponse = await response.json();
        setNotifications(data.data);
        setPagination(data.pagination);

        // Count unread notifications
        const countResponse = await fetch(
          '/api/v1/notification?isRead=false&isArchived=false&limit=1',
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (countResponse.ok) {
          const countData = await countResponse.json();
          setUnreadCount(countData.pagination.total);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    },
    [limit, isRead, isArchived]
  );

  const markAsRead = useCallback(async (notificationId: number) => {
    try {
      const token = getCookie('access_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/v1/notification/read?id=${notificationId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark notification as read');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.notification.id === notificationId
            ? { ...notification, isRead: true, readAt: new Date().toISOString() }
            : notification
        )
      );

      // Decrease unread count
      setUnreadCount(prev => Math.max(0, prev - 1));

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return false;
    }
  }, []);

  const markAsUnread = useCallback(async (notificationId: number) => {
    try {
      const token = getCookie('access_token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/api/v1/notification/read?id=${notificationId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark notification as unread');
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.notification.id === notificationId
            ? { ...notification, isRead: false, readAt: null }
            : notification
        )
      );

      // Increase unread count
      setUnreadCount(prev => prev + 1);

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      return false;
    }
  }, []);

  const archiveNotification = useCallback(
    async (notificationId: number) => {
      try {
        const token = getCookie('access_token');
        if (!token) {
          throw new Error('Authentication required');
        }

        const response = await fetch(`/api/v1/notification/archive?id=${notificationId}`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to archive notification');
        }

        // Update local state
        setNotifications(prev =>
          prev.filter(notification => notification.notification.id !== notificationId)
        );

        // If the notification was unread, decrease unread count
        const notification = notifications.find(n => n.notification.id === notificationId);
        if (notification && !notification.isRead) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }

        return true;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        return false;
      }
    },
    [notifications]
  );

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Auto refresh if enabled
  useEffect(() => {
    if (!autoRefresh) return;

    const intervalId = setInterval(() => {
      fetchNotifications();
    }, refreshInterval);

    return () => clearInterval(intervalId);
  }, [autoRefresh, refreshInterval, fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    pagination,
    fetchNotifications,
    markAsRead,
    markAsUnread,
    archiveNotification,
  };
};

export default useNotifications;
