import { useState, useCallback } from 'react';
import {
  ChatCompletionRequest,
  ChatCompletionResponse,
  ChatMessage,
  aiCompletionsService
} from '@/services/aiCompletionsService';

interface UseAICompletionsState {
  loading: boolean;
  error: string | null;
  chatResponse: ChatCompletionResponse | null;
}

interface UseAICompletionsReturn extends UseAICompletionsState {
  generateChatCompletion: (request: ChatCompletionRequest) => Promise<ChatCompletionResponse | null>;
  generateChatText: (messages: ChatMessage[], options?: Partial<Omit<ChatCompletionRequest, 'messages'>>) => Promise<string>;
  reset: () => void;
}

export function useAICompletions(): UseAICompletionsReturn {
  const [state, setState] = useState<UseAICompletionsState>({
    loading: false,
    error: null,
    chatResponse: null,
  });

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      chatResponse: null,
    });
  }, []);



  const generateChatCompletion = useCallback(async (request: ChatCompletionRequest): Promise<ChatCompletionResponse | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const data = await aiCompletionsService.createChatCompletion(request);

      setState({
        loading: false,
        error: null,
        chatResponse: data,
      });

      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setState({
        loading: false,
        error: errorMessage,
        chatResponse: null,
      });
      return null;
    }
  }, []);



  const generateChatText = useCallback(async (
    messages: ChatMessage[],
    options: Partial<Omit<ChatCompletionRequest, 'messages'>> = {}
  ): Promise<string> => {
    const request: ChatCompletionRequest = {
      model: options.model || 'openai/gpt-4o',
      messages,
      max_tokens: options.max_tokens || 100,
      temperature: options.temperature || 0.7,
    };

    const response = await generateChatCompletion(request);
    return response?.choices[0]?.message?.content || '';
  }, [generateChatCompletion]);

  return {
    ...state,
    generateChatCompletion,
    generateChatText,
    reset,
  };
}
