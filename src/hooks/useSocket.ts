import { useSocket } from '@/lib/socket-context';
import { useEffect } from 'react';

export const useSocketEvent = (eventName: string, callback: (data: any) => void) => {
  const { socket } = useSocket();

  useEffect(() => {
    if (!socket) return;

    socket.on(eventName, callback);

    return () => {
      socket.off(eventName, callback);
    };
  }, [socket, eventName, callback]);
};

export const useSocketEmit = () => {
  const { socket } = useSocket();

  const emit = (eventName: string, data?: any) => {
    if (socket) {
      socket.emit(eventName, data);
    }
  };

  return emit;
};
