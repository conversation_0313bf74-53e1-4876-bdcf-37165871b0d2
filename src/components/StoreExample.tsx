'use client';

import React from 'react';
import useStore from '../store/useStore';

const StoreExample = () => {
  // Access state and actions from the store
  const count = useStore(state => state.count);
  const increment = useStore(state => state.increment);
  const decrement = useStore(state => state.decrement);
  const reset = useStore(state => state.reset);

  const user = useStore(state => state.user);
  const setUser = useStore(state => state.setUser);
  const login = useStore(state => state.login);
  const logout = useStore(state => state.logout);

  return (
    <div className="p-6 border rounded-lg shadow-sm">
      <h2 className="text-xl font-bold mb-4">Zustand Store Example</h2>

      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Counter</h3>
        <div className="flex items-center gap-3">
          <button onClick={decrement} className="px-3 py-1 bg-red-500 text-white rounded">
            -
          </button>
          <span className="text-xl font-bold">{count}</span>
          <button onClick={increment} className="px-3 py-1 bg-green-500 text-white rounded">
            +
          </button>
          <button onClick={reset} className="px-3 py-1 bg-gray-500 text-white rounded ml-2">
            Reset
          </button>
        </div>
      </div>

      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">User</h3>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <span>Name:</span>
            <input
              type="text"
              value={user.name}
              onChange={e => setUser(e.target.value)}
              className="border px-2 py-1 rounded"
              placeholder="Enter name"
            />
          </div>
          <div className="flex items-center gap-2">
            <span>Status:</span>
            <span className={user.isLoggedIn ? 'text-green-600' : 'text-red-600'}>
              {user.isLoggedIn ? 'Logged In' : 'Logged Out'}
            </span>
          </div>
          <div className="flex gap-2 mt-2">
            <button
              onClick={login}
              disabled={user.isLoggedIn}
              className={`px-3 py-1 rounded ${
                user.isLoggedIn ? 'bg-gray-300 cursor-not-allowed' : 'bg-blue-500 text-white'
              }`}
            >
              Login
            </button>
            <button
              onClick={logout}
              disabled={!user.isLoggedIn}
              className={`px-3 py-1 rounded ${
                !user.isLoggedIn ? 'bg-gray-300 cursor-not-allowed' : 'bg-orange-500 text-white'
              }`}
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreExample;
