'use client';

import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import Sidebar from './Sidebar';
import Header from './Header';
import { SocketProvider, useSocket } from '@/lib/socket-context';
import { useSocketEvent, useSocketEmit } from '@/hooks/useSocket';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh; /* Fixed height instead of min-height */
  width: 100%;
  overflow: hidden; /* Prevent container from overflowing */
`;

const ContentSection = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;

  /* Mobile */
  @media (max-width: 767px) {
    flex-direction: column;
  }
`;

const MainSection = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const ContentWrapper = styled.main`
  flex: 1;
  overflow-y: auto; /* Allow content to scroll vertically */
  overflow-x: hidden; /* Prevent horizontal scrolling */
  // padding: 1rem;
  background-color: var(--background);
  padding-top: 80px;

  /* Tablet */
  @media (max-width: 1023px) {
    padding-top: 70px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding-top: 0;
    padding-bottom: 80px; /* Space for bottom navigation */
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding-bottom: 70px;
  }
`;

export default function MainLayout({ children }: { children: React.ReactNode }) {
  const headerRef = useRef<HTMLElement>(null);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const handleMobileSidebarToggle = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  return (
    <SocketProvider>
      <LayoutContainer>
        <Header ref={headerRef} onMobileMenuToggle={handleMobileSidebarToggle} />
        <ContentSection>
          <Sidebar
            isMobileOpen={isMobileSidebarOpen}
            onMobileToggle={handleMobileSidebarToggle}
          />
          <MainSection>
            <ContentWrapper>{children}</ContentWrapper>
          </MainSection>
        </ContentSection>
      </LayoutContainer>
    </SocketProvider>
  );
}
