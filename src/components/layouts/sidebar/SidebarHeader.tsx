'use client';

import React from 'react';
import styled from 'styled-components';
import Link from 'next/link';
import { Home } from 'lucide-react';
import { Tooltip } from '@/components/ui/tooltip';
import { useSidebar } from '../Sidebar';

const HeaderContainer = styled.div<{ $isCollapsed: boolean }>`
  padding: ${props => (props.$isCollapsed ? '1rem' : '1rem 1.5rem')};
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  justify-content: ${props => (props.$isCollapsed ? 'center' : 'flex-start')};
`;

const LogoContainer = styled.div<{ $isCollapsed: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
`;

const LogoIcon = styled.div<{ $isCollapsed: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 1.5rem;
  width: 3rem;
  height: 3rem;
  border-radius: 0.875rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #f3f4f6;
  margin-right: ${props => (props.$isCollapsed ? '0' : '1rem')};
  flex-shrink: 0;

  &:hover {
    background-color: #374151;
    color: white;
    transform: scale(1.05);
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: none;
  }
`;

const LogoText = styled.span<{ $isCollapsed: boolean }>`
  display: ${props => (props.$isCollapsed ? 'none' : 'block')};
  font-weight: 600;
  font-size: 1.125rem;
  line-height: 1.5rem;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export default function SidebarHeader() {
  const { isCollapsed } = useSidebar();

  const headerContent = (
    <HeaderContainer $isCollapsed={isCollapsed}>
      <LogoContainer $isCollapsed={isCollapsed}>
        <Link
          href="/dashboard"
          style={{ textDecoration: 'none', display: 'flex', alignItems: 'center', width: '100%' }}
        >
          <LogoIcon $isCollapsed={isCollapsed}>
            <Home size={24} />
          </LogoIcon>
          <LogoText $isCollapsed={isCollapsed}>Dashboard</LogoText>
        </Link>
      </LogoContainer>
    </HeaderContainer>
  );

  // Show tooltip only when collapsed
  if (isCollapsed) {
    return (
      <Tooltip content="Dashboard" position="right">
        {headerContent}
      </Tooltip>
    );
  }

  return headerContent;
}
