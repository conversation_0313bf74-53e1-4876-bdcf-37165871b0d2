'use client';

import React, { useState, createContext, useContext } from 'react';
import styled from 'styled-components';
import { X, Menu, ChevronLeft, ChevronRight } from 'lucide-react';
import SidebarHeader from './sidebar/SidebarHeader';
import SidebarMenu from './sidebar/SidebarMenu';
import SidebarFooter from './sidebar/SidebarFooter';

// Context for sidebar state management
interface SidebarContextType {
  isCollapsed: boolean;
  isMobileOpen: boolean;
  toggleCollapse: () => void;
  toggleMobile: () => void;
  expandSidebar: () => void;
  openSubmenus: Set<string>;
  toggleSubmenu: (path: string) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

const SidebarContainer = styled.aside<{ $isCollapsed: boolean; $isMobileOpen: boolean }>`
  display: flex;
  flex-direction: column;
  width: ${props => (props.$isCollapsed ? '72px' : '240px')};
  background-color: transparent;
  backdrop-filter: blur(10px);
  height: calc(100vh - 80px);
  position: sticky;
  top: 100px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Tablet */
  @media (max-width: 1023px) and (min-width: 768px) {
    width: ${props => (props.$isCollapsed ? '64px' : '220px')};
    height: calc(100vh - 70px);
    top: 70px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    position: fixed;
    z-index: 1000;
    width: 280px;
    height: calc(100vh - 80px);
    top: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transform: ${props => (props.$isMobileOpen ? 'translateX(0)' : 'translateX(-100%)')};
  }

  /* Small mobile */
  @media (max-width: 480px) {
    width: 260px;
    height: calc(100vh - 70px);
  }
`;

const MobileToggleButton = styled.button`
  display: none;
  position: fixed;
  left: 10px;
  top: 10px;
  z-index: 1001;
  background-color: #f0f0f0;
  color: #474747;
  border: none;
  border-radius: 50%;
  padding: 8px;
  cursor: pointer;
  width: 40px;
  height: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background-color: #e0e0e0;
    transform: scale(1.05);
  }

  /* Hide this button since we're using the header button instead */
  @media (max-width: 767px) {
    display: none;
  }
`;

const CollapseToggleButton = styled.button<{ $isCollapsed: boolean }>`
  position: absolute;
  right: -16px;
  top: 20px;
  z-index: 1002;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  color: #6b7280;
  border-radius: 50%;
  padding: 2px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: #f8f9fa;
    color: #374151;
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: none;
  }

  /* Tablet */
  @media (max-width: 1023px) and (min-width: 768px) {
    width: 28px;
    height: 28px;
    right: -14px;
  }

  /* Mobile */
  @media (max-width: 767px) {
    display: none;
  }
`;

const SidebarContent = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

const MobileOverlay = styled.div<{ $isVisible: boolean }>`
  display: none;

  /* Mobile */
  @media (max-width: 767px) {
    display: ${props => (props.$isVisible ? 'block' : 'none')};
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }
`;

interface SidebarProps {
  isMobileOpen?: boolean;
  onMobileToggle?: () => void;
}

export default function Sidebar({ isMobileOpen: externalMobileOpen, onMobileToggle }: SidebarProps = {}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [openSubmenus, setOpenSubmenus] = useState<Set<string>>(new Set());

  // Use external mobile state if provided, otherwise use internal state
  const effectiveMobileOpen = externalMobileOpen !== undefined ? externalMobileOpen : isMobileOpen;

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    // Close all submenus when collapsing
    if (!isCollapsed) {
      setOpenSubmenus(new Set());
    }
  };

  const toggleMobile = () => {
    if (onMobileToggle) {
      onMobileToggle();
    } else {
      setIsMobileOpen(!isMobileOpen);
    }
  };

  const toggleSubmenu = (path: string) => {
    const newOpenSubmenus = new Set(openSubmenus);
    if (newOpenSubmenus.has(path)) {
      newOpenSubmenus.delete(path);
    } else {
      newOpenSubmenus.add(path);
    }
    setOpenSubmenus(newOpenSubmenus);
  };

  const expandSidebar = () => {
    setIsCollapsed(false);
  };

  const sidebarContextValue: SidebarContextType = {
    isCollapsed,
    isMobileOpen: effectiveMobileOpen,
    toggleCollapse,
    toggleMobile,
    expandSidebar,
    openSubmenus,
    toggleSubmenu,
  };

  return (
    <SidebarContext.Provider value={sidebarContextValue}>
      <MobileOverlay $isVisible={effectiveMobileOpen} onClick={toggleMobile} />
      <MobileToggleButton onClick={toggleMobile}>
        {effectiveMobileOpen ? <X size={24} /> : <Menu size={24} />}
      </MobileToggleButton>
      <SidebarContainer $isCollapsed={isCollapsed} $isMobileOpen={effectiveMobileOpen}>
        <CollapseToggleButton $isCollapsed={isCollapsed} onClick={toggleCollapse}>
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </CollapseToggleButton>
        <SidebarContent>
          {/* <SidebarHeader /> */}
          <SidebarMenu />
          <SidebarFooter />
        </SidebarContent>
      </SidebarContainer>
    </SidebarContext.Provider>
  );
}
