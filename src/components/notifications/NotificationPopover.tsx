'use client';

import React, { forwardRef, ForwardedRef } from 'react';
import styled, { keyframes } from 'styled-components';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Bell, Check, Archive, Clock, ChevronRight } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import useNotifications from '@/hooks/useNotifications';

const PopoverContainer = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 60px;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: ${props => (props.$isOpen ? 'block' : 'none')};
  z-index: 99999;
  width: 360px;
  max-height: 480px;
  display: flex;
  flex-direction: column;

  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 24px;
    transform: rotate(45deg);
    width: 12px;
    height: 12px;
    background-color: white;
    border-radius: 2px;
    box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
  }

  /* Mobile - Position above bottom navigation */
  @media (max-width: 767px) {
    top: auto;
    bottom: 60px;
    right: 10px;
    left: auto;
    width: 320px;
    max-width: calc(100vw - 20px);
    max-height: 60vh;

    &::before {
      top: auto;
      bottom: -6px;
      right: 60px; /* Position arrow near notification button */
      transform: rotate(225deg); /* Flip arrow to point down */
      box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.05);
    }
  }

  /* Small mobile */
  @media (max-width: 480px) {
    bottom: 50px;
    right: 5px;
    left: auto;
    width: 280px;
    max-width: calc(100vw - 10px);
    max-height: 50vh;

    &::before {
      right: 45px;
    }
  }
`;

const PopoverHeader = styled.div`
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const NotificationsContainer = styled.div`
  overflow-y: auto;
  max-height: 360px;
`;

const NotificationItem = styled.div<{ $isRead: boolean }>`
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  gap: 12px;
  background-color: ${props => (props.$isRead ? 'white' : '#f9f9ff')};
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => (props.$isRead ? '#f9f9f9' : '#f0f0ff')};
  }

  &:last-child {
    border-bottom: none;
  }

  /* Mobile - Increase touch target and adjust spacing */
  @media (max-width: 767px) {
    padding: 14px 12px;
    gap: 10px;
    min-height: 44px; /* Touch-friendly height */
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 12px 10px;
    gap: 8px;
  }
`;

const NotificationIcon = styled.div<{ $color?: string }>`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: ${props => props.$color || '#e0e0e0'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationTitle = styled.div<{ $isRead: boolean }>`
  font-weight: ${props => (props.$isRead ? '500' : '600')};
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 4px;
`;

const NotificationMessage = styled.div`
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const NotificationTime = styled.div`
  font-size: 0.75rem;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const NotificationActions = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f0f0f0;
    color: #4b5563;
  }
`;

const EmptyNotifications = styled.div`
  padding: 32px 16px;
  text-align: center;
  color: #666;
  font-size: 0.9rem;
`;

const ViewAllButton = styled.div`
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
  text-align: center;

  a {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: #3b82f6;
    font-weight: 500;
    font-size: 0.9rem;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const SkeletonBase = styled.div`
  background: #f0f0f0;
  background-image: linear-gradient(to right, #f0f0f0 0%, #e0e0e0 20%, #f0f0f0 40%, #f0f0f0 100%);
  background-size: 200px 100%;
  animation: ${shimmer} 2.5s infinite linear;
  border-radius: 4px;
`;

const LoadingContainer = styled.div`
  padding: 8px;
  display: flex;
  flex-direction: column;
`;

interface NotificationPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  onNotificationRead?: () => void; // Callback to notify parent when notification is read
}

const NotificationPopover = forwardRef<HTMLDivElement, NotificationPopoverProps>(
  ({ isOpen, onClose, onNotificationRead }, ref: ForwardedRef<HTMLDivElement>) => {
    const router = useRouter();
    const { notifications, unreadCount, loading, error, markAsRead, archiveNotification } =
      useNotifications({
        limit: 5,
        isArchived: false,
        autoRefresh: true,
        refreshInterval: 30000, // 30 seconds
      });

    const handleNotificationClick = async (notification: any) => {
      // Extract task ID from notification data
      let taskId: number | null = null;

      // Try to get task ID from entityId if entityType is 'task'
      if (notification.entityType === 'task' && notification.entityId) {
        taskId = notification.entityId;
      }
      // Fallback to data.taskId if available
      else if (notification.data && notification.data.taskId) {
        taskId = notification.data.taskId;
      }

      // Mark notification as read
      const success = await markAsRead(notification.id);
      if (success && onNotificationRead) {
        onNotificationRead();
      }

      // Navigate to kanban task page if task ID is found
      if (taskId) {
        onClose(); // Close the popover before navigating
        router.push(`/kanban/${taskId}`);
      }
    };

    const handleArchive = async (e: React.MouseEvent, notificationId: number) => {
      e.stopPropagation();
      const success = await archiveNotification(notificationId);
      if (success && onNotificationRead) {
        onNotificationRead();
      }
    };

    const handleMarkAsRead = async (e: React.MouseEvent, notificationId: number) => {
      e.stopPropagation();
      const success = await markAsRead(notificationId);
      if (success && onNotificationRead) {
        onNotificationRead();
      }
    };

    return (
      <PopoverContainer $isOpen={isOpen} ref={ref}>
        <PopoverHeader>
          <span>Notifications {unreadCount > 0 && `(${unreadCount})`}</span>
        </PopoverHeader>

        <NotificationsContainer>
          {loading ? (
            <LoadingContainer>
              {[...Array(3)].map((_, index) => (
                <NotificationItem
                  key={index}
                  $isRead={true}
                  as="div"
                  style={{ opacity: 0.7, cursor: 'default' }}
                >
                  <SkeletonBase style={{ width: '36px', height: '36px', borderRadius: '50%' }} />
                  <NotificationContent>
                    <SkeletonBase style={{ width: '70%', height: '16px', marginBottom: '8px' }} />
                    <SkeletonBase style={{ width: '90%', height: '14px', marginBottom: '8px' }} />
                    <SkeletonBase style={{ width: '40%', height: '12px' }} />
                  </NotificationContent>
                  <NotificationActions>
                    <SkeletonBase style={{ width: '24px', height: '24px', borderRadius: '4px' }} />
                  </NotificationActions>
                </NotificationItem>
              ))}
            </LoadingContainer>
          ) : error ? (
            <EmptyNotifications>Failed to load notifications</EmptyNotifications>
          ) : notifications.length === 0 ? (
            <EmptyNotifications>No notifications to display</EmptyNotifications>
          ) : (
            notifications.map(item => (
              <NotificationItem
                key={item.id}
                $isRead={item.isRead}
                onClick={() => handleNotificationClick(item.notification)}
              >
                <NotificationIcon $color={item.notification.type.color}>
                  <Bell size={16} />
                </NotificationIcon>
                <NotificationContent>
                  <NotificationTitle $isRead={item.isRead}>
                    {item.notification.title}
                  </NotificationTitle>
                  <NotificationMessage>{item.notification.content}</NotificationMessage>
                  <NotificationTime>
                    <Clock size={12} />
                    {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
                  </NotificationTime>
                </NotificationContent>
                <NotificationActions>
                  {!item.isRead && (
                    <ActionButton
                      title="Mark as read"
                      onClick={e => handleMarkAsRead(e, item.notification.id)}
                    >
                      <Check size={16} />
                    </ActionButton>
                  )}
                  <ActionButton
                    title="Archive"
                    onClick={e => handleArchive(e, item.notification.id)}
                  >
                    <Archive size={16} />
                  </ActionButton>
                </NotificationActions>
              </NotificationItem>
            ))
          )}
        </NotificationsContainer>

        <ViewAllButton>
          <Link href="/notification" onClick={onClose}>
            View all notifications
            <ChevronRight size={16} />
          </Link>
        </ViewAllButton>
      </PopoverContainer>
    );
  }
);

NotificationPopover.displayName = 'NotificationPopover';

export default NotificationPopover;
