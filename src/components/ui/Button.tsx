import styled from 'styled-components';
import { ButtonHTMLAttributes, ReactNode } from 'react';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
  $variant?: 'primary' | 'secondary' | 'outline';
  $size?: 'small' | 'medium' | 'large';
  $fullWidth?: boolean;
}

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  /* Size variants */
  ${props =>
    props.$size === 'small' &&
    `
    padding: 8px 16px;
    font-size: 14px;
  `}

  ${props =>
    props.$size === 'medium' || !props.$size
      ? `
    padding: 10px 20px;
    font-size: 16px;
  `
      : ''}
  
  ${props =>
    props.$size === 'large' &&
    `
    padding: 12px 24px;
    font-size: 18px;
  `}
  
  /* Color variants */
  ${props =>
    props.$variant === 'primary' || !props.$variant
      ? `
    background-color: #3B82F6;
    color: white;
    border: none;
    
    &:hover {
      background-color: #2563EB;
    }
    
    &:active {
      background-color: #1D4ED8;
    }
  `
      : ''}
  
  ${props =>
    props.$variant === 'secondary' &&
    `
    background-color: #6B7280;
    color: white;
    border: none;
    
    &:hover {
      background-color: #4B5563;
    }
    
    &:active {
      background-color: #374151;
    }
  `}
  
  ${props =>
    props.$variant === 'outline' &&
    `
    background-color: transparent;
    color: #3B82F6;
    border: 1px solid #3B82F6;
    
    &:hover {
      background-color: rgba(59, 130, 246, 0.1);
    }
    
    &:active {
      background-color: rgba(59, 130, 246, 0.2);
    }
  `}
  
  /* Full width */
  ${props =>
    props.$fullWidth &&
    `
    width: 100%;
  `}
  
  /* Disabled state */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

export const Button = ({
  children,
  $variant = 'primary',
  $size = 'medium',
  $fullWidth = false,
  ...props
}: ButtonProps) => {
  return (
    <StyledButton $variant={$variant} $size={$size} $fullWidth={$fullWidth} {...props}>
      {children}
    </StyledButton>
  );
};

export default Button;
