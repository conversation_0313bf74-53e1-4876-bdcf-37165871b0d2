'use client';

import React, { useCallback, useState } from 'react';
import { useFileUpload, UploadProgress } from '@/hooks/useFileUpload';
import { FileType } from '@/services/s3Service';
import { Upload, X, File, Image, FileText, User, MessageCircle, CheckCircle, AlertCircle } from 'lucide-react';

interface FileUploadProps {
  fileType: FileType;
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  onUploadComplete?: (files: { url: string; key: string; fileName: string }[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
}

const FILE_TYPE_ICONS = {
  images: Image,
  documents: FileText,
  avatars: User,
  chat: MessageCircle,
};

const FILE_TYPE_LABELS = {
  images: 'Images',
  documents: 'Documents',
  avatars: 'Avatar',
  chat: 'Chat Files',
};

const DEFAULT_ACCEPT = {
  images: 'image/jpeg,image/png,image/gif,image/webp',
  documents: '.pdf,.doc,.docx,.xls,.xlsx,.txt',
  avatars: 'image/jpeg,image/png,image/webp',
  chat: 'image/jpeg,image/png,image/gif,image/webp,.pdf,.txt',
};

export function FileUpload({
  fileType,
  accept,
  multiple = false,
  maxFiles = 5,
  onUploadComplete,
  onUploadError,
  className = '',
  disabled = false,
}: FileUploadProps) {
  const { uploads, isUploading, uploadFile, uploadFiles, removeUpload } = useFileUpload();
  const [dragActive, setDragActive] = useState(false);

  const IconComponent = FILE_TYPE_ICONS[fileType];
  const acceptTypes = accept || DEFAULT_ACCEPT[fileType];

  const handleFiles = useCallback(async (files: FileList) => {
    if (disabled) return;

    const fileArray = Array.from(files);
    const filesToUpload = multiple ? fileArray.slice(0, maxFiles) : [fileArray[0]];

    try {
      if (multiple) {
        await uploadFiles(filesToUpload, fileType);
        
        // Check for completed uploads
        const completedUploads = uploads
          .filter(upload => upload.status === 'completed' && upload.url && upload.key)
          .map(upload => ({
            url: upload.url!,
            key: upload.key!,
            fileName: upload.fileName,
          }));

        if (completedUploads.length > 0 && onUploadComplete) {
          onUploadComplete(completedUploads);
        }
      } else {
        const result = await uploadFile(filesToUpload[0], fileType);
        
        if (result.success && result.data && onUploadComplete) {
          onUploadComplete([result.data]);
        } else if (!result.success && onUploadError) {
          onUploadError(result.error || 'Upload failed');
        }
      }
    } catch (error: any) {
      if (onUploadError) {
        onUploadError(error.message || 'Upload failed');
      }
    }
  }, [disabled, multiple, maxFiles, uploadFile, uploadFiles, fileType, uploads, onUploadComplete, onUploadError]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [disabled, handleFiles]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (disabled) return;

    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [disabled, handleFiles]);

  const getStatusIcon = (status: UploadProgress['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 cursor-pointer'}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          multiple={multiple}
          accept={acceptTypes}
          onChange={handleChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
        
        <div className="flex flex-col items-center space-y-2">
          <div className="flex items-center space-x-2">
            <IconComponent className="w-8 h-8 text-gray-400" />
            <Upload className="w-8 h-8 text-gray-400" />
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-900">
              Drop {FILE_TYPE_LABELS[fileType].toLowerCase()} here or click to browse
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {multiple ? `Up to ${maxFiles} files` : 'Single file only'}
            </p>
          </div>
        </div>
      </div>

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-900">Upload Progress</h4>
          
          {uploads.map((upload, index) => (
            <div key={`${upload.fileName}-${index}`} className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <File className="w-4 h-4 text-gray-400 flex-shrink-0" />
                  <span className="text-sm text-gray-900 truncate">{upload.fileName}</span>
                  {getStatusIcon(upload.status)}
                </div>
                
                <button
                  onClick={() => removeUpload(upload.fileName)}
                  className="ml-2 p-1 hover:bg-gray-200 rounded"
                >
                  <X className="w-4 h-4 text-gray-400" />
                </button>
              </div>
              
              {upload.status === 'uploading' && (
                <div className="mt-2">
                  <div className="bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${upload.progress}%` }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{upload.progress}%</p>
                </div>
              )}
              
              {upload.status === 'error' && upload.error && (
                <p className="text-xs text-red-500 mt-1">{upload.error}</p>
              )}
              
              {upload.status === 'completed' && upload.url && (
                <p className="text-xs text-green-600 mt-1">Upload completed successfully</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Loading State */}
      {isUploading && (
        <div className="mt-4 flex items-center justify-center space-x-2 text-sm text-gray-600">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <span>Uploading...</span>
        </div>
      )}
    </div>
  );
}
