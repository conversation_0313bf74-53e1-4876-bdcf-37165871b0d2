'use client';

import React, { useState } from 'react';
import { useSocket } from '@/lib/socket-context';
import { useSocketEvent, useSocketEmit } from '@/hooks/useSocket';

export const SocketStatusIndicator: React.FC = () => {
  const { isConnected } = useSocket();

  return (
    <div
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        padding: '8px 12px',
        borderRadius: '4px',
        backgroundColor: isConnected ? '#4CAF50' : '#F44336',
        color: 'white',
        fontSize: '12px',
        zIndex: 1000,
      }}
    >
      {isConnected ? 'Socket Connected' : 'Socket Disconnected'}
    </div>
  );
};

export const SocketTestComponent: React.FC = () => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<string[]>([]);
  const emit = useSocketEmit();

  // Listen for incoming messages
  useSocketEvent('message', (data: string) => {
    setMessages(prev => [...prev, `Received: ${data}`]);
  });

  const sendMessage = () => {
    if (message.trim()) {
      emit('message', message);
      setMessages(prev => [...prev, `Sent: ${message}`]);
      setMessage('');
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', margin: '20px' }}>
      <h3>Socket.IO Test Component</h3>

      <div style={{ marginBottom: '10px' }}>
        <input
          type="text"
          value={message}
          onChange={e => setMessage(e.target.value)}
          placeholder="Enter a message..."
          style={{ marginRight: '10px', padding: '5px' }}
          onKeyPress={e => e.key === 'Enter' && sendMessage()}
        />
        <button onClick={sendMessage} style={{ padding: '5px 10px' }}>
          Send Message
        </button>
      </div>
      <div>
        <h4>Messages:</h4>
        <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
          {messages.map((msg, index) => (
            <div key={index} style={{ padding: '2px 0' }}>
              {msg}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
