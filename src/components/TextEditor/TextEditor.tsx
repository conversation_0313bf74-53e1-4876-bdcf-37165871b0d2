'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Loader2,
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import { resizeImage, isSupportedImageFormat, type ImageResizeOptions } from '@/utils/imageUpload';

interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  uploadImage?: (file: File) => Promise<string>;
  imageResizeOptions?: ImageResizeOptions;
  enableImageResize?: boolean;
}

const EditorContainer = styled.div`
  position: relative;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${appTheme.colors.background.main};
  overflow: hidden;
`;

const Toolbar = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: ${props => props.$active ? '#e5e7eb' : 'transparent'};
  color: ${props => props.$active ? '#374151' : '#6b7280'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const ToolbarSeparator = styled.div`
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 4px;
`;

const EditorContent = styled.div<{ $minHeight?: number; $maxHeight?: number }>`
  min-height: ${props => props.$minHeight || 150}px;
  max-height: ${props => props.$maxHeight || 400}px;
  overflow-y: auto;
  padding: 12px;
  font-family: inherit;
  font-size: ${appTheme.typography.fontSizes.sm};
  line-height: 1.5;
  color: ${appTheme.colors.text.primary};
  outline: none;
  
  &:empty:before {
    content: attr(data-placeholder);
    color: #9ca3af;
    pointer-events: none;
  }

  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul, ol {
    margin: 8px 0;
    padding-left: 24px;
  }

  li {
    margin: 4px 0;
  }

  a {
    color: #3b82f6;
    text-decoration: underline;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }

  .text-left {
    text-align: left;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }
`;

const ImageUploadInput = styled.input`
  display: none;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: ${appTheme.borderRadius.md};
`;

const LoadingContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: white;
  border-radius: ${appTheme.borderRadius.sm};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.secondary};

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

export default function TextEditor({
  value,
  onChange,
  placeholder = 'Start typing...',
  minHeight = 150,
  maxHeight = 400,
  uploadImage,
  imageResizeOptions = { maxWidth: 600, quality: 0.85 },
  enableImageResize = true
}: TextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isProcessingImage, setIsProcessingImage] = useState(false);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  }, [onChange]);

  const execCommand = useCallback((command: string, value?: string) => {
    try {
      // Check if execCommand is available and use it
      if (typeof document.execCommand === 'function') {
        document.execCommand(command, false, value);
      } else {
        // For browsers that don't support execCommand, handle manually
        console.warn(`execCommand not supported for: ${command}`);
      }
      editorRef.current?.focus();
      handleContentChange();
    } catch (error) {
      console.warn('Error executing command:', command, error);
      editorRef.current?.focus();
    }
  }, [handleContentChange]);

  const isCommandActive = useCallback((command: string) => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined') {
      return false;
    }
    
    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return false;
      
      const range = selection.getRangeAt(0);
      const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE 
        ? range.commonAncestorContainer.parentElement 
        : range.commonAncestorContainer as Element;
      
      if (!parentElement) return false;
      
      // Check for specific formatting
      switch (command) {
        case 'bold':
          return window.getComputedStyle(parentElement).fontWeight === 'bold' || 
                 window.getComputedStyle(parentElement).fontWeight === '700' ||
                 parentElement.closest('strong, b') !== null;
        case 'italic':
          return window.getComputedStyle(parentElement).fontStyle === 'italic' ||
                 parentElement.closest('em, i') !== null;
        case 'underline':
          return window.getComputedStyle(parentElement).textDecoration.includes('underline') ||
                 parentElement.closest('u') !== null;
        case 'insertUnorderedList':
          return parentElement.closest('ul') !== null;
        case 'insertOrderedList':
          return parentElement.closest('ol') !== null;
        case 'justifyLeft':
          return window.getComputedStyle(parentElement).textAlign === 'left' || 
                 window.getComputedStyle(parentElement).textAlign === 'start';
        case 'justifyCenter':
          return window.getComputedStyle(parentElement).textAlign === 'center';
        case 'justifyRight':
          return window.getComputedStyle(parentElement).textAlign === 'right';
        default:
          return false;
      }
    } catch (error) {
      console.warn('Error checking command state:', error);
      return false;
    }
  }, []);

  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      try {
        setIsProcessingImage(true);

        // Check if it's a supported format for resizing
        if (!isSupportedImageFormat(file)) {
          throw new Error('Unsupported image format. Please use JPEG, PNG, WebP, or GIF.');
        }

        let processedFile = file;

        // Resize image if enabled and options are provided
        if (enableImageResize && imageResizeOptions) {
          try {
            processedFile = await resizeImage(file, imageResizeOptions);
          } catch (resizeError) {
            console.warn('Failed to resize image, using original:', resizeError);
            // Continue with original file if resize fails
          }
        }

        if (uploadImage) {
          // Upload to server and get URL
          const imageUrl = await uploadImage(processedFile);
          execCommand('insertImage', imageUrl);
        } else {
          // Fallback to base64
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            execCommand('insertImage', imageUrl);
          };
          reader.readAsDataURL(processedFile);
        }
      } catch (error) {
        console.error('Failed to process/upload image:', error);
        // Fallback to base64 with original file on error
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          execCommand('insertImage', imageUrl);
        };
        reader.readAsDataURL(file);
      } finally {
        setIsProcessingImage(false);
      }
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [execCommand, uploadImage, enableImageResize, imageResizeOptions]);

  const toolbarButtons = [
    { command: 'bold', icon: Bold, title: 'Bold' },
    { command: 'italic', icon: Italic, title: 'Italic' },
    { command: 'underline', icon: Underline, title: 'Underline' },
    { separator: true },
    { command: 'insertUnorderedList', icon: List, title: 'Bullet List' },
    { command: 'insertOrderedList', icon: ListOrdered, title: 'Numbered List' },
    { separator: true },
    { command: 'justifyLeft', icon: AlignLeft, title: 'Align Left' },
    { command: 'justifyCenter', icon: AlignCenter, title: 'Align Center' },
    { command: 'justifyRight', icon: AlignRight, title: 'Align Right' },
    { separator: true },
    { command: 'undo', icon: Undo, title: 'Undo' },
    { command: 'redo', icon: Redo, title: 'Redo' },
  ] as const;

  return (
    <EditorContainer>
      <Toolbar>
        {toolbarButtons.map((button, index) => {
          if ('separator' in button && button.separator) {
            return <ToolbarSeparator key={index} />;
          }

          if ('icon' in button && button.icon) {
            const Icon = button.icon;
            return (
              <ToolbarButton
                key={button.command}
                type="button"
                title={button.title}
                $active={isCommandActive(button.command)}
                onClick={() => execCommand(button.command)}
              >
                <Icon />
              </ToolbarButton>
            );
          }

          return null;
        })}

        <ToolbarSeparator />

        <ToolbarButton
          type="button"
          title="Insert Image"
          onClick={() => fileInputRef.current?.click()}
          disabled={isProcessingImage}
        >
          <Image />
        </ToolbarButton>
      </Toolbar>

      <EditorContent
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        data-placeholder={placeholder}
        $minHeight={minHeight}
        $maxHeight={maxHeight}
        onInput={handleContentChange}
        onPaste={handleContentChange}
      />

      <ImageUploadInput
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        disabled={isProcessingImage}
      />

      {isProcessingImage && (
        <LoadingOverlay>
          <LoadingContent>
            <Loader2 size={16} />
            Resizing image...
          </LoadingContent>
        </LoadingOverlay>
      )}
    </EditorContainer>
  );
}
