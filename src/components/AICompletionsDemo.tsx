'use client';

import React, { useState } from 'react';
import { useAICompletions } from '@/hooks/useAICompletions';
import { ChatCompletionRequest, ChatMessage } from '@/services/aiCompletionsService';

export default function AICompletionsDemo() {
  const { loading, error, chatResponse, generateChatCompletion, generateChatText, reset } = useAICompletions();
  const [model, setModel] = useState('openai/gpt-4o');
  const [maxTokens, setMaxTokens] = useState(100);
  const [temperature, setTemperature] = useState(0.7);
  const [messages, setMessages] = useState<ChatMessage[]>([
    { role: 'user', content: 'วันนี้วันที่เท่าไร' }
  ]);
  const [newMessage, setNewMessage] = useState('');

  const handleGenerateCompletion = async () => {
    const request: ChatCompletionRequest = {
      model,
      messages,
      max_tokens: maxTokens,
      temperature,
    };
    await generateChatCompletion(request);
  };

  const handleQuickTest = async () => {
    const result = await generateChatText([
      { role: 'user', content: 'สวัสดีครับ ช่วยแนะนำตัวหน่อย' }
    ], {
      model: 'openai/gpt-4o',
      max_tokens: 100,
      temperature: 0.7,
    });
    console.log('Quick test result:', result);
  };

  const addMessage = () => {
    if (newMessage.trim()) {
      setMessages([...messages, { role: 'user', content: newMessage.trim() }]);
      setNewMessage('');
    }
  };

  const removeMessage = (index: number) => {
    setMessages(messages.filter((_, i) => i !== index));
  };

  const updateMessage = (index: number, content: string) => {
    const updatedMessages = [...messages];
    updatedMessages[index].content = content;
    setMessages(updatedMessages);
  };

  const updateMessageRole = (index: number, role: 'system' | 'user' | 'assistant') => {
    const updatedMessages = [...messages];
    updatedMessages[index].role = role;
    setMessages(updatedMessages);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">AI Chat Completions Demo</h1>

        {/* Input Form */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="openai/gpt-4o">GPT-4o</option>
              <option value="openai/gpt-4">GPT-4</option>
              <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Messages
            </label>
            <div className="space-y-2 mb-4">
              {messages.map((message, index) => (
                <div key={index} className="flex space-x-2 items-start">
                  <select
                    value={message.role}
                    onChange={(e) => updateMessageRole(index, e.target.value as 'system' | 'user' | 'assistant')}
                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                  >
                    <option value="system">System</option>
                    <option value="user">User</option>
                    <option value="assistant">Assistant</option>
                  </select>
                  <textarea
                    value={message.content}
                    onChange={(e) => updateMessage(index, e.target.value)}
                    rows={2}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter message content..."
                  />
                  <button
                    onClick={() => removeMessage(index)}
                    className="px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
            <div className="flex space-x-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add new message..."
                onKeyDown={(e) => e.key === 'Enter' && addMessage()}
              />
              <button
                onClick={addMessage}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Add Message
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Tokens
              </label>
              <input
                type="number"
                value={maxTokens}
                onChange={(e) => setMaxTokens(Number(e.target.value))}
                min="1"
                max="2000"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Temperature
              </label>
              <input
                type="number"
                value={temperature}
                onChange={(e) => setTemperature(Number(e.target.value))}
                min="0"
                max="2"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={handleGenerateCompletion}
            disabled={loading || messages.length === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {loading && (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            Generate Chat Completion
          </button>

          <button
            onClick={handleQuickTest}
            disabled={loading}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Quick Test
          </button>

          <button
            onClick={reset}
            className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Reset
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {/* Response Display */}
        {chatResponse && (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="text-lg font-medium text-green-800 mb-2">Generated Text</h3>
              <p className="text-green-700 whitespace-pre-wrap">
                {chatResponse.choices[0]?.message?.content}
              </p>
            </div>

            <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Response Details</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">ID:</span> {chatResponse.id}
                </div>
                <div>
                  <span className="font-medium">Model:</span> {chatResponse.model}
                </div>
                <div>
                  <span className="font-medium">Created:</span> {new Date(chatResponse.created * 1000).toLocaleString()}
                </div>
                <div>
                  <span className="font-medium">Finish Reason:</span> {chatResponse.choices[0]?.finish_reason}
                </div>
              </div>

              {chatResponse.usage && (
                <div className="mt-4">
                  <h4 className="font-medium text-gray-700 mb-2">Token Usage</h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Prompt:</span> {chatResponse.usage.prompt_tokens}
                    </div>
                    <div>
                      <span className="font-medium">Completion:</span> {chatResponse.usage.completion_tokens}
                    </div>
                    <div>
                      <span className="font-medium">Total:</span> {chatResponse.usage.total_tokens}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Full Response JSON</h3>
              <pre className="text-xs text-blue-700 overflow-x-auto">
                {JSON.stringify(chatResponse, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
