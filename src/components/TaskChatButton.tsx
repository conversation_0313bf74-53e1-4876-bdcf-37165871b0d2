'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import styled from 'styled-components';
import { MessageCircle, Loader2 } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { navigateToChatWithTask } from '@/services/taskChatHelper';

interface TaskChatButtonProps {
  taskId: number;
  taskTitle: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ChatButton = styled.button<{
  $variant: 'primary' | 'secondary' | 'outline';
  $size: 'sm' | 'md' | 'lg';
}>`
  display: inline-flex;
  align-items: center;
  gap: ${props => (props.$size === 'sm' ? appTheme.spacing.xs : appTheme.spacing.sm)};
  padding: ${props => {
    switch (props.$size) {
      case 'sm':
        return `${appTheme.spacing.xs} ${appTheme.spacing.sm}`;
      case 'lg':
        return `${appTheme.spacing.md} ${appTheme.spacing.lg}`;
      default:
        return `${appTheme.spacing.sm} ${appTheme.spacing.md}`;
    }
  }};
  border: 1px solid
    ${props => {
      switch (props.$variant) {
        case 'primary':
          return appTheme.colors.primary;
        case 'secondary':
          return appTheme.colors.secondary;
        case 'outline':
          return appTheme.colors.border;
        default:
          return appTheme.colors.primary;
      }
    }};
  border-radius: ${appTheme.borderRadius.md};
  background: ${props => {
    switch (props.$variant) {
      case 'primary':
        return appTheme.colors.primary;
      case 'secondary':
        return appTheme.colors.secondary;
      case 'outline':
        return 'transparent';
      default:
        return appTheme.colors.primary;
    }
  }};
  color: ${props => {
    switch (props.$variant) {
      case 'primary':
        return appTheme.colors.text.light;
      case 'secondary':
        return appTheme.colors.text.light;
      case 'outline':
        return appTheme.colors.text.primary;
      default:
        return appTheme.colors.text.light;
    }
  }};
  font-size: ${props => {
    switch (props.$size) {
      case 'sm':
        return '12px';
      case 'lg':
        return '16px';
      default:
        return '14px';
    }
  }};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const IconWrapper = styled.span<{ $size: 'sm' | 'md' | 'lg' }>`
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: ${props => {
      switch (props.$size) {
        case 'sm':
          return '14px';
        case 'lg':
          return '20px';
        default:
          return '16px';
      }
    }};
    height: ${props => {
      switch (props.$size) {
        case 'sm':
          return '14px';
        case 'lg':
          return '20px';
        default:
          return '16px';
      }
    }};
  }
`;

export default function TaskChatButton({
  taskId,
  taskTitle,
  variant = 'primary',
  size = 'md',
  className,
}: TaskChatButtonProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    setLoading(true);
    try {
      navigateToChatWithTask(router, taskId, taskTitle);
    } catch (error) {
      console.error('Failed to navigate to task chat:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ChatButton
      $variant={variant}
      $size={size}
      className={className}
      onClick={handleClick}
      disabled={loading}
    >
      <IconWrapper $size={size}>
        {loading ? <Loader2 className="animate-spin" /> : <MessageCircle />}
      </IconWrapper>
      {loading ? 'Opening...' : 'Open Chat'}
    </ChatButton>
  );
}

// Export a simple hook for creating task chats programmatically
export const useTaskChat = () => {
  const router = useRouter();

  const openTaskChat = (taskId: number, taskTitle: string) => {
    navigateToChatWithTask(router, taskId, taskTitle);
  };

  return { openTaskChat };
};
