import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

/**
 * Helper function to extract token from request
 */
export function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

/**
 * Helper function to verify user is authenticated and get user info
 */
export async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
    isBot: user.userRole.isBot,
  };
}

/**
 * Simplified authentication for endpoints that only need userId
 */
export async function authenticateUserSimple(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

export function decodeJWT(token: string): any {
  try {
    // Validate token format
    if (!token || typeof token !== 'string') {
      return null;
    }

    // JWT has 3 parts separated by dots: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // Decode the payload (second part)
    const payload = parts[1];

    // Add padding if needed for base64 decoding
    const paddedPayload = payload + '='.repeat((4 - (payload.length % 4)) % 4);

    // Decode base64 - handle both browser and Node.js environments
    let decodedPayload: string;
    if (typeof atob !== 'undefined') {
      // Browser environment
      decodedPayload = atob(paddedPayload);
    } else {
      // Node.js environment (middleware runs on server)
      decodedPayload = Buffer.from(paddedPayload, 'base64').toString('utf-8');
    }

    // Parse JSON
    return JSON.parse(decodedPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

/**
 * Check if JWT token is expired
 */
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = decodeJWT(token);

    if (!decoded || !decoded.exp) {
      // If we can't decode or there's no expiration, consider it expired
      return true;
    }

    // JWT exp is in seconds, Date.now() is in milliseconds
    const currentTime = Math.floor(Date.now() / 1000);

    return decoded.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Consider expired if there's an error
  }
}


/**
 * Validate access token from cookies
 */
export function validateAccessToken(token: string | undefined): boolean {
  if (!token) {
    return false;
  }

  // Check if token is expired
  if (isTokenExpired(token)) {
    return false;
  }

  return true;
}