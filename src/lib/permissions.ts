import { prisma } from '@/lib/prisma';

/**
 * Check if a user is an owner of an organization
 */
export async function isOrganizationOwner(userId: number, organizationId: number): Promise<boolean> {
  try {
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      select: { ownerUserId: true },
    });

    return organization?.ownerUserId === userId;
  } catch (error) {
    console.error('Error checking organization ownership:', error);
    return false;
  }
}

/**
 * Check if a user is an admin of an organization
 */
export async function isOrganizationAdmin(userId: number, organizationId: number): Promise<boolean> {
  try {
    const admin = await prisma.organizationAdmin.findUnique({
      where: {
        userId_organizationId: {
          userId,
          organizationId,
        },
      },
      select: { isActive: true },
    });

    return admin?.isActive === true;
  } catch (error) {
    console.error('Error checking organization admin status:', error);
    return false;
  }
}

/**
 * Check if a user has admin privileges in an organization (either owner or admin)
 */
export async function hasOrganizationAdminPrivileges(userId: number, organizationId: number): Promise<boolean> {
  try {
    const [isOwner, isAdmin] = await Promise.all([
      isOrganizationOwner(userId, organizationId),
      isOrganizationAdmin(userId, organizationId),
    ]);

    return isOwner || isAdmin;
  } catch (error) {
    console.error('Error checking organization admin privileges:', error);
    return false;
  }
}

/**
 * Get all organizations where a user has admin privileges
 */
export async function getUserOrganizationAdminPrivileges(userId: number): Promise<{
  ownedOrganizations: number[];
  adminOrganizations: number[];
  allAdminOrganizations: number[];
}> {
  try {
    const [ownedOrgs, adminOrgs] = await Promise.all([
      // Organizations owned by the user
      prisma.organization.findMany({
        where: { ownerUserId: userId },
        select: { id: true },
      }),
      // Organizations where user is admin
      prisma.organizationAdmin.findMany({
        where: {
          userId,
          isActive: true,
        },
        select: { organizationId: true },
      }),
    ]);

    const ownedOrganizations = ownedOrgs.map(org => org.id);
    const adminOrganizations = adminOrgs.map(admin => admin.organizationId);
    const allAdminOrganizations = [...new Set([...ownedOrganizations, ...adminOrganizations])];

    return {
      ownedOrganizations,
      adminOrganizations,
      allAdminOrganizations,
    };
  } catch (error) {
    console.error('Error getting user organization admin privileges:', error);
    return {
      ownedOrganizations: [],
      adminOrganizations: [],
      allAdminOrganizations: [],
    };
  }
}

/**
 * Check if a user can access a department (based on organization admin privileges)
 */
export async function canAccessDepartment(userId: number, departmentId: number): Promise<boolean> {
  try {
    const department = await prisma.department.findUnique({
      where: { id: departmentId },
      select: { organizationId: true },
    });

    if (!department) {
      return false;
    }

    return await hasOrganizationAdminPrivileges(userId, department.organizationId);
  } catch (error) {
    console.error('Error checking department access:', error);
    return false;
  }
}

/**
 * Check if a user can access a task (based on organization admin privileges or task assignment)
 */
export async function canAccessTask(userId: number, taskId: number): Promise<boolean> {
  try {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { 
        organizationId: true,
        createdByUserId: true,
        taskAssignments: {
          where: { userId },
          select: { id: true },
        },
      },
    });

    if (!task) {
      return false;
    }

    // Check if user created the task
    if (task.createdByUserId === userId) {
      return true;
    }

    // Check if user is assigned to the task
    if (task.taskAssignments.length > 0) {
      return true;
    }

    // Check if user has organization admin privileges
    return await hasOrganizationAdminPrivileges(userId, task.organizationId);
  } catch (error) {
    console.error('Error checking task access:', error);
    return false;
  }
}

/**
 * Filter organizations based on user's admin privileges
 */
export async function filterOrganizationsByAdminPrivileges(
  userId: number,
  organizationIds: number[]
): Promise<number[]> {
  try {
    const privileges = await getUserOrganizationAdminPrivileges(userId);
    return organizationIds.filter(orgId => privileges.allAdminOrganizations.includes(orgId));
  } catch (error) {
    console.error('Error filtering organizations by admin privileges:', error);
    return [];
  }
}

/**
 * Check if a user has global admin role (isAdmin = true in UserRole)
 */
export async function hasGlobalAdminRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isAdmin === true;
  } catch (error) {
    console.error('Error checking global admin role:', error);
    return false;
  }
}

/**
 * Check if a user has owner role (isOwner = true in UserRole)
 */
export async function hasOwnerRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isOwner === true;
  } catch (error) {
    console.error('Error checking owner role:', error);
    return false;
  }
}

/**
 * Check if a user has bot role (isBot = true in UserRole)
 */
export async function hasBotRole(userId: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isBot === true;
  } catch (error) {
    console.error('Error checking bot role:', error);
    return false;
  }
}

/**
 * Check if a user is a leader in a specific department
 */
export async function isDepartmentLeader(userId: number, departmentId: number): Promise<boolean> {
  try {
    const departmentMember = await prisma.departmentMember.findFirst({
      where: {
        userId,
        departmentId,
        isLeader: true,
      },
    });

    return !!departmentMember;
  } catch (error) {
    console.error('Error checking department leadership:', error);
    return false;
  }
}

/**
 * Check if a user is a leader in any department
 */
export async function isAnyDepartmentLeader(userId: number): Promise<boolean> {
  try {
    const departmentMember = await prisma.departmentMember.findFirst({
      where: {
        userId,
        isLeader: true,
      },
    });

    return !!departmentMember;
  } catch (error) {
    console.error('Error checking any department leadership:', error);
    return false;
  }
}

/**
 * Get all users in an organization (for organization feedback)
 */
export async function getOrganizationMembers(organizationId: number): Promise<number[]> {
  try {
    const departments = await prisma.department.findMany({
      where: { organizationId },
      include: {
        members: {
          select: { userId: true },
        },
      },
    });

    const userIds = new Set<number>();
    departments.forEach(dept => {
      dept.members.forEach(member => {
        userIds.add(member.userId);
      });
    });

    return Array.from(userIds);
  } catch (error) {
    console.error('Error getting organization members:', error);
    return [];
  }
}

/**
 * Get all users in a department (for department feedback)
 */
export async function getDepartmentMembers(departmentId: number): Promise<number[]> {
  try {
    const members = await prisma.departmentMember.findMany({
      where: { departmentId },
      select: { userId: true },
    });

    return members.map(member => member.userId);
  } catch (error) {
    console.error('Error getting department members:', error);
    return [];
  }
}

/**
 * Get all users assigned to a task (for task feedback)
 */
export async function getTaskAssignedMembers(taskId: number): Promise<number[]> {
  try {
    const assignments = await prisma.taskAssignment.findMany({
      where: {
        taskId,
        isLeader: false, // Only non-leader members
      },
      select: { userId: true },
    });

    return assignments.map(assignment => assignment.userId);
  } catch (error) {
    console.error('Error getting task assigned members:', error);
    return [];
  }
}

/**
 * Check if a user can create feedback of a specific type
 */
export async function canCreateFeedback(userId: number, feedbackType: string, targetId?: number): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    if (!user) return false;

    const isOwner = user.userRole.isOwner;
    const isAdmin = user.userRole.isAdmin;
    const isMember = user.userRole.isMember;

    switch (feedbackType) {
      case 'private':
        // Private: owner, admin can create for organization users; member leader can create for department users
        if (isOwner || isAdmin) return true;
        if (isMember) {
          return await isAnyDepartmentLeader(userId);
        }
        return false;

      case 'task':
        // Task: owner, admin, member leader can create for task assigned members
        if (isOwner || isAdmin) return true;
        if (isMember) {
          return await isAnyDepartmentLeader(userId);
        }
        return false;

      case 'department':
        // Department: owner, admin can create for department members
        return isOwner || isAdmin;

      case 'organization':
        // Organization: only owner can create for organization members
        return isOwner;

      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking feedback creation permission:', error);
    return false;
  }
}

/**
 * Check if a user can delete a specific feedback
 */
export async function canDeleteFeedback(userId: number, feedbackId: number): Promise<boolean> {
  try {
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackId },
      select: { createFromId: true },
    });

    if (!feedback) return false;

    // Can delete if user created the feedback
    if (feedback.createFromId === userId) return true;

    // Can delete if user is owner
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    return user?.userRole?.isOwner === true;
  } catch (error) {
    console.error('Error checking feedback deletion permission:', error);
    return false;
  }
}

/**
 * Get users that can be assigned to feedback based on type and user permissions
 */
export async function getAssignableFeedbackUsers(
  userId: number,
  feedbackType: string,
  organizationId?: number,
  departmentId?: number,
  taskId?: number
): Promise<number[]> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { userRole: true },
    });

    if (!user) return [];

    const isOwner = user.userRole.isOwner;
    const isAdmin = user.userRole.isAdmin;
    const isMember = user.userRole.isMember;

    switch (feedbackType) {
      case 'private':
        if (isOwner || isAdmin) {
          // Can assign to users in organization
          return organizationId ? await getOrganizationMembers(organizationId) : [];
        }
        if (isMember && departmentId) {
          // Member leader can assign to users in their department
          const isLeader = await isDepartmentLeader(userId, departmentId);
          return isLeader ? await getDepartmentMembers(departmentId) : [];
        }
        return [];

      case 'task':
        if ((isOwner || isAdmin || (isMember && await isAnyDepartmentLeader(userId))) && taskId) {
          return await getTaskAssignedMembers(taskId);
        }
        return [];

      case 'department':
        if ((isOwner || isAdmin) && departmentId) {
          return await getDepartmentMembers(departmentId);
        }
        return [];

      case 'organization':
        if (isOwner && organizationId) {
          return await getOrganizationMembers(organizationId);
        }
        return [];

      default:
        return [];
    }
  } catch (error) {
    console.error('Error getting assignable feedback users:', error);
    return [];
  }
}
