import axios from 'axios';
import { getCookie } from 'cookies-next';

export interface FileUploadResponse {
  success: boolean;
  data?: {
    url: string;
    key: string;
    fileName: string;
    fileType: string;
    size: number;
    contentType: string;
  };
  error?: string;
}


export interface DeleteFileResponse {
  success: boolean;
  message?: string;
  error?: string;
}

export type FileType = 'images' | 'documents' | 'avatars' | 'chat';

class S3Service {
  private baseUrl = '/api/v1/upload';

  private getAuthHeaders() {
    const token = getCookie('access_token');
    return {
      Authorization: `Bearer ${token}`,
    };
  }

  // Direct file upload
  async uploadFile(file: File, fileType: FileType): Promise<FileUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileType', fileType);

      const response = await axios.post(this.baseUrl, formData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Upload failed',
      };
    }
  }





  // Delete file from S3
  async deleteFile(key: string): Promise<DeleteFileResponse> {
    try {
      const response = await axios.delete(`${this.baseUrl}/delete`, {
        headers: this.getAuthHeaders(),
        data: { key },
      });

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Delete failed',
      };
    }
  }


}

export const s3Service = new S3Service();
