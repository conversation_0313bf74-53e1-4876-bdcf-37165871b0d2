import { prisma } from '@/lib/prisma';
import {
  createCompleteOrganizationChat,
  addAdminToAllChats,
  removeAdminFromAllChats,
  repairOrganizationChatMemberships
} from './organizationChatService';

/**
 * Service for handling chat synchronization operations
 * This service provides high-level functions for managing admin chat access
 */

export interface ChatSyncResult {
  success: boolean;
  message: string;
  details?: {
    added: number;
    removed: number;
    errors: string[];
  };
}

/**
 * Synchronize admin user with organization chats after admin creation
 */
export async function syncAdminChatsOnCreation(
  adminUserId: number,
  organizationIds: number[]
): Promise<ChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      const addResult = await addAdminToAllChats(adminUserId, organizationIds, tx);
      return addResult;
    });

    if (result.errors.length > 0) {
      return {
        success: false,
        message: 'Admin created but some chat operations failed',
        details: {
          added: result.organizationChats + result.departmentChats,
          removed: 0,
          errors: result.errors,
        },
      };
    }

    return {
      success: true,
      message: `Admin successfully added to ${result.organizationChats} organization chat(s) and ${result.departmentChats} department chat(s)`,
      details: {
        added: result.organizationChats + result.departmentChats,
        removed: 0,
        errors: [],
      },
    };
  } catch (error) {
    console.error('Error syncing admin chats on creation:', error);
    return {
      success: false,
      message: 'Failed to sync admin with organization and department chats',
      details: {
        added: 0,
        removed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Synchronize admin user with organization chats after admin removal
 */
export async function syncAdminChatsOnRemoval(
  adminUserId: number,
  organizationIds: number[]
): Promise<ChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      const removeResult = await removeAdminFromAllChats(adminUserId, organizationIds, tx);
      return removeResult;
    });

    if (result.errors.length > 0) {
      return {
        success: false,
        message: 'Admin removed but some chat operations failed',
        details: {
          added: 0,
          removed: result.organizationChats + result.departmentChats,
          errors: result.errors,
        },
      };
    }

    return {
      success: true,
      message: `Admin successfully removed from ${result.organizationChats} organization chat(s) and ${result.departmentChats} department chat(s)`,
      details: {
        added: 0,
        removed: result.organizationChats + result.departmentChats,
        errors: [],
      },
    };
  } catch (error) {
    console.error('Error syncing admin chats on removal:', error);
    return {
      success: false,
      message: 'Failed to remove admin from organization and department chats',
      details: {
        added: 0,
        removed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Repair organization chat memberships for consistency
 * This function can be called periodically or when inconsistencies are detected
 */
export async function repairChatMemberships(organizationId?: number): Promise<ChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await repairOrganizationChatMemberships(organizationId, tx);
    });

    return {
      success: result.errors.length === 0,
      message: result.errors.length === 0 
        ? `Successfully repaired ${result.repaired} organization chat memberships`
        : `Repaired ${result.repaired} memberships with ${result.errors.length} errors`,
      details: {
        added: result.repaired,
        removed: 0,
        errors: result.errors,
      },
    };
  } catch (error) {
    console.error('Error repairing chat memberships:', error);
    return {
      success: false,
      message: 'Failed to repair organization chat memberships',
      details: {
        added: 0,
        removed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Validate organization chat consistency
 * This function checks if all active admins are properly added to organization chats
 */
export async function validateChatConsistency(organizationId?: number): Promise<{
  isConsistent: boolean;
  issues: string[];
  recommendations: string[];
}> {
  try {
    const whereClause: any = {};
    if (organizationId) {
      whereClause.id = organizationId;
    }

    const organizations = await prisma.organization.findMany({
      where: whereClause,
      include: {
        organizationAdmins: {
          where: { isActive: true },
          select: { userId: true },
        },
        chats: {
          where: { chatType: 'ORGANIZATION' },
          include: {
            chatUsers: {
              select: { userId: true, isAdmin: true },
            },
          },
        },
      },
    });

    const issues: string[] = [];
    const recommendations: string[] = [];

    for (const org of organizations) {
      const activeAdminIds = org.organizationAdmins.map(admin => admin.userId);
      const orgChat = org.chats.find(chat => chat.chatType === 'ORGANIZATION');

      if (!orgChat) {
        issues.push(`Organization ${org.name} (ID: ${org.id}) has no organization chat`);
        recommendations.push(`Create organization chat for ${org.name}`);
        continue;
      }

      const chatAdminIds = orgChat.chatUsers
        .filter(cu => cu.isAdmin)
        .map(cu => cu.userId);

      // Check for admins not in chat
      const adminsNotInChat = activeAdminIds.filter(adminId => !chatAdminIds.includes(adminId));
      if (adminsNotInChat.length > 0) {
        issues.push(`Organization ${org.name}: ${adminsNotInChat.length} admin(s) not in chat`);
        recommendations.push(`Add missing admins to ${org.name} chat`);
      }

      // Check for chat admins not in organization admins
      const chatAdminsNotInOrg = chatAdminIds.filter(chatAdminId => !activeAdminIds.includes(chatAdminId));
      if (chatAdminsNotInOrg.length > 0) {
        issues.push(`Organization ${org.name}: ${chatAdminsNotInOrg.length} chat admin(s) not in organization admins`);
        recommendations.push(`Remove invalid chat admins from ${org.name} chat or add them as organization admins`);
      }
    }

    return {
      isConsistent: issues.length === 0,
      issues,
      recommendations,
    };
  } catch (error) {
    console.error('Error validating chat consistency:', error);
    return {
      isConsistent: false,
      issues: ['Failed to validate chat consistency'],
      recommendations: ['Check system logs and try again'],
    };
  }
}

/**
 * Get chat synchronization status for admin management UI
 */
export async function getChatSyncStatus(adminUserId: number, organizationIds: number[]): Promise<{
  organizationId: number;
  organizationName: string;
  chatExists: boolean;
  adminInChat: boolean;
  isAdmin: boolean;
}[]> {
  try {
    const organizations = await prisma.organization.findMany({
      where: {
        id: { in: organizationIds },
      },
      include: {
        chats: {
          where: { chatType: 'ORGANIZATION' },
          include: {
            chatUsers: {
              where: { userId: adminUserId },
            },
          },
        },
      },
    });

    return organizations.map(org => {
      const orgChat = org.chats.find(chat => chat.chatType === 'ORGANIZATION');
      const chatUser = orgChat?.chatUsers[0];

      return {
        organizationId: org.id,
        organizationName: org.name,
        chatExists: !!orgChat,
        adminInChat: !!chatUser,
        isAdmin: chatUser?.isAdmin || false,
      };
    });
  } catch (error) {
    console.error('Error getting chat sync status:', error);
    return organizationIds.map(id => ({
      organizationId: id,
      organizationName: 'Unknown',
      chatExists: false,
      adminInChat: false,
      isAdmin: false,
    }));
  }
}
