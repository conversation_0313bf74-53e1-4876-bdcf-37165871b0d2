import { prisma } from '@/lib/prisma';
import { ChatType, PrismaClient } from '@/generated/prisma';
import { DefaultArgs } from '@/generated/prisma/runtime/library';

export interface CreateOrganizationChatParams {
  organizationId: number;
  organizationName: string;
  creatorUserId: number;
}

export interface CreateDepartmentChatParams {
  departmentId: number;
  departmentName: string;
  organizationId: number;
}

export interface OrganizationChatResult {
  chat: {
    id: number;
    name: string | null;
    chatType: ChatType;
    organizationId: number | null;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    lastMessageAt: Date | null;
  };
  chatUser: {
    id: number;
    chatId: number;
    userId: number;
    joinedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    isAdmin: boolean;
  };
}

/**
 * Creates an organization chat and adds the creator as an admin participant
 * This function should be called within a database transaction
 */
export async function createOrganizationChat(
  params: CreateOrganizationChatParams,
  tx: any = prisma
): Promise<OrganizationChatResult> {
  const { organizationId, organizationName, creatorUserId } = params;

  // Check if organization chat already exists
  const existingChat = await tx.chat.findFirst({
    where: {
      chatType: 'ORGANIZATION',
      organizationId: organizationId,
    },
  });

  if (existingChat) {
    throw new Error(`Organization chat already exists for organization ${organizationName}`);
  }

  // Verify organization exists
  const organization = await tx.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    throw new Error(`Organization with ID ${organizationId} not found`);
  }

  // Verify creator user exists
  const creator = await tx.user.findUnique({
    where: { id: creatorUserId },
  });

  if (!creator) {
    throw new Error(`User with ID ${creatorUserId} not found`);
  }

  // Create the organization chat
  const chat = await tx.chat.create({
    data: {
      name: organizationName,
      chatType: 'ORGANIZATION' as ChatType,
      organizationId: organizationId,
    },
  });

  // Add the creator as an admin participant
  const chatUser = await tx.chatUser.create({
    data: {
      chatId: chat.id,
      userId: creatorUserId,
      isAdmin: true, // Creator is admin
    },
  });

  return {
    chat,
    chatUser,
  };
}

/**
 * Adds all organization members to the organization chat
 * This function gets all users from all departments in the organization
 * and adds them as participants (excluding the creator who is already added)
 */
export async function addOrganizationMembersToChat(
  chatId: number,
  organizationId: number,
  creatorUserId: number,
  tx: any = prisma
): Promise<void> {
  // Get all users in all departments of the organization
  const organizationDepartments = await tx.department.findMany({
    where: { organizationId },
    include: {
      members: {
        select: { userId: true },
      },
    },
  });

  // Collect all unique user IDs from all departments
  const organizationUserIds: number[] = [];
  organizationDepartments.forEach((dept:any) => {
    dept.members.forEach((member:any) => {
      if (member.userId !== creatorUserId) { // Exclude creator as they're already added
        organizationUserIds.push(member.userId);
      }
    });
  });

  // Remove duplicates
  const uniqueUserIds = [...new Set(organizationUserIds)];

  // Add all members to the chat (if there are any)
  if (uniqueUserIds.length > 0) {
    await tx.chatUser.createMany({
      data: uniqueUserIds.map(userId => ({
        chatId,
        userId,
        isAdmin: false, // Regular members are not admins
      })),
      skipDuplicates: true, // Skip if user is already in the chat
    });
  }
}

/**
 * Complete organization chat creation with all members
 * This function should be called within an existing transaction
 */
export async function createCompleteOrganizationChat(
  params: CreateOrganizationChatParams,
  tx: any = prisma
): Promise<OrganizationChatResult> {
  // Create the chat and add creator
  const result = await createOrganizationChat(params, tx);

  // Add all organization members to the chat
  await addOrganizationMembersToChat(
    result.chat.id,
    params.organizationId,
    params.creatorUserId,
    tx
  );

  return result;
}

/**
 * Standalone function that creates organization chat with transaction
 * Use this when calling from outside an existing transaction
 */
export async function createCompleteOrganizationChatStandalone(
  params: CreateOrganizationChatParams
): Promise<OrganizationChatResult> {
  return await prisma.$transaction(async (tx) => {
    return await createCompleteOrganizationChat(params, tx);
  });
}

/**
 * Add an admin user to organization chats
 * This function adds the admin to all organization chats they have access to
 */
export async function addAdminToOrganizationChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  for (const organizationId of organizationIds) {
    try {
      // Find the organization chat
      const organizationChat = await tx.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
      });

      if (!organizationChat) {
        // If no organization chat exists, try to create one
        const organization = await tx.organization.findUnique({
          where: { id: organizationId },
        });

        if (organization) {
          try {
            const chatResult = await createCompleteOrganizationChat({
              organizationId,
              organizationName: organization.name,
              creatorUserId: organization.ownerUserId,
            }, tx);

            // Add the admin to the newly created chat
            await tx.chatUser.create({
              data: {
                chatId: chatResult.chat.id,
                userId: adminUserId,
                isAdmin: true,
              },
            });

            results.success++;
          } catch (createError) {
            results.errors.push(`Failed to create organization chat for organization ${organizationId}: ${createError instanceof Error ? createError.message : 'Unknown error'}`);
          }
        } else {
          results.errors.push(`Organization ${organizationId} not found`);
        }
        continue;
      }

      // Check if admin is already in the chat
      const existingChatUser = await tx.chatUser.findUnique({
        where: {
          chatId_userId: {
            chatId: organizationChat.id,
            userId: adminUserId,
          },
        },
      });

      if (existingChatUser) {
        // Update to ensure they are admin
        if (!existingChatUser.isAdmin) {
          await tx.chatUser.update({
            where: { id: existingChatUser.id },
            data: { isAdmin: true },
          });
        }
      } else {
        // Add admin to the chat
        await tx.chatUser.create({
          data: {
            chatId: organizationChat.id,
            userId: adminUserId,
            isAdmin: true,
          },
        });
      }

      results.success++;
    } catch (error) {
      results.errors.push(`Failed to add admin to organization ${organizationId} chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * Remove an admin user from organization chats
 * This function removes the admin from organization chats they no longer have access to
 */
export async function removeAdminFromOrganizationChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  for (const organizationId of organizationIds) {
    try {
      // Find the organization chat
      const organizationChat = await tx.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
      });

      if (!organizationChat) {
        // No chat exists, nothing to remove
        results.success++;
        continue;
      }

      // Remove admin from the chat
      const deleteResult = await tx.chatUser.deleteMany({
        where: {
          chatId: organizationChat.id,
          userId: adminUserId,
        },
      });

      results.success++;
    } catch (error) {
      results.errors.push(`Failed to remove admin from organization ${organizationId} chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * Add an admin user to all department chats within specified organizations
 * This function adds the admin to all department chats in the organizations they have access to
 */
export async function addAdminToDepartmentChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  for (const organizationId of organizationIds) {
    try {
      // Get all departments in this organization
      const departments = await tx.department.findMany({
        where: {
          organizationId: organizationId,
        },
        select: {
          id: true,
          name: true,
        },
      });

      // Add admin to each department chat
      for (const department of departments) {
        try {
          // Find the department chat
          const departmentChat = await tx.chat.findFirst({
            where: {
              chatType: 'DEPARTMENT',
              departmentId: department.id,
            },
          });

          if (!departmentChat) {
            // If no department chat exists, try to create one
            try {
              const chatResult = await createCompleteDepartmentChat({
                departmentId: department.id,
                departmentName: department.name,
                organizationId: organizationId,
              }, tx);

              // Add the admin to the newly created chat (they should already be added by createCompleteDepartmentChat)
              // But let's ensure they have admin privileges
              const existingChatUser = await tx.chatUser.findUnique({
                where: {
                  chatId_userId: {
                    chatId: chatResult.chat.id,
                    userId: adminUserId,
                  },
                },
              });

              if (!existingChatUser) {
                await tx.chatUser.create({
                  data: {
                    chatId: chatResult.chat.id,
                    userId: adminUserId,
                    isAdmin: true,
                  },
                });
              } else if (!existingChatUser.isAdmin) {
                await tx.chatUser.update({
                  where: { id: existingChatUser.id },
                  data: { isAdmin: true },
                });
              }

              results.success++;
            } catch (createError) {
              results.errors.push(`Failed to create department chat for department ${department.name}: ${createError instanceof Error ? createError.message : 'Unknown error'}`);
            }
            continue;
          }

          // Check if admin is already in the department chat
          const existingChatUser = await tx.chatUser.findUnique({
            where: {
              chatId_userId: {
                chatId: departmentChat.id,
                userId: adminUserId,
              },
            },
          });

          if (existingChatUser) {
            // Update to ensure they are admin
            if (!existingChatUser.isAdmin) {
              await tx.chatUser.update({
                where: { id: existingChatUser.id },
                data: { isAdmin: true },
              });
            }
          } else {
            // Add admin to the department chat
            await tx.chatUser.create({
              data: {
                chatId: departmentChat.id,
                userId: adminUserId,
                isAdmin: true,
              },
            });
          }

          results.success++;
        } catch (error) {
          results.errors.push(`Failed to add admin to department ${department.name} chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      results.errors.push(`Failed to process departments for organization ${organizationId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * Create a department chat and add organization owner and admins as participants
 * This function should be called within a database transaction
 */
export async function createDepartmentChat(
  params: CreateDepartmentChatParams,
  tx: any = prisma
): Promise<OrganizationChatResult> {
  const { departmentId, departmentName, organizationId } = params;

  // Check if department chat already exists
  const existingChat = await tx.chat.findFirst({
    where: {
      chatType: 'DEPARTMENT',
      departmentId: departmentId,
    },
  });

  if (existingChat) {
    throw new Error(`Department chat already exists for department ${departmentName}`);
  }

  // Verify department exists
  const department = await tx.department.findUnique({
    where: { id: departmentId },
    include: {
      organization: true,
    },
  });

  if (!department) {
    throw new Error(`Department with ID ${departmentId} not found`);
  }

  // Create the department chat
  const chat = await tx.chat.create({
    data: {
      name: `${departmentName} (${department.organization.name})`,
      chatType: 'DEPARTMENT' as ChatType,
      departmentId: departmentId,
      organizationId: organizationId,
    },
  });

  // Add organization owner as admin
  const ownerChatUser = await tx.chatUser.create({
    data: {
      chatId: chat.id,
      userId: department.organization.ownerUserId,
      isAdmin: true,
    },
  });

  // Get all active organization admins
  const organizationAdmins = await tx.organizationAdmin.findMany({
    where: {
      organizationId: organizationId,
      isActive: true,
    },
    select: {
      userId: true,
    },
  });

  // Add organization admins to department chat
  for (const admin of organizationAdmins) {
    // Skip if admin is the same as owner
    if (admin.userId !== department.organization.ownerUserId) {
      await tx.chatUser.create({
        data: {
          chatId: chat.id,
          userId: admin.userId,
          isAdmin: true,
        },
      });
    }
  }

  return {
    chat,
    chatUser: ownerChatUser,
  };
}

/**
 * Add a member to organization and department chats
 * This function should be called within a database transaction
 */
export async function addMemberToChats(
  memberId: number,
  departmentId: number,
  organizationId: number,
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  try {
    // Get member user ID
    const member = await tx.departmentMember.findUnique({
      where: { id: memberId },
      select: { userId: true },
    });

    if (!member) {
      results.errors.push(`Member with ID ${memberId} not found`);
      return results;
    }

    const userId = member.userId;

    // Add to organization chat
    try {
      const organizationChat = await tx.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
      });

      if (organizationChat) {
        // Check if user is already in organization chat
        const existingOrgChatUser = await tx.chatUser.findUnique({
          where: {
            chatId_userId: {
              chatId: organizationChat.id,
              userId: userId,
            },
          },
        });

        if (!existingOrgChatUser) {
          await tx.chatUser.create({
            data: {
              chatId: organizationChat.id,
              userId: userId,
              isAdmin: false, // Members are not admins
            },
          });
          results.success++;
        }
      } else {
        results.errors.push(`Organization chat not found for organization ${organizationId}`);
      }
    } catch (error) {
      results.errors.push(`Failed to add member to organization chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Add to department chat
    try {
      const departmentChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: departmentId,
        },
      });

      if (departmentChat) {
        // Check if user is already in department chat
        const existingDeptChatUser = await tx.chatUser.findUnique({
          where: {
            chatId_userId: {
              chatId: departmentChat.id,
              userId: userId,
            },
          },
        });

        if (!existingDeptChatUser) {
          await tx.chatUser.create({
            data: {
              chatId: departmentChat.id,
              userId: userId,
              isAdmin: false, // Members are not admins
            },
          });
          results.success++;
        }
      } else {
        results.errors.push(`Department chat not found for department ${departmentId}`);
      }
    } catch (error) {
      results.errors.push(`Failed to add member to department chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return results;
  } catch (error) {
    results.errors.push(`Failed to add member to chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return results;
  }
}

/**
 * Handle member department change by removing from old department chat and adding to new one
 * This function should be called within a database transaction
 */
export async function handleMemberDepartmentChange(
  userId: number,
  oldDepartmentId: number,
  newDepartmentId: number,
  tx: any = prisma
): Promise<{ success: boolean; errors: string[] }> {
  const errors: string[] = [];

  try {
    // Remove from old department chat
    if (oldDepartmentId) {
      const oldDepartmentChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: oldDepartmentId,
        },
      });

      if (oldDepartmentChat) {
        await tx.chatUser.deleteMany({
          where: {
            chatId: oldDepartmentChat.id,
            userId: userId,
          },
        });
      }
    }

    // Add to new department chat
    if (newDepartmentId) {
      const newDepartmentChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: newDepartmentId,
        },
      });

      if (newDepartmentChat) {
        // Check if user is already in new department chat
        const existingChatUser = await tx.chatUser.findUnique({
          where: {
            chatId_userId: {
              chatId: newDepartmentChat.id,
              userId: userId,
            },
          },
        });

        if (!existingChatUser) {
          await tx.chatUser.create({
            data: {
              chatId: newDepartmentChat.id,
              userId: userId,
              isAdmin: false, // Members are not admins
            },
          });
        }
      } else {
        errors.push(`Department chat not found for new department ${newDepartmentId}`);
      }
    }

    return { success: errors.length === 0, errors };
  } catch (error) {
    errors.push(`Failed to handle member department change: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false, errors };
  }
}

/**
 * Remove member from all chats (organization and department)
 * This function should be called within a database transaction
 */
export async function removeMemberFromChats(
  userId: number,
  departmentId: number,
  organizationId: number,
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  try {
    // Remove from organization chat
    try {
      const organizationChat = await tx.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
      });

      if (organizationChat) {
        const deleteResult = await tx.chatUser.deleteMany({
          where: {
            chatId: organizationChat.id,
            userId: userId,
          },
        });
        if (deleteResult.count > 0) {
          results.success++;
        }
      }
    } catch (error) {
      results.errors.push(`Failed to remove member from organization chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Remove from department chat
    try {
      const departmentChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: departmentId,
        },
      });

      if (departmentChat) {
        const deleteResult = await tx.chatUser.deleteMany({
          where: {
            chatId: departmentChat.id,
            userId: userId,
          },
        });
        if (deleteResult.count > 0) {
          results.success++;
        }
      }
    } catch (error) {
      results.errors.push(`Failed to remove member from department chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return results;
  } catch (error) {
    results.errors.push(`Failed to remove member from chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return results;
  }
}

/**
 * Complete department creation with chat setup
 * This function creates department chat and adds organization owner and admins
 */
export async function createCompleteDepartmentChat(
  params: CreateDepartmentChatParams,
  tx: any = prisma
): Promise<OrganizationChatResult> {
  return await createDepartmentChat(params, tx);
}

/**
 * Standalone function that creates department chat with transaction
 * Use this when calling from outside an existing transaction
 */
export async function createCompleteDepartmentChatStandalone(
  params: CreateDepartmentChatParams
): Promise<OrganizationChatResult> {
  return await prisma.$transaction(async (tx) => {
    return await createCompleteDepartmentChat(params, tx);
  });
}

/**
 * Add member to chats with user ID directly
 * This is a helper function for when you have the user ID instead of member ID
 */
export async function addUserToChats(
  userId: number,
  departmentId: number,
  organizationId: number,
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  try {
    // Add to organization chat
    try {
      const organizationChat = await tx.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
      });

      if (organizationChat) {
        // Check if user is already in organization chat
        const existingOrgChatUser = await tx.chatUser.findUnique({
          where: {
            chatId_userId: {
              chatId: organizationChat.id,
              userId: userId,
            },
          },
        });

        if (!existingOrgChatUser) {
          await tx.chatUser.create({
            data: {
              chatId: organizationChat.id,
              userId: userId,
              isAdmin: false, // Members are not admins
            },
          });
          results.success++;
        }
      } else {
        results.errors.push(`Organization chat not found for organization ${organizationId}`);
      }
    } catch (error) {
      results.errors.push(`Failed to add user to organization chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Add to department chat
    try {
      const departmentChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: departmentId,
        },
      });

      if (departmentChat) {
        // Check if user is already in department chat
        const existingDeptChatUser = await tx.chatUser.findUnique({
          where: {
            chatId_userId: {
              chatId: departmentChat.id,
              userId: userId,
            },
          },
        });

        if (!existingDeptChatUser) {
          await tx.chatUser.create({
            data: {
              chatId: departmentChat.id,
              userId: userId,
              isAdmin: false, // Members are not admins
            },
          });
          results.success++;
        }
      } else {
        results.errors.push(`Department chat not found for department ${departmentId}`);
      }
    } catch (error) {
      results.errors.push(`Failed to add user to department chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return results;
  } catch (error) {
    results.errors.push(`Failed to add user to chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return results;
  }
}

/**
 * Remove an admin user from all department chats within specified organizations
 * This function removes the admin from department chats they no longer have access to
 */
export async function removeAdminFromDepartmentChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  for (const organizationId of organizationIds) {
    try {
      // Get all departments in this organization
      const departments = await tx.department.findMany({
        where: {
          organizationId: organizationId,
        },
        select: {
          id: true,
          name: true,
        },
      });

      // Remove admin from each department chat
      for (const department of departments) {
        try {
          // Find the department chat
          const departmentChat = await tx.chat.findFirst({
            where: {
              chatType: 'DEPARTMENT',
              departmentId: department.id,
            },
          });

          if (!departmentChat) {
            // No chat exists, nothing to remove
            continue;
          }

          // Remove admin from the department chat
          const deleteResult = await tx.chatUser.deleteMany({
            where: {
              chatId: departmentChat.id,
              userId: adminUserId,
            },
          });

          if (deleteResult.count > 0) {
            results.success++;
          }
        } catch (error) {
          results.errors.push(`Failed to remove admin from department ${department.name} chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    } catch (error) {
      results.errors.push(`Failed to process departments for organization ${organizationId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * Add admin to both organization and department chats
 * This is the main function to use for admin chat synchronization
 */
export async function addAdminToAllChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ organizationChats: number; departmentChats: number; errors: string[] }> {
  const results = { organizationChats: 0, departmentChats: 0, errors: [] as string[] };

  try {
    // Add to organization chats
    const orgResult = await addAdminToOrganizationChats(adminUserId, organizationIds, tx);
    results.organizationChats = orgResult.success;
    results.errors.push(...orgResult.errors);

    // Add to department chats
    const deptResult = await addAdminToDepartmentChats(adminUserId, organizationIds, tx);
    results.departmentChats = deptResult.success;
    results.errors.push(...deptResult.errors);

    return results;
  } catch (error) {
    results.errors.push(`Failed to add admin to all chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return results;
  }
}

/**
 * Remove admin from both organization and department chats
 * This is the main function to use for admin chat cleanup
 */
export async function removeAdminFromAllChats(
  adminUserId: number,
  organizationIds: number[],
  tx: any = prisma
): Promise<{ organizationChats: number; departmentChats: number; errors: string[] }> {
  const results = { organizationChats: 0, departmentChats: 0, errors: [] as string[] };

  try {
    // Remove from organization chats
    const orgResult = await removeAdminFromOrganizationChats(adminUserId, organizationIds, tx);
    results.organizationChats = orgResult.success;
    results.errors.push(...orgResult.errors);

    // Remove from department chats
    const deptResult = await removeAdminFromDepartmentChats(adminUserId, organizationIds, tx);
    results.departmentChats = deptResult.success;
    results.errors.push(...deptResult.errors);

    return results;
  } catch (error) {
    results.errors.push(`Failed to remove admin from all chats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return results;
  }
}

/**
 * Synchronize admin user's organization chat access
 * This function ensures the admin has access to chats for organizations they manage
 * and removes access from organizations they no longer manage
 */
export async function synchronizeAdminOrganizationChats(
  adminUserId: number,
  newOrganizationIds: number[],
  tx: any = prisma
): Promise<{ added: number; removed: number; errors: string[] }> {
  // Get current organization assignments for this admin
  const currentAssignments = await tx.organizationAdmin.findMany({
    where: {
      userId: adminUserId,
      isActive: true,
    },
    select: {
      organizationId: true,
    },
  });

  const currentOrganizationIds = currentAssignments.map((assignment:any) => assignment.organizationId);

  // Determine which organizations to add and remove
  const organizationsToAdd = newOrganizationIds.filter(id => !currentOrganizationIds.includes(id));
  const organizationsToRemove = currentOrganizationIds.filter((id:any) => !newOrganizationIds.includes(id));

  const results = { added: 0, removed: 0, errors: [] as string[] };

  // Add admin to new organization chats
  if (organizationsToAdd.length > 0) {
    const addResult = await addAdminToOrganizationChats(adminUserId, organizationsToAdd, tx);
    results.added = addResult.success;
    results.errors.push(...addResult.errors);
  }

  // Remove admin from old organization chats
  if (organizationsToRemove.length > 0) {
    const removeResult = await removeAdminFromOrganizationChats(adminUserId, organizationsToRemove, tx);
    results.removed = removeResult.success;
    results.errors.push(...removeResult.errors);
  }

  return results;
}

/**
 * Ensure organization chat exists and add admin if needed
 * This is a helper function that creates the chat if it doesn't exist
 */
export async function ensureOrganizationChatAndAddAdmin(
  organizationId: number,
  adminUserId: number,
  tx: any = prisma
): Promise<{ chatId: number; created: boolean; error?: string }> {
  try {
    // Validate input parameters
    if (!organizationId || !adminUserId) {
      return { chatId: 0, created: false, error: 'Organization ID and admin user ID are required' };
    }

    // Check if organization chat exists
    let organizationChat = await tx.chat.findFirst({
      where: {
        chatType: 'ORGANIZATION',
        organizationId: organizationId,
      },
    });

    let created = false;

    // Create chat if it doesn't exist
    if (!organizationChat) {
      const organization = await tx.organization.findUnique({
        where: { id: organizationId },
      });

      if (!organization) {
        return { chatId: 0, created: false, error: `Organization ${organizationId} not found` };
      }

      try {
        const chatResult = await createCompleteOrganizationChat({
          organizationId,
          organizationName: organization.name,
          creatorUserId: organization.ownerUserId,
        }, tx);

        organizationChat = chatResult.chat;
        created = true;
      } catch (createError) {
        return {
          chatId: 0,
          created: false,
          error: `Failed to create organization chat: ${createError instanceof Error ? createError.message : 'Unknown error'}`
        };
      }
    }

    // Add admin to chat if not already present
    try {
      const existingChatUser = await tx.chatUser.findUnique({
        where: {
          chatId_userId: {
            chatId: organizationChat.id,
            userId: adminUserId,
          },
        },
      });

      if (!existingChatUser) {
        await tx.chatUser.create({
          data: {
            chatId: organizationChat.id,
            userId: adminUserId,
            isAdmin: true,
          },
        });
      } else if (!existingChatUser.isAdmin) {
        // Ensure they have admin privileges
        await tx.chatUser.update({
          where: { id: existingChatUser.id },
          data: { isAdmin: true },
        });
      }
    } catch (userError) {
      return {
        chatId: organizationChat.id,
        created,
        error: `Chat exists but failed to add admin: ${userError instanceof Error ? userError.message : 'Unknown error'}`
      };
    }

    return { chatId: organizationChat.id, created };
  } catch (error) {
    return {
      chatId: 0,
      created: false,
      error: `Failed to ensure organization chat: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Bulk synchronize multiple admin users with their organization chats
 * This is useful for batch operations or system maintenance
 */
export async function bulkSynchronizeAdminChats(
  adminUserIds: number[],
  tx: any = prisma
): Promise<{ success: number; errors: string[] }> {
  const results = { success: 0, errors: [] as string[] };

  for (const adminUserId of adminUserIds) {
    try {
      // Get current organization assignments for this admin
      const assignments = await tx.organizationAdmin.findMany({
        where: {
          userId: adminUserId,
          isActive: true,
        },
        select: {
          organizationId: true,
        },
      });

      const organizationIds = assignments.map((assignment:any) => assignment.organizationId);

      if (organizationIds.length > 0) {
        const syncResult = await synchronizeAdminOrganizationChats(adminUserId, organizationIds, tx);
        if (syncResult.errors.length > 0) {
          results.errors.push(...syncResult.errors.map(err => `Admin ${adminUserId}: ${err}`));
        } else {
          results.success++;
        }
      } else {
        results.success++; // No organizations to sync, consider it successful
      }
    } catch (error) {
      results.errors.push(`Failed to sync admin ${adminUserId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return results;
}

/**
 * Get organization chat information for admin management UI
 * This helps the UI show which chats an admin has access to
 */
export async function getAdminOrganizationChats(
  adminUserId: number,
  organizationIds?: number[],
  tx: any = prisma
): Promise<Array<{
  organizationId: number;
  organizationName: string;
  chatId: number | null;
  chatName: string | null;
  isParticipant: boolean;
  isAdmin: boolean;
}>> {
  // Build where clause for organizations
  const whereClause: any = {};
  if (organizationIds && organizationIds.length > 0) {
    whereClause.id = { in: organizationIds };
  }

  // Get organizations and their chats
  const organizations = await tx.organization.findMany({
    where: whereClause,
    include: {
      chats: {
        where: {
          chatType: 'ORGANIZATION',
        },
        include: {
          chatUsers: {
            where: {
              userId: adminUserId,
            },
          },
        },
      },
    },
  });

  return organizations.map((org: any) => {
    const orgChat = org.chats.find((chat: any) => chat.chatType === 'ORGANIZATION');
    const chatUser = orgChat?.chatUsers[0];

    return {
      organizationId: org.id,
      organizationName: org.name,
      chatId: orgChat?.id || null,
      chatName: orgChat?.name || null,
      isParticipant: !!chatUser,
      isAdmin: chatUser?.isAdmin || false,
    };
  });
}

/**
 * Repair organization chat memberships
 * This function ensures all active admin users are properly added to organization chats
 * Useful for data migration or fixing inconsistencies
 */
export async function repairOrganizationChatMemberships(
  organizationId?: number,
  tx: any = prisma
): Promise<{ repaired: number; errors: string[] }> {
  const results = { repaired: 0, errors: [] as string[] };

  try {
    // Get organizations to repair
    const whereClause: any = {};
    if (organizationId) {
      whereClause.id = organizationId;
    }

    const organizations = await tx.organization.findMany({
      where: whereClause,
      include: {
        organizationAdmins: {
          where: {
            isActive: true,
          },
          select: {
            userId: true,
          },
        },
        chats: {
          where: {
            chatType: 'ORGANIZATION',
          },
        },
      },
    });

    for (const org of organizations) {
      try {
        const orgChat = org.chats.find((chat: any) => chat.chatType === 'ORGANIZATION');

        if (!orgChat) {
          // Create organization chat if it doesn't exist
          const chatResult = await createCompleteOrganizationChat({
            organizationId: org.id,
            organizationName: org.name,
            creatorUserId: org.ownerUserId,
          }, tx);

          // Add all admins to the newly created chat
          const adminUserIds = org.organizationAdmins.map((admin: any) => admin.userId);
          if (adminUserIds.length > 0) {
            const addResult = await addAdminToOrganizationChats(org.ownerUserId, [org.id], tx);
            for (const adminUserId of adminUserIds) {
              if (adminUserId !== org.ownerUserId) {
                await addAdminToOrganizationChats(adminUserId, [org.id], tx);
              }
            }
          }

          results.repaired++;
        } else {
          // Ensure all active admins are in the chat
          const adminUserIds = org.organizationAdmins.map((admin: any) => admin.userId);
          for (const adminUserId of adminUserIds) {
            const addResult = await addAdminToOrganizationChats(adminUserId, [org.id], tx);
            if (addResult.success > 0) {
              results.repaired++;
            }
          }
        }
      } catch (error) {
        results.errors.push(`Failed to repair organization ${org.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  } catch (error) {
    results.errors.push(`Failed to repair organization chat memberships: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return results;
}
