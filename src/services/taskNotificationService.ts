import { prisma } from '@/lib/prisma';

/**
 * Service for handling task-related notifications
 */
export const taskNotificationService = {
  /**
   * Create a notification when a task is assigned to a user
   */
  async notifyTaskAssigned(
    taskId: number,
    taskTitle: string,
    assignedToUserId: number,
    assignedByUserId: number
  ) {
    try {
      // Find or create the notification type
      let notificationType = await prisma.notificationType.findFirst({
        where: { name: 'task_assigned' },
      });

      if (!notificationType) {
        notificationType = await prisma.notificationType.create({
          data: {
            name: 'task_assigned',
            displayName: 'Task Assigned',
            description: 'Notification sent when a task is assigned to a user',
            template: 'You have been assigned a new task: {{taskTitle}}',
            color: '#4F46E5', // Indigo color
            isActive: true,
          },
        });
      }

      // Get the assigner's name
      const assigner = await prisma.user.findUnique({
        where: { id: assignedByUserId },
        select: { firstName: true, lastName: true },
      });

      const assignerName = assigner ? `${assigner.firstName} ${assigner.lastName}` : 'Someone';

      // Create notification and link to user in a single transaction
      const result = await prisma.$transaction(async tx => {
        // Create the notification
        const notification = await tx.notification.create({
          data: {
            typeId: notificationType.id,
            title: 'New Task Assigned',
            content: `${assignerName} assigned you a new task: ${taskTitle}`,
            data: { taskId, taskTitle },
            entityType: 'task',
            entityId: taskId,
          },
        });

        // Check if a user notification already exists
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: assignedToUserId,
            notificationId: notification.id,
          },
        });

        // Only create if it doesn't exist
        if (!existingUserNotification) {
          await tx.userNotification.create({
            data: {
              userId: assignedToUserId,
              notificationId: notification.id,
            },
          });
        }

        return notification;
      });

      return result;
    } catch (error) {
      console.error('Error creating task assigned notification:', error);
      // Return null instead of throwing to prevent API failures
      return null;
    }
  },

  /**
   * Create a notification when a task status is changed
   */
  async notifyTaskStatusChanged(
    taskId: number,
    taskTitle: string,
    assignedToUserId: number,
    updatedByUserId: number,
    oldStatusName: string,
    newStatusName: string
  ) {
    try {
      // Find or create the notification type
      let notificationType = await prisma.notificationType.findFirst({
        where: { name: 'task_status_changed' },
      });

      if (!notificationType) {
        notificationType = await prisma.notificationType.create({
          data: {
            name: 'task_status_changed',
            displayName: 'Task Status Changed',
            description: 'Notification sent when a task status is changed',
            template:
              'Your task "{{taskTitle}}" status has been changed from {{oldStatus}} to {{newStatus}}',
            color: '#F59E0B', // Amber color
            isActive: true,
          },
        });
      }

      // Get the updater's name
      const updater = await prisma.user.findUnique({
        where: { id: updatedByUserId },
        select: { firstName: true, lastName: true },
      });

      const updaterName = updater ? `${updater.firstName} ${updater.lastName}` : 'Someone';

      // Create notification and link to user in a single transaction
      const result = await prisma.$transaction(async tx => {
        // Create the notification
        const notification = await tx.notification.create({
          data: {
            typeId: notificationType.id,
            title: 'Task Status Updated',
            content: `${updaterName} changed your task "${taskTitle}" status from ${oldStatusName} to ${newStatusName}`,
            data: {
              taskId,
              taskTitle,
              oldStatus: oldStatusName,
              newStatus: newStatusName,
            },
            entityType: 'task',
            entityId: taskId,
          },
        });

        // Check if a user notification already exists
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: assignedToUserId,
            notificationId: notification.id,
          },
        });

        // Only create if it doesn't exist
        if (!existingUserNotification) {
          await tx.userNotification.create({
            data: {
              userId: assignedToUserId,
              notificationId: notification.id,
            },
          });
        }

        return notification;
      });

      return result;
    } catch (error) {
      console.error('Error creating task status changed notification:', error);
      // Return null instead of throwing to prevent API failures
      return null;
    }
  },

  /**
   * Create a notification when a task is completed
   */
  async notifyTaskCompleted(
    taskId: number,
    taskTitle: string,
    assignedToUserId: number,
    completedByUserId: number
  ) {
    try {
      // Find or create the notification type
      let notificationType = await prisma.notificationType.findFirst({
        where: { name: 'task_completed' },
      });

      if (!notificationType) {
        notificationType = await prisma.notificationType.create({
          data: {
            name: 'task_completed',
            displayName: 'Task Completed',
            description: 'Notification sent when a task is marked as completed',
            template: 'Your task "{{taskTitle}}" has been marked as completed',
            color: '#10B981', // Emerald color
            isActive: true,
          },
        });
      }

      // Get the completer's name
      const completer = await prisma.user.findUnique({
        where: { id: completedByUserId },
        select: { firstName: true, lastName: true },
      });

      const completerName = completer ? `${completer.firstName} ${completer.lastName}` : 'Someone';

      // Create notification and link to user in a single transaction
      const result = await prisma.$transaction(async tx => {
        // Create the notification
        const notification = await tx.notification.create({
          data: {
            typeId: notificationType.id,
            title: 'Task Completed',
            content: `${completerName} marked your task "${taskTitle}" as completed`,
            data: { taskId, taskTitle },
            entityType: 'task',
            entityId: taskId,
          },
        });

        // Check if a user notification already exists
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: assignedToUserId,
            notificationId: notification.id,
          },
        });

        // Only create if it doesn't exist
        if (!existingUserNotification) {
          await tx.userNotification.create({
            data: {
              userId: assignedToUserId,
              notificationId: notification.id,
            },
          });
        }

        return notification;
      });

      return result;
    } catch (error) {
      console.error('Error creating task completed notification:', error);
      // Return null instead of throwing to prevent API failures
      return null;
    }
  },
  /**
   * Create a notification when a comment is added to task progress
   */
  async notifyTaskProgressComment(
    taskId: number,
    taskTitle: string,
    commentContent: string,
    recipientUserId: number,
    commentedByUserId: number
  ) {
    try {
      // Find the notification type
      const notificationType = await prisma.notificationType.findFirst({
        where: { name: 'task_progress_comment' },
      });

      if (!notificationType) {
        console.error('Notification type task_progress_comment not found');
        return null;
      }

      // Get the commenter's name
      const commenter = await prisma.user.findUnique({
        where: { id: commentedByUserId },
        select: { firstName: true, lastName: true },
      });

      const commenterName = commenter ? `${commenter.firstName} ${commenter.lastName}` : 'Someone';

      // Create notification and link to user in a single transaction
      const result = await prisma.$transaction(async tx => {
        // Create the notification
        const notification = await tx.notification.create({
          data: {
            typeId: notificationType.id,
            title: 'New Comment on Task',
            content: `${commenterName} commented on task "${taskTitle}": ${commentContent.substring(0, 50)}${commentContent.length > 50 ? '...' : ''}`,
            data: { taskId, taskTitle, commentContent },
            entityType: 'task',
            entityId: taskId,
          },
        });

        // Check if a user notification already exists
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: recipientUserId,
            notificationId: notification.id,
          },
        });

        // Only create if it doesn't exist
        if (!existingUserNotification) {
          await tx.userNotification.create({
            data: {
              userId: recipientUserId,
              notificationId: notification.id,
            },
          });
        }

        return notification;
      });

      return result;
    } catch (error) {
      console.error('Error creating task progress comment notification:', error);
      // Return null instead of throwing to prevent API failures
      return null;
    }
  },

  /**
   * Create a notification when a progress update is requested for a task
   */
  async notifyTaskProgressRequest(
    taskId: number,
    taskTitle: string,
    requestContent: string,
    recipientUserId: number,
    requestedByUserId: number
  ) {
    try {
      // Find the notification type
      const notificationType = await prisma.notificationType.findFirst({
        where: { name: 'task_progress_request' },
      });

      if (!notificationType) {
        console.error('Notification type task_progress_request not found');
        return null;
      }

      // Get the requester's name
      const requester = await prisma.user.findUnique({
        where: { id: requestedByUserId },
        select: { firstName: true, lastName: true },
      });

      const requesterName = requester ? `${requester.firstName} ${requester.lastName}` : 'Someone';

      // Create notification and link to user in a single transaction
      const result = await prisma.$transaction(async tx => {
        // Create the notification
        const notification = await tx.notification.create({
          data: {
            typeId: notificationType.id,
            title: 'Progress Update Requested',
            content: `${requesterName} requested a progress update for task "${taskTitle}"`,
            data: { taskId, taskTitle, requestContent },
            entityType: 'task',
            entityId: taskId,
          },
        });

        // Check if a user notification already exists
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: recipientUserId,
            notificationId: notification.id,
          },
        });

        // Only create if it doesn't exist
        if (!existingUserNotification) {
          await tx.userNotification.create({
            data: {
              userId: recipientUserId,
              notificationId: notification.id,
            },
          });
        }

        return notification;
      });

      return result;
    } catch (error) {
      console.error('Error creating task progress request notification:', error);
      // Return null instead of throwing to prevent API failures
      return null;
    }
  },
};
