import { getCookie } from 'cookies-next';

const API_BASE = '/api/v1';

// Helper to get auth token
const getAuthToken = () => {
  return getCookie('access_token');
};

// Helper to make authenticated requests
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const token = getAuthToken();

  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
  });

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }

  return response.json();
};

// Feedback Types API
export const feedbackTypeApi = {
  // Get all feedback types (master data)
  getFeedbackTypes: async () => {
    return apiRequest('/feedback-type');
  },
};

// Feedback API functions
export const feedbackApi = {
  // Get feedback records with different views
  getFeedback: async (params?: {
    id?: number;
    view?: 'my' | 'all';
    organizationId?: number;
    departmentId?: number;
  }) => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    const queryString = searchParams.toString();
    return apiRequest(`/feedback${queryString ? `?${queryString}` : ''}`);
  },

  // Get single feedback by ID
  getFeedbackById: async (id: number) => {
    return apiRequest(`/feedback?id=${id}`);
  },

  // Get user's own feedback (created, assigned, shared)
  getMyFeedback: async () => {
    return apiRequest('/feedback?view=my');
  },

  // Get all feedback for organization (admin view)
  getOrganizationFeedback: async (organizationId: number) => {
    return apiRequest(`/feedback?view=all&organizationId=${organizationId}`);
  },

  // Get all feedback for department (leader view)
  getDepartmentFeedback: async (departmentId: number) => {
    return apiRequest(`/feedback?view=all&departmentId=${departmentId}`);
  },

  // Create new feedback
  createFeedback: async (data: {
    feedbackTypeId: number;
    situation?: string;
    behavior?: string;
    impact?: string;
    actionable?: string;
    appreciation?: string;
    growthToken?: number;
    userIds: number[];
    organizationId?: number;
    departmentId?: number;
    taskId?: number;
  }) => {
    return apiRequest('/feedback', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update existing feedback
  updateFeedback: async (data: {
    id: number;
    situation?: string;
    behavior?: string;
    impact?: string;
    actionable?: string;
    appreciation?: string;
    growthToken?: number;
    userIds?: number[];
    organizationId?: number;
    departmentId?: number;
    taskId?: number;
  }) => {
    return apiRequest('/feedback', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete feedback
  deleteFeedback: async (id: number) => {
    return apiRequest(`/feedback?id=${id}`, {
      method: 'DELETE',
    });
  },
};

// Feedback User API functions
export const feedbackUserApi = {
  // Get users assigned to feedback
  getFeedbackUsers: async (feedbackId: number) => {
    return apiRequest(`/feedback-user?feedbackId=${feedbackId}`);
  },

  // Get specific user feedback details
  getFeedbackUserDetails: async (feedbackId: number, userId: number) => {
    return apiRequest(`/feedback-user?feedbackId=${feedbackId}&userId=${userId}`);
  },

  // Update user's feedback status
  updateFeedbackUserStatus: async (data: {
    feedbackId: number;
    userId: number;
    isAccept?: boolean;
    isDiscard?: boolean;
    reflection?: string;
    isShare?: boolean;
  }) => {
    return apiRequest('/feedback-user', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Add users to existing feedback
  addUsersToFeedback: async (data: {
    feedbackId: number;
    userIds: number[];
  }) => {
    return apiRequest('/feedback-user', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Remove user from feedback
  removeUserFromFeedback: async (feedbackId: number, userId: number) => {
    return apiRequest(`/feedback-user?feedbackId=${feedbackId}&userId=${userId}`, {
      method: 'DELETE',
    });
  },
};

// User Search API for feedback recipients
export const feedbackUserSearchApi = {
  // Search for users who can receive private feedback
  searchUsers: async (params: {
    organizationId?: string;
    departmentId?: string;
    name?: string;
  }) => {
    const searchParams = new URLSearchParams();

    if (params.organizationId) {
      searchParams.append('organizationId', params.organizationId);
    }
    if (params.departmentId) {
      searchParams.append('departmentId', params.departmentId);
    }
    if (params.name?.trim()) {
      searchParams.append('name', params.name.trim());
    }

    const queryString = searchParams.toString();
    return apiRequest(`/task-assignment-users${queryString ? `?${queryString}` : ''}`);
  },
};

// Combined feedback service with helper functions
export const feedbackService = {
  // Initialize feedback data for a user
  initializeFeedbackData: async () => {
    try {
      const [feedbackTypes, myFeedback] = await Promise.all([
        feedbackTypeApi.getFeedbackTypes(),
        feedbackApi.getMyFeedback(),
      ]);

      return {
        feedbackTypes: feedbackTypes.feedbackTypes || [],
        myFeedback: myFeedback || { created: [], assigned: [], shared: [] },
      };
    } catch (error) {
      console.error('Failed to initialize feedback data:', error);
      throw error;
    }
  },

  // Accept feedback with reflection
  acceptFeedback: async (feedbackId: number, userId: number, reflection?: string, isShare?: boolean) => {
    return feedbackUserApi.updateFeedbackUserStatus({
      feedbackId,
      userId,
      isAccept: true,
      isDiscard: false,
      reflection,
      isShare,
    });
  },

  // Discard feedback
  discardFeedback: async (feedbackId: number, userId: number) => {
    return feedbackUserApi.updateFeedbackUserStatus({
      feedbackId,
      userId,
      isAccept: false,
      isDiscard: true,
    });
  },

  // Share feedback reflection
  shareFeedbackReflection: async (feedbackId: number, userId: number, isShare: boolean) => {
    return feedbackUserApi.updateFeedbackUserStatus({
      feedbackId,
      userId,
      isShare,
    });
  },

  // Get feedback statistics for a user
  getFeedbackStats: async () => {
    try {
      const myFeedback = await feedbackApi.getMyFeedback();
      
      const stats = {
        totalReceived: myFeedback.assigned?.length || 0,
        totalGiven: myFeedback.created?.length || 0,
        totalShared: myFeedback.shared?.length || 0,
        acceptedCount: myFeedback.assigned?.filter((f: any) => 
          f.feedbackUsers?.some((fu: any) => fu.isAccept === true)
        ).length || 0,
        pendingCount: myFeedback.assigned?.filter((f: any) => 
          f.feedbackUsers?.some((fu: any) => fu.isAccept === null)
        ).length || 0,
      };

      return stats;
    } catch (error) {
      console.error('Failed to get feedback stats:', error);
      throw error;
    }
  },

  // Format feedback for display
  formatFeedbackForDisplay: (feedback: any) => {
    return {
      id: feedback.id,
      type: feedback.feedbackType?.displayName || feedback.feedbackType?.name || 'Unknown',
      situation: feedback.situation || '',
      behavior: feedback.behavior || '',
      impact: feedback.impact || '',
      actionable: feedback.actionable || '',
      appreciation: feedback.appreciation || '',
      growthToken: feedback.growthToken || 0,
      createdAt: feedback.createdAt,
      updatedAt: feedback.updatedAt,
      creator: feedback.createFrom ? {
        id: feedback.createFrom.id,
        name: `${feedback.createFrom.firstName} ${feedback.createFrom.lastName}`,
        email: feedback.createFrom.email,
      } : null,
      assignedUsers: feedback.feedbackUsers?.map((fu: any) => ({
        id: fu.user.id,
        name: `${fu.user.firstName} ${fu.user.lastName}`,
        email: fu.user.email,
        isAccept: fu.isAccept,
        isDiscard: fu.isDiscard,
        reflection: fu.reflection,
        isShare: fu.isShare,
      })) || [],
    };
  },
};
