import { chatApi } from './chatService';

// Helper function to create a task chat from task detail page
export const createTaskChat = async (taskId: number, taskTitle: string) => {
  try {
    // Check if task chat already exists
    const existingChat = await chatApi.checkChatExists({
      chatType: 'task',
      taskId,
    });

    if (existingChat) {
      return {
        success: true,
        chat: existingChat,
        isNew: false,
        message: 'Task chat already exists',
      };
    }

    // Create new task chat
    const response = await chatApi.createChat({
      chatType: 'task',
      taskId,
      name: `${taskTitle} - Task Chat`,
    });

    return {
      success: true,
      chat: response.chat,
      isNew: true,
      message: 'Task chat created successfully',
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to create task chat',
    };
  }
};

// Helper function to navigate to chat page with specific chat selected
export const navigateToChatWithTask = (router: any, taskId: number, taskTitle: string) => {
  // Store the task info in sessionStorage to be picked up by chat page
  sessionStorage.setItem(
    'openTaskChat',
    JSON.stringify({
      taskId,
      taskTitle,
      timestamp: Date.now(),
    })
  );

  // Navigate to chat page
  router.push('/chat?tab=task');
};
