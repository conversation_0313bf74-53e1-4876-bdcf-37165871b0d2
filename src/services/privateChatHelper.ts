import { chatApi } from './chatService';

// Helper function to create or find a private chat
export const createPrivateChat = async (
  currentUserId: number,
  targetUserId: number,
  targetUserName: string
) => {
  try {
    // Check if private chat already exists
    const existingChat = await chatApi.checkChatExists({
      chatType: 'private',
      participantIds: [currentUserId, targetUserId],
    });

    if (existingChat) {
      return {
        success: true,
        chat: existingChat,
        isNew: false,
        message: 'Private chat already exists',
      };
    }

    // Create new private chat
    const response = await chatApi.createChat({
      chatType: 'private',
      participantIds: [currentUserId, targetUserId],
      name: `Chat with ${targetUserName}`,
    });

    return {
      success: true,
      chat: response.chat,
      isNew: true,
      message: 'Private chat created successfully',
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Failed to create private chat',
    };
  }
};

// Helper function to navigate to chat page with specific private chat selected
export const navigateToPrivateChat = (
  router: any,
  currentUserId: number,
  targetUserId: number,
  targetUserName: string
) => {
  // Store the private chat info in sessionStorage to be picked up by chat page
  sessionStorage.setItem(
    'openPrivateChat',
    JSON.stringify({
      currentUserId,
      targetUserId,
      targetUserName,
      timestamp: Date.now(),
    })
  );

  // Navigate to chat page
  router.push('/chat?tab=private');
};
