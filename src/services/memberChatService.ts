import { prisma } from '@/lib/prisma';
import { 
  addUserToChats, 
  handleMemberDepartmentChange, 
  removeMemberFromChats,
  createCompleteDepartmentChat 
} from './organizationChatService';

/**
 * Service for handling member-specific chat operations
 * This service provides high-level functions for managing member chat access
 */

export interface MemberChatSyncResult {
  success: boolean;
  message: string;
  details?: {
    chatsAdded: number;
    chatsRemoved: number;
    errors: string[];
  };
}

/**
 * Synchronize member with chats after member creation
 */
export async function syncMemberChatsOnCreation(
  userId: number,
  departmentId: number,
  organizationId: number
): Promise<MemberChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await addUserToChats(userId, departmentId, organizationId, tx);
    });

    if (result.errors.length > 0) {
      return {
        success: false,
        message: 'Member created but some chat operations failed',
        details: {
          chatsAdded: result.success,
          chatsRemoved: 0,
          errors: result.errors,
        },
      };
    }

    return {
      success: true,
      message: `Member successfully added to ${result.success} chat(s)`,
      details: {
        chatsAdded: result.success,
        chatsRemoved: 0,
        errors: [],
      },
    };
  } catch (error) {
    console.error('Error syncing member chats on creation:', error);
    return {
      success: false,
      message: 'Failed to sync member with chats',
      details: {
        chatsAdded: 0,
        chatsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Synchronize member chats after department change
 */
export async function syncMemberChatsOnDepartmentChange(
  userId: number,
  oldDepartmentId: number,
  newDepartmentId: number
): Promise<MemberChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await handleMemberDepartmentChange(userId, oldDepartmentId, newDepartmentId, tx);
    });

    if (result.errors.length > 0) {
      return {
        success: false,
        message: 'Member department changed but some chat operations failed',
        details: {
          chatsAdded: 1, // Assuming successful add to new department
          chatsRemoved: 1, // Assuming successful remove from old department
          errors: result.errors,
        },
      };
    }

    return {
      success: true,
      message: 'Member successfully moved between department chats',
      details: {
        chatsAdded: 1,
        chatsRemoved: 1,
        errors: [],
      },
    };
  } catch (error) {
    console.error('Error syncing member chats on department change:', error);
    return {
      success: false,
      message: 'Failed to sync member chats during department change',
      details: {
        chatsAdded: 0,
        chatsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Synchronize member chats after member removal
 */
export async function syncMemberChatsOnRemoval(
  userId: number,
  departmentId: number,
  organizationId: number
): Promise<MemberChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      return await removeMemberFromChats(userId, departmentId, organizationId, tx);
    });

    if (result.errors.length > 0) {
      return {
        success: false,
        message: 'Member removed but some chat cleanup failed',
        details: {
          chatsAdded: 0,
          chatsRemoved: result.success,
          errors: result.errors,
        },
      };
    }

    return {
      success: true,
      message: `Member successfully removed from ${result.success} chat(s)`,
      details: {
        chatsAdded: 0,
        chatsRemoved: result.success,
        errors: [],
      },
    };
  } catch (error) {
    console.error('Error syncing member chats on removal:', error);
    return {
      success: false,
      message: 'Failed to remove member from chats',
      details: {
        chatsAdded: 0,
        chatsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Ensure department chat exists and add all current members
 * This function can be used to repair missing department chats
 */
export async function ensureDepartmentChatWithMembers(
  departmentId: number
): Promise<MemberChatSyncResult> {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get department with members and organization info
      const department = await tx.department.findUnique({
        where: { id: departmentId },
        include: {
          organization: true,
          members: {
            include: {
              user: {
                select: { id: true, deletedAt: true },
              },
            },
          },
        },
      });

      if (!department) {
        throw new Error(`Department with ID ${departmentId} not found`);
      }

      // Check if department chat exists
      const existingChat = await tx.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: departmentId,
        },
      });

      let chatsAdded = 0;
      const errors: string[] = [];

      if (!existingChat) {
        // Create department chat
        try {
          await createCompleteDepartmentChat({
            departmentId: department.id,
            departmentName: department.name,
            organizationId: department.organizationId,
          }, tx);
          chatsAdded++;
        } catch (chatError) {
          errors.push(`Failed to create department chat: ${chatError instanceof Error ? chatError.message : 'Unknown error'}`);
        }
      }

      // Add all active members to the chat
      const activeMembers = department.members.filter(member => !member.user.deletedAt);
      for (const member of activeMembers) {
        try {
          const addResult = await addUserToChats(
            member.userId,
            departmentId,
            department.organizationId,
            tx
          );
          chatsAdded += addResult.success;
          errors.push(...addResult.errors);
        } catch (memberError) {
          errors.push(`Failed to add member ${member.userId} to chats: ${memberError instanceof Error ? memberError.message : 'Unknown error'}`);
        }
      }

      return { chatsAdded, errors };
    });

    return {
      success: result.errors.length === 0,
      message: result.errors.length === 0 
        ? `Successfully ensured department chat with ${result.chatsAdded} operations`
        : `Department chat ensured with ${result.errors.length} errors`,
      details: {
        chatsAdded: result.chatsAdded,
        chatsRemoved: 0,
        errors: result.errors,
      },
    };
  } catch (error) {
    console.error('Error ensuring department chat with members:', error);
    return {
      success: false,
      message: 'Failed to ensure department chat with members',
      details: {
        chatsAdded: 0,
        chatsRemoved: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      },
    };
  }
}

/**
 * Get member chat status for UI display
 */
export async function getMemberChatStatus(
  userId: number,
  departmentId: number,
  organizationId: number
): Promise<{
  organizationChat: { exists: boolean; isMember: boolean; chatId?: number };
  departmentChat: { exists: boolean; isMember: boolean; chatId?: number };
}> {
  try {
    const [organizationChat, departmentChat] = await Promise.all([
      prisma.chat.findFirst({
        where: {
          chatType: 'ORGANIZATION',
          organizationId: organizationId,
        },
        include: {
          chatUsers: {
            where: { userId: userId },
          },
        },
      }),
      prisma.chat.findFirst({
        where: {
          chatType: 'DEPARTMENT',
          departmentId: departmentId,
        },
        include: {
          chatUsers: {
            where: { userId: userId },
          },
        },
      }),
    ]);

    return {
      organizationChat: {
        exists: !!organizationChat,
        isMember: !!organizationChat?.chatUsers[0],
        chatId: organizationChat?.id,
      },
      departmentChat: {
        exists: !!departmentChat,
        isMember: !!departmentChat?.chatUsers[0],
        chatId: departmentChat?.id,
      },
    };
  } catch (error) {
    console.error('Error getting member chat status:', error);
    return {
      organizationChat: { exists: false, isMember: false },
      departmentChat: { exists: false, isMember: false },
    };
  }
}
