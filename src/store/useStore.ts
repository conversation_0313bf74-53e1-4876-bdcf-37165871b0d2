import { create } from 'zustand';

// Define the store state type
interface StoreState {
  // Counter example
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;

  // User example
  user: {
    name: string;
    isLoggedIn: boolean;
  };
  setUser: (name: string) => void;
  login: () => void;
  logout: () => void;
}

// Create the store
const useStore = create<StoreState>(set => ({
  // Counter initial state and actions
  count: 0,
  increment: () => set(state => ({ count: state.count + 1 })),
  decrement: () => set(state => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),

  // User initial state and actions
  user: {
    name: '',
    isLoggedIn: false,
  },
  setUser: name =>
    set(state => ({
      user: {
        ...state.user,
        name,
      },
    })),
  login: () =>
    set(state => ({
      user: {
        ...state.user,
        isLoggedIn: true,
      },
    })),
  logout: () =>
    set(state => ({
      user: {
        ...state.user,
        isLoggedIn: false,
      },
    })),
}));

export default useStore;
