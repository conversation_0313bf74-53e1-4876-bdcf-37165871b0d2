import { create } from 'zustand';
import { getCookie } from 'cookies-next';

// Define the user data interface
export interface UserData {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string | null;
  imageUrl?: string | null;
  role: {
    id: number;
    name: string;
    description?: string | null;
    isOwner: boolean;
    isAdmin: boolean;
    isMember: boolean;
    isBot: boolean;
  };
  organization?: {
    id: number;
    name: string;
    description?: string | null;
    imageUrl?: string | null;
  } | null;
  departments?: Array<{
    id: number;
    name: string;
    description?: string | null;
    joinedAt: string;
    organizationId: number;
    organizationName: string;
    isLeader: boolean;
  }>;
  organizations?: Array<{
    id: number;
    name: string;
    description?: string | null;
    imageUrl?: string | null;
    isOwner: boolean;
    departmentCount: number;
    taskCount: number;
    departments: Array<{
      id: number;
      name: string;
      description?: string | null;
      memberCount: number;
      userJoinedAt: string;
      isDirectMember: boolean;
      isLeader: boolean;
    }>;
  }>;
  summary?: {
    totalOrganizations: number;
    ownedOrganizations: number;
    memberOrganizations: number;
    totalDepartments: number;
    totalDepartmentsAcrossOrgs: number;
  };
}

// Define the user store state
interface UserStoreState {
  userData: UserData | null;
  loading: boolean;
  error: string | null;

  // Actions
  setUserData: (data: UserData | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;

  // User data fetch function
  fetchUserData: () => Promise<void>;
}

// Create the user store
const useUserStore = create<UserStoreState>((set, get) => ({
  // Initial state
  userData: null,
  loading: false,
  error: null,

  // Actions
  setUserData: data => set({ userData: data }),
  setLoading: isLoading => set({ loading: isLoading }),
  setError: error => set({ error }),

  // Fetch user data function
  fetchUserData: async () => {
    try {
      set({ loading: true, error: null });

      // Get token from cookies using cookies-next
      const token = getCookie('access_token');

      if (!token) {
        set({ error: 'Authentication required. Please log in.', loading: false });
        return;
      }

      const response = await fetch('/api/v1/me', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        // Get more specific error information based on status code
        const errorMessage = `Failed to fetch user data: ${response.status} ${response.statusText}`;

        // Try to get more details from response if available
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || errorData.error || errorMessage);
        } catch {
          // If we can't parse the response as JSON, use the status info
          throw new Error(errorMessage);
        }
      }

      const data = await response.json();
      set({ userData: data.user, loading: false });
    } catch (err) {
      console.error('Error fetching user data:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to load user data',
        loading: false,
      });
    }
  },
}));

export default useUserStore;
