import { NextRequest, NextResponse } from 'next/server';

import { validateAccessToken } from '@/lib/auth';

/**
 * Middleware to handle authentication and routing
 * - Redirects logged-in users from index (/) to /kanban
 * - Redirects non-logged-in users to /login (except /register)
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Get access token from cookies
  const accessToken = request.cookies.get('access_token')?.value;

  // Log for debugging (remove in production)
  console.log(`[Middleware] Checking access to: ${pathname}`);
  console.log(`[Middleware] Token present: ${!!accessToken}`);

  try {
    // Validate the token
    const isValidToken = validateAccessToken(accessToken);

    // If user is logged in and accessing index page, redirect to /kanban
    if (isValidToken && pathname === '/') {
      console.log(`[Middleware] Logged-in user accessing index, redirecting to /kanban`);
      const kanbanUrl = new URL('/kanban', request.url);
      return NextResponse.redirect(kanbanUrl);
    }

    // If user is not logged in
    if (!isValidToken) {
      // Allow access to /register and /login pages
      if (pathname === '/register' || pathname === '/login') {
        console.log(`[Middleware] Allowing access to public page: ${pathname}`);
        return NextResponse.next();
      }

      // For all other pages, redirect to login
      console.log(`[Middleware] Invalid token, redirecting to login`);
      const loginUrl = new URL('/login', request.url);

      // Add the original URL as a redirect parameter so user can be redirected back after login
      if (pathname !== '/') {
        loginUrl.searchParams.set('redirect', pathname);
      }

      return NextResponse.redirect(loginUrl);
    }

    // Token is valid, allow the request to continue
    console.log(`[Middleware] Valid token, allowing access`);
    return NextResponse.next();
  } catch (error) {
    // If there's any error in middleware, redirect to login for safety
    console.error(`[Middleware] Error processing request:`, error);

    // Allow access to /register and /login pages even on error
    if (pathname === '/register' || pathname === '/login') {
      return NextResponse.next();
    }

    const loginUrl = new URL('/login', request.url);
    if (pathname !== '/') {
      loginUrl.searchParams.set('redirect', pathname);
    }

    return NextResponse.redirect(loginUrl);
  }
}

/**
 * Configure which routes the middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
