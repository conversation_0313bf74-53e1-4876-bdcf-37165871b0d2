import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// POST to restore a deleted user (set deletedAt to null)
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user making the request
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get the user ID to restore from the query parameters
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const userIdNum = Number(userId);
    if (isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userIdNum },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if the user is already active (not deleted)
    if (!user.deletedAt) {
      return NextResponse.json({ error: 'User is already active' }, { status: 400 });
    }

    // Restore the user by setting deletedAt to null
    const restoredUser = await prisma.user.update({
      where: { id: userIdNum },
      data: { deletedAt: null },
    });

    return NextResponse.json({
      message: 'User restored successfully',
      user: {
        id: restoredUser.id,
        firstName: restoredUser.firstName,
        lastName: restoredUser.lastName,
        email: restoredUser.email,
        deletedAt: restoredUser.deletedAt,
      },
    });
  } catch (error) {
    console.error('Error restoring user:', error);
    return NextResponse.json(
      { error: 'An error occurred while restoring the user' },
      { status: 500 }
    );
  }
}
