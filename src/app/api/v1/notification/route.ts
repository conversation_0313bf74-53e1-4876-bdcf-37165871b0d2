import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// GET all notifications for the authenticated user
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = auth.userId;
    const url = new URL(request.url);

    // Parse pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Parse filter parameters
    const isRead = url.searchParams.get('isRead');
    const isArchived = url.searchParams.get('isArchived');
    const typeId = url.searchParams.get('typeId');

    // Build filter conditions
    const filterConditions: any = {
      userId: userId,
    };

    if (isRead !== null) {
      filterConditions.isRead = isRead === 'true';
    }

    if (isArchived !== null) {
      filterConditions.isArchived = isArchived === 'true';
    }

    if (typeId !== null && !isNaN(parseInt(typeId))) {
      filterConditions.notification = {
        typeId: parseInt(typeId),
      };
    }

    // Get user notifications with pagination
    const userNotifications = await prisma.userNotification.findMany({
      where: filterConditions,
      include: {
        notification: {
          include: {
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip: offset,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.userNotification.count({
      where: filterConditions,
    });

    return NextResponse.json({
      data: userNotifications,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching notifications' },
      { status: 500 }
    );
  }
}

// POST to create a new notification and send it to specified users
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Parse the request body
    const body = await request.json();
    const { typeId, title, content, data, entityType, entityId, userIds } = body;

    // Validate required fields
    if (!typeId || !title || !content) {
      return NextResponse.json(
        { error: 'Type ID, title, and content are required' },
        { status: 400 }
      );
    }

    // Validate that the notification type exists
    const notificationType = await prisma.notificationType.findUnique({
      where: { id: typeId },
    });

    if (!notificationType) {
      return NextResponse.json({ error: 'Notification type not found' }, { status: 404 });
    }

    // Validate userIds if provided
    if (userIds && !Array.isArray(userIds)) {
      return NextResponse.json({ error: 'userIds must be an array' }, { status: 400 });
    }

    // Create the notification
    const notification = await prisma.notification.create({
      data: {
        typeId,
        title,
        content,
        data: data || {},
        entityType,
        entityId,
      },
    });

    // If userIds are provided, create user notifications for those users
    if (userIds && userIds.length > 0) {
      // Create user notifications for each user
      await prisma.userNotification.createMany({
        data: userIds.map((userId: number) => ({
          userId,
          notificationId: notification.id,
        })),
      });
    }

    return NextResponse.json({
      message: 'Notification created successfully',
      notification,
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the notification' },
      { status: 500 }
    );
  }
}
