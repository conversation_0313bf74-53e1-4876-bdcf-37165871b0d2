import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// POST to archive a notification
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = auth.userId;
    const url = new URL(request.url);
    const notificationId = url.searchParams.get('id');

    if (!notificationId || isNaN(Number(notificationId))) {
      return NextResponse.json({ error: 'Valid notification ID is required' }, { status: 400 });
    }

    // Check if the user notification exists
    const userNotification = await prisma.userNotification.findUnique({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
    });

    if (!userNotification) {
      return NextResponse.json({ error: 'Notification not found for this user' }, { status: 404 });
    }

    // Archive the notification
    const updatedNotification = await prisma.userNotification.update({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
      data: {
        isArchived: true,
        archivedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: 'Notification archived successfully',
      notification: updatedNotification,
    });
  } catch (error) {
    console.error('Error archiving notification:', error);
    return NextResponse.json(
      { error: 'An error occurred while archiving the notification' },
      { status: 500 }
    );
  }
}

// DELETE to unarchive a notification
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = auth.userId;
    const url = new URL(request.url);
    const notificationId = url.searchParams.get('id');

    if (!notificationId || isNaN(Number(notificationId))) {
      return NextResponse.json({ error: 'Valid notification ID is required' }, { status: 400 });
    }

    // Check if the user notification exists
    const userNotification = await prisma.userNotification.findUnique({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
    });

    if (!userNotification) {
      return NextResponse.json({ error: 'Notification not found for this user' }, { status: 404 });
    }

    // Unarchive the notification
    const updatedNotification = await prisma.userNotification.update({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
      data: {
        isArchived: false,
        archivedAt: null,
      },
    });

    return NextResponse.json({
      message: 'Notification unarchived successfully',
      notification: updatedNotification,
    });
  } catch (error) {
    console.error('Error unarchiving notification:', error);
    return NextResponse.json(
      { error: 'An error occurred while unarchiving the notification' },
      { status: 500 }
    );
  }
}
