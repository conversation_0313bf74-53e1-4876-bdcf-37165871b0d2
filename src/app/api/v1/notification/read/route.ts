import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// POST to mark a notification as read
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = auth.userId;
    const url = new URL(request.url);
    const notificationId = url.searchParams.get('id');

    if (!notificationId || isNaN(Number(notificationId))) {
      return NextResponse.json({ error: 'Valid notification ID is required' }, { status: 400 });
    }

    // Check if the user notification exists
    const userNotification = await prisma.userNotification.findUnique({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
    });

    if (!userNotification) {
      return NextResponse.json({ error: 'Notification not found for this user' }, { status: 404 });
    }

    // Mark the notification as read
    const updatedNotification = await prisma.userNotification.update({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
      data: {
        isRead: true,
        readAt: new Date(),
      },
    });

    return NextResponse.json({
      message: 'Notification marked as read',
      notification: updatedNotification,
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the notification' },
      { status: 500 }
    );
  }
}

// DELETE to mark a notification as unread
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const userId = auth.userId;
    const url = new URL(request.url);
    const notificationId = url.searchParams.get('id');

    if (!notificationId || isNaN(Number(notificationId))) {
      return NextResponse.json({ error: 'Valid notification ID is required' }, { status: 400 });
    }

    // Check if the user notification exists
    const userNotification = await prisma.userNotification.findUnique({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
    });

    if (!userNotification) {
      return NextResponse.json({ error: 'Notification not found for this user' }, { status: 404 });
    }

    // Mark the notification as unread
    const updatedNotification = await prisma.userNotification.update({
      where: {
        userId_notificationId: {
          userId: userId,
          notificationId: Number(notificationId),
        },
      },
      data: {
        isRead: false,
        readAt: null,
      },
    });

    return NextResponse.json({
      message: 'Notification marked as unread',
      notification: updatedNotification,
    });
  } catch (error) {
    console.error('Error marking notification as unread:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the notification' },
      { status: 500 }
    );
  }
}
