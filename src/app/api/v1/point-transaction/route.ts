import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * GET API for point transactions
 *
 * Returns all point transactions for the authenticated user
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get point transactions for the authenticated user
    const pointTransactions = await prisma.pointTransaction.findMany({
      where: {
        userId: auth.userId,
      },
      include: {
        task: {
          select: {
            id: true,
            taskTitle: true,
            taskDescription: true,
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
            taskAssignments: {
              where: { isActive: true },
              select: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                  },
                },
              },
            },
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate total points
    const totalPoints = pointTransactions.reduce(
      (sum, transaction) => sum + transaction.pointAmount,
      0
    );

    return NextResponse.json({
      pointTransactions,
      totalPoints,
    });
  } catch (error) {
    console.error('Error in GET /api/v1/point-transaction:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching point transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST API to create a new point transaction
 *
 * Creates a new point transaction for a user
 * Required fields:
 * - taskId: ID of the task
 * - pointAmount: Amount of points for the transaction
 * - userId: ID of the user to receive points (required for admin/owner)
 *
 * Note: Only owners and admins can create point transactions for other users
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if user is owner or admin
    if (!auth.isOwner && !auth.isAdmin) {
      return NextResponse.json(
        { error: 'Only owners and admins can create point transactions' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    console.log('Request body:', body); // Log the request body for debugging

    // Extract and convert values to appropriate types
    const taskId = body.taskId ? Number(body.taskId) : null;
    const pointAmount =
      body.pointAmount !== undefined && body.pointAmount !== null ? Number(body.pointAmount) : null;
    const userId = body.userId ? Number(body.userId) : null;

    // Validate required fields
    if (!taskId || isNaN(taskId)) {
      return NextResponse.json({ error: 'Valid Task ID is required' }, { status: 400 });
    }

    if (pointAmount === null || isNaN(pointAmount)) {
      return NextResponse.json({ error: 'Valid Point amount is required' }, { status: 400 });
    }

    if (!userId || isNaN(userId)) {
      return NextResponse.json({ error: 'Valid User ID is required' }, { status: 400 });
    }

    // Verify task exists
    const task = await prisma.task.findUnique({
      where: { id: taskId }, // taskId is already converted to Number
    });

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Verify the task has points and isClaimPoint is false
    if (!task.points) {
      return NextResponse.json(
        { error: 'This task does not have any points assigned' },
        { status: 400 }
      );
    }

    if (task.isClaimPoint) {
      return NextResponse.json(
        { error: 'Points for this task have already been claimed' },
        { status: 400 }
      );
    }

    // Verify the user exists
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }, // userId is already converted to Number
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    try {
      // Use a transaction to ensure both operations succeed or fail together
      const pointTransaction = await prisma.$transaction(async tx => {
        // Create the point transaction
        const transaction = await tx.pointTransaction.create({
          data: {
            userId: userId, // Already converted to Number
            taskId: taskId, // Already converted to Number
            pointAmount: pointAmount, // Already converted to Number
          },
        });

        // Update the task within the same transaction
        await tx.$executeRaw`UPDATE tasks SET is_claim_point = true WHERE id = ${taskId}`;
        console.log('Task updated successfully within transaction');

        return transaction;
      });

      return NextResponse.json({
        message: 'Point transaction created successfully',
        pointTransaction,
      });
    } catch (transactionError) {
      console.error('Transaction error:', transactionError);
      return NextResponse.json(
        {
          error: 'Failed to create point transaction',
          details:
            transactionError instanceof Error
              ? transactionError.message
              : 'Unknown transaction error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error in POST /api/v1/point-transaction:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    return NextResponse.json(
      {
        error: 'An error occurred while creating the point transaction',
        details: errorMessage,
        stack: process.env.NODE_ENV === 'development' ? errorStack : undefined,
      },
      { status: 500 }
    );
  }
}
