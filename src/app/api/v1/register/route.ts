import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcrypt';
import { USER_ROLES } from '@/constants/roles';
import { createCompleteOrganizationChat } from '@/services/organizationChatService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const { email, password, firstName, lastName, phone, organizationName } = body;

    if (!email || !password || !firstName || !lastName || !organizationName) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 });
    }

    // Hash the password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Get the Boss role ID (should be 1 based on seed file)
    const bossRole = await prisma.userRole.findFirst({
      where: { name: USER_ROLES.OWNER },
    });

    if (!bossRole) {
      return NextResponse.json({ error: 'Boss role not found' }, { status: 500 });
    }

    // Create user, organization, and organization chat in a transaction
    // This ensures that if any step fails, the entire registration process is rolled back
    const result = await prisma.$transaction(async tx => {
      // 1. Create the user with Boss role
      const user = await tx.user.create({
        data: {
          email,
          passwordHash,
          firstName,
          lastName,
          phone,
          userRoleId: bossRole.id,
        },
      });

      // 2. Create the organization
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          ownerUserId: user.id,
        },
      });

      // 3. Create the organization chat automatically
      const organizationChatResult = await createCompleteOrganizationChat({
        organizationId: organization.id,
        organizationName: organization.name,
        creatorUserId: user.id,
      }, tx);

      return {
        user,
        organization,
        organizationChat: organizationChatResult.chat,
        organizationChatUser: organizationChatResult.chatUser
      };
    });

    // Return success response with created user (excluding password)
    const { user, organization, organizationChat, organizationChatUser } = result;

    return NextResponse.json(
      {
        message: 'User registered successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: USER_ROLES.OWNER,
          },
          organization: {
            id: organization.id,
            name: organization.name,
          },
          organizationChat: {
            id: organizationChat.id,
            name: organizationChat.name,
            chatType: organizationChat.chatType,
          },
          chatMembership: {
            isAdmin: organizationChatUser.isAdmin,
            joinedAt: organizationChatUser.joinedAt,
          },
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);

    // Provide more specific error messages based on the error
    let errorMessage = 'Failed to register user';

    if (error instanceof Error) {
      // Check for specific error patterns
      if (error.message.includes('organization')) {
        errorMessage = 'Failed to create organization during registration';
      } else if (error.message.includes('chat')) {
        errorMessage = 'User and organization created, but failed to set up organization chat';
      } else if (error.message.includes('Unique constraint')) {
        errorMessage = 'User with this email already exists';
      }
    }

    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
