import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * GET API for feedback types (master data)
 * 
 * Returns all available feedback types for the system.
 * This is master data that doesn't require special permissions.
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get all feedback types
    const feedbackTypes = await prisma.feedbackType.findMany({
      select: {
        id: true,
        name: true,
        displayName: true,
        description: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    return NextResponse.json({
      message: 'Feedback types retrieved successfully',
      feedbackTypes,
    });
  } catch (error) {
    console.error('Error fetching feedback types:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feedback types' },
      { status: 500 }
    );
  }
}
