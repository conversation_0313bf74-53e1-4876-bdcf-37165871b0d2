import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: {
      userRole: true,
      departmentMembers: {
        include: {
          department: {
            include: {
              organization: true,
            },
          },
        },
      },
    },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
    departments: user.departmentMembers.map(member => ({
      departmentId: member.department.id,
      organizationId: member.department.organizationId,
    })),
  };
}

/**
 * GET API for rankings based on point transactions
 *
 * Supports filtering by:
 * - period: 'daily', 'monthly', 'alltime' (default: 'alltime')
 * - organizationId: Filter by organization (only for isOwner/isAdmin)
 * - departmentId: Filter by department (only for isOwner/isAdmin)
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get query parameters
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'alltime';
    const organizationId = url.searchParams.get('organizationId')
      ? parseInt(url.searchParams.get('organizationId')!)
      : undefined;
    const departmentId = url.searchParams.get('departmentId')
      ? parseInt(url.searchParams.get('departmentId')!)
      : undefined;

    // Calculate date range based on period
    let startDate: Date | undefined;
    const now = new Date();

    if (period === 'daily') {
      startDate = new Date(now);
      startDate.setHours(0, 0, 0, 0); // Start of today
    } else if (period === 'monthly') {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1); // Start of current month
    }
    // For 'alltime', startDate remains undefined

    // Build the where clause for point transactions
    let whereClause: any = {};

    // Add date filter if applicable
    if (startDate) {
      whereClause.createdAt = {
        gte: startDate,
      };
    }

    // Handle authorization and filtering logic
    if (auth.isOwner || auth.isAdmin) {
      // Owners and admins can filter by organization and department
      if (organizationId) {
        whereClause.user = {
          departmentMembers: {
            some: {
              department: {
                organizationId: organizationId,
              },
            },
          },
        };
      }

      if (departmentId) {
        whereClause.user = {
          ...whereClause.user,
          departmentMembers: {
            some: {
              departmentId: departmentId,
            },
          },
        };
      }
    } else {
      // Regular members can only see rankings from their own departments
      if (auth.departments.length > 0) {
        whereClause.user = {
          departmentMembers: {
            some: {
              departmentId: {
                in: auth.departments.map(d => d.departmentId),
              },
            },
          },
        };
      }
    }

    // Group point transactions by user and sum the points
    const rankings = await prisma.pointTransaction.groupBy({
      by: ['userId'],
      _sum: {
        pointAmount: true,
      },
      where: whereClause,
      orderBy: {
        _sum: {
          pointAmount: 'desc',
        },
      },
    });

    // Get user details for each ranking
    const rankingsWithUserDetails = await Promise.all(
      rankings.map(async (rank, index) => {
        const user = await prisma.user.findUnique({
          where: { id: rank.userId },
          include: {
            departmentMembers: {
              include: {
                department: {
                  include: {
                    organization: true,
                  },
                },
              },
            },
          },
        });

        if (!user) {
          return null;
        }

        // Get primary department and organization (first one)
        const primaryDepartmentMember = user.departmentMembers[0];
        const department = primaryDepartmentMember?.department;
        const organization = department?.organization;

        return {
          place: index + 1,
          userId: user.id,
          username: `${user.firstName} ${user.lastName}`,
          email: user.email,
          imageUrl: user.imageUrl,
          points: rank._sum.pointAmount || 0,
          department: department
            ? {
                id: department.id,
                name: department.name,
              }
            : null,
          organization: organization
            ? {
                id: organization.id,
                name: organization.name,
              }
            : null,
        };
      })
    );

    // Filter out any null values (users not found)
    const validRankings = rankingsWithUserDetails.filter(Boolean);

    return NextResponse.json({
      rankings: validRankings,
      period,
      filters: {
        organizationId,
        departmentId,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/v1/ranking:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching rankings' },
      { status: 500 }
    );
  }
}
