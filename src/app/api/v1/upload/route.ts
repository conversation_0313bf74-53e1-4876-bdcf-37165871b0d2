import { NextRequest, NextResponse } from 'next/server';
import { verifyJwt } from '@/lib/jwt';
import { generateS3Key, validateFile, uploadToS3, FileType } from '@/lib/s3';

export async function POST(request: NextRequest) {
  try {
    // Verify JWT token
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const decoded = verifyJwt(token);
    if (!decoded || !decoded.userId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    const userId = decoded.userId;

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const fileType = formData.get('fileType') as FileType;

    if (!file) {
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }

    if (!fileType || !['images', 'documents', 'avatars', 'chat'].includes(fileType)) {
      return NextResponse.json({ error: 'Valid fileType is required' }, { status: 400 });
    }

    // Validate file
    const validation = validateFile(file, fileType);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    // Generate S3 key
    const s3Key = generateS3Key(userId.toString(), fileType, file.name);

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to S3
    const uploadResult = await uploadToS3(s3Key, buffer, file.type);

    if (!uploadResult.success) {
      return NextResponse.json({ error: uploadResult.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        // url: uploadResult.url,
        url: `${process.env.NEXT_PUBLIC_AWS_CDN_URL}/${s3Key}`,
        key: s3Key,
        fileName: file.name,
        fileType,
        size: file.size,
        contentType: file.type,
      },
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
