import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { getUserOrganizationAdminPrivileges, hasOrganizationAdminPrivileges } from '@/lib/permissions';
import { USER_ROLES } from '@/constants/roles';
import { taskNotificationService } from '@/services/taskNotificationService';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Helper function to check if user has admin or owner role
async function verifyAdminRole(userId: number) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  if (!user.userRole.isAdmin && !user.userRole.isOwner) {
    return {
      error: 'You do not have permission to perform this action. Admin role required.',
      status: 403,
    };
  }

  return { isAdmin: true };
}

// Helper function to update task chat participants when assignments change
async function updateTaskChatParticipants(taskId: number, updatedTask: any) {
  try {
    // Find the task chat
    const taskChat = await prisma.chat.findFirst({
      where: {
        taskId: taskId,
        chatType: 'TASK',
      },
    });

    if (!taskChat) {
      console.log(`No task chat found for task ID: ${taskId}`);
      return;
    }

    console.log(`Updating chat participants for task chat ID: ${taskChat.id}`);

    // Get the new participant list: task creator + all assigned users
    const assignedUserIds = updatedTask.taskAssignments.map((assignment: any) => assignment.userId);
    const newParticipantIds = [
      ...new Set([
        updatedTask.createdByUserId,
        ...assignedUserIds,
      ].filter(id => id != null)), // Filter out null/undefined values
    ];

    console.log('New participant IDs:', newParticipantIds);

    // Remove all existing chat users
    await prisma.chatUser.deleteMany({
      where: {
        chatId: taskChat.id,
      },
    });

    console.log('Removed all existing chat participants');

    // Add new chat users
    await prisma.chatUser.createMany({
      data: newParticipantIds.map(userId => ({
        chatId: taskChat.id,
        userId,
        isAdmin: userId === updatedTask.createdByUserId, // Task creator is admin
      })),
    });

    console.log('Added new chat participants:', newParticipantIds);
  } catch (error) {
    console.error('Error in updateTaskChatParticipants:', error);
    throw error;
  }
}

/**
 * GET API for tasks
 *
 * This API supports two modes:
 *
 * 1. Get a single task by ID:
 *    - Path: /api/v1/task?id=123
 *    - Returns: Detailed task information including status and progress list
 *
 * 2. Get a list of tasks with filtering:
 *    - Path: /api/v1/task
 *    - Optional query parameters:
 *      - assignedToUserIds: Filter by assigned users (comma-separated IDs)
 *      - createdByUserId: Filter by creator
 *      - statusId: Filter by status
 *      - organizationId: Filter by organization
 *      - departmentId: Filter by department
 *    - Returns: List of tasks matching the filters
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const taskId = url.searchParams.get('id');
    const assignedToUserIds = url.searchParams.get('assignedToUserIds');
    const createdByUserId = url.searchParams.get('createdByUserId');
    const statusId = url.searchParams.get('statusId');
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');

    // Build the where clause for filtering
    const where: any = {};

    // Get a specific task by ID
    if (taskId) {
      const taskIdNum = Number(taskId);
      if (isNaN(taskIdNum)) {
        return NextResponse.json({ error: 'Invalid task ID' }, { status: 400 });
      }

      const task = await prisma.task.findUnique({
        where: { id: taskIdNum },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
            },
          },
          taskAssignments: {
            where: { isActive: true },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                },
              },
              assignedByUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
            orderBy: {
              assignedAt: 'desc',
            },
          },
          organization: true,
          department: true,
          status: true,
          taskProgresses: {
            include: {
              updatedByUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                },
              },
              progressType: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
      });

      // isClaimPoint is already included by default in the task model

      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 });
      }

      // Check if user is related to this task (created it, assigned to it, or is admin)
      const userRole = await prisma.user.findUnique({
        where: { id: auth.userId },
        include: { userRole: true },
      });

      // Check if user is assigned to this task
      const isAssignedToTask = task.taskAssignments?.some(
        assignment => assignment.user.id === auth.userId && assignment.isActive
      );

      if (
        task.createdByUserId !== auth.userId &&
        !isAssignedToTask &&
        !userRole?.userRole?.isOwner &&
        !userRole?.userRole?.isAdmin
      ) {
        return NextResponse.json(
          {
            error: 'You do not have permission to view this task',
          },
          { status: 403 }
        );
      }

      return NextResponse.json({ task });
    }

    // Apply filters
    if (assignedToUserIds) {
      const userIds = assignedToUserIds.split(',').map(id => Number(id.trim()));
      where.taskAssignments = {
        some: {
          userId: { in: userIds },
          isActive: true,
        },
      };
    }

    if (createdByUserId) {
      where.createdByUserId = Number(createdByUserId);
    }

    if (statusId) {
      where.statusId = Number(statusId);
    }

    // Apply direct organization and department filters if provided
    if (organizationId && !isNaN(Number(organizationId))) {
      where.organizationId = Number(organizationId);
    }

    if (departmentId && !isNaN(Number(departmentId))) {
      where.departmentId = Number(departmentId);
    }

    // Get user role to determine what tasks they can see
    const user = await prisma.user.findUnique({
      where: { id: auth.userId },
      include: { userRole: true },
    });

    // We now use direct organization and department filters instead of the old filtering logic

    // Apply role-based filtering using new permission system
    if (user?.userRole?.isOwner || user?.userRole?.isAdmin) {
      // Get organizations where user has admin privileges (owner or admin)
      const privileges = await getUserOrganizationAdminPrivileges(auth.userId);

      if (privileges.allAdminOrganizations.length > 0) {
        // Add organization filter - only show tasks from organizations where user has admin privileges
        where.organizationId = {
          in: privileges.allAdminOrganizations,
        };
      } else {
        // If user has no admin privileges, return empty result
        where.id = -1; // This will return no results
      }

      // Apply additional createdByUserId filter if provided
      if (createdByUserId) {
        // If we have existing filters, we need to combine them with the user filter
        if (where.OR) {
          // Create a new condition that combines the existing OR with the createdByUserId
          const existingOR = where.OR;
          where.AND = [{ OR: existingOR }, { createdByUserId: Number(createdByUserId) }];
          delete where.OR; // Remove the original OR since it's now in AND
        } else {
          // No existing filters, just set createdByUserId directly
          where.createdByUserId = Number(createdByUserId);
        }
      }
    } else {
      // Members and other roles can only see tasks assigned to them or created by them
      // Other roles - restrict to tasks created by or assigned to the user
      const userRestriction = {
        OR: [
          { createdByUserId: auth.userId },
          {
            taskAssignments: {
              some: {
                userId: auth.userId,
                isActive: true,
              },
            },
          },
        ],
      };

      if (where.OR) {
        // Create a new condition that combines the existing OR with the user restriction
        const existingOR = where.OR;
        where.AND = [{ OR: existingOR }, userRestriction];
        delete where.OR; // Remove the original OR since it's now in AND
      } else {
        // No existing filters, just set the user restriction
        Object.assign(where, userRestriction);
      }
    }

    // If user is a member, filter tasks to only include those with statuses where isMemberDisplay = true
    let filteredTasks = [];

    // Get tasks with filtering
    const tasks = await prisma.task.findMany({
      where,
      include: {
        createdByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
          },
        },
        taskAssignments: {
          where: { isActive: true },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
          orderBy: {
            assignedAt: 'desc',
          },
        },
        organization: true,
        department: true,
        status: true,
      },
      orderBy: {
        updatedAt: 'asc',
      },
    });

    // isClaimPoint is already included by default in the task model

    // If user has isMember role, filter tasks by status.isMemberDisplay
    if (auth.isMember) {
      filteredTasks = tasks.filter(task => task.status.isMemberDisplay);
    } else {
      filteredTasks = tasks;
    }

    return NextResponse.json({ tasks: filteredTasks });
  } catch (error) {
    console.error('Error in GET /api/v1/task:', error);
    return NextResponse.json({ error: 'An error occurred while fetching tasks' }, { status: 500 });
  }
}

// POST to create a new task
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Verify user has admin role (role ID = 1)
    // const adminCheck = await verifyAdminRole(auth.userId);
    // if ('error' in adminCheck) {
    //   return NextResponse.json({ error: adminCheck.error }, { status: adminCheck.status });
    // }

    const body = await request.json();
    const {
      taskTitle,
      taskDescription,
      assignedToUserIds, // Now accepts array of user IDs
      taskAssignments, // New format: array of {userId, isLeader}
      statusId,
      points,
      dueDate,
      organizationId,
      departmentId,
    } = body;

    // Validate required fields
    if (!taskTitle) {
      return NextResponse.json({ error: 'Task title is required' }, { status: 400 });
    }

    // Support both old format (assignedToUserIds) and new format (taskAssignments)
    let assignmentData: Array<{ userId: number, isLeader: boolean }> = [];

    if (taskAssignments && Array.isArray(taskAssignments) && taskAssignments.length > 0) {
      // New format with leader information
      assignmentData = taskAssignments.map((assignment: any) => ({
        userId: Number(assignment.userId),
        isLeader: Boolean(assignment.isLeader || false)
      }));
    } else if (assignedToUserIds && Array.isArray(assignedToUserIds) && assignedToUserIds.length > 0) {
      // Old format - backward compatibility
      assignmentData = assignedToUserIds.map((userId: any) => ({
        userId: Number(userId),
        isLeader: false
      }));
    } else {
      return NextResponse.json({ error: 'At least one assigned user is required' }, { status: 400 });
    }

    if (!statusId) {
      return NextResponse.json({ error: 'Status ID is required' }, { status: 400 });
    }

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    if (!departmentId) {
      return NextResponse.json({ error: 'Department ID is required' }, { status: 400 });
    }

    // Verify all assigned users exist
    const assignedUserIds = assignmentData.map(assignment => assignment.userId);
    const assignedUsers = await prisma.user.findMany({
      where: { id: { in: assignedUserIds } },
    });

    if (assignedUsers.length !== assignedUserIds.length) {
      const foundIds = assignedUsers.map(user => user.id);
      const missingIds = assignedUserIds.filter(id => !foundIds.includes(id));
      return NextResponse.json({
        error: `Assigned users not found: ${missingIds.join(', ')}`
      }, { status: 404 });
    }

    // Verify status exists
    const status = await prisma.taskStatus.findUnique({
      where: { id: Number(statusId) },
    });

    if (!status) {
      return NextResponse.json({ error: 'Task status not found' }, { status: 404 });
    }

    // Verify organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: Number(organizationId) },
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Verify department exists and belongs to the organization
    const department = await prisma.department.findUnique({
      where: { id: Number(departmentId) },
    });

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 });
    }

    if (department.organizationId !== Number(organizationId)) {
      return NextResponse.json(
        { error: 'Department does not belong to the specified organization' },
        { status: 400 }
      );
    }

    // Create the task using a transaction to handle potential ID conflicts
    const task = await prisma.$transaction(async tx => {
      // Create the task
      const createdTask = await tx.task.create({
        data: {
          taskTitle,
          taskDescription,
          createdByUserId: auth.userId,
          statusId: Number(statusId),
          organizationId: Number(organizationId),
          departmentId: Number(departmentId),
          points: points ? Number(points) : null,
          dueDate: dueDate ? new Date(dueDate) : null,
        },
      });

      // Create task assignments for all assigned users
      const taskAssignments = await Promise.all(
        assignmentData.map(assignment =>
          tx.taskAssignment.create({
            data: {
              taskId: createdTask.id,
              userId: assignment.userId,
              isLeader: assignment.isLeader,
              assignedBy: auth.userId,
            },
          })
        )
      );

      // Fetch the complete task with all relations
      const taskWithRelations = await tx.task.findUnique({
        where: { id: createdTask.id },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          taskAssignments: {
            where: { isActive: true },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          organization: true,
          department: true,
          status: true,
        },
      });

      // Create task progress entry with Activity Log type
      const creatorInfo = await tx.user.findUnique({
        where: { id: auth.userId },
        select: { firstName: true, lastName: true },
      });

      // First, ensure the Activity Log progress type exists
      const activityLogType = await tx.taskProgressType.upsert({
        where: { id: 3 },
        update: {},
        create: {
          id: 3,
          name: 'log',
          displayName: 'Activity Log',
          color: '#8B5CF6',
          description: 'Log of activity or work done on the task',
          isOwner: false,
          isAdmin: false,
          isMember: false,
        },
      });

      await tx.taskProgress.create({
        data: {
          taskId: createdTask.id,
          updatedByUserId: auth.userId,
          progressTypeId: activityLogType.id,
          progressDescription: `Task created by ${creatorInfo?.firstName || ''} ${creatorInfo?.lastName || ''}`,
        },
      });

      return taskWithRelations;
    });

    // Send notifications to all assigned users
    if (task && task.taskAssignments) {
      try {
        await Promise.all(
          task.taskAssignments.map(async (assignment: any) => {
            await taskNotificationService.notifyTaskAssigned(
              task.id,
              task.taskTitle,
              assignment.userId,
              auth.userId
            );
          })
        );
      } catch (notificationError) {
        console.error('Error sending task assignment notifications:', notificationError);
        // Continue with the response even if notification fails
      }
    }

    return NextResponse.json({ task }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/v1/task:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the task' },
      { status: 500 }
    );
  }
}

// PATCH to update a task
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      id,
      taskTitle,
      taskDescription,
      assignedToUserIds, // Now accepts array of user IDs
      taskAssignments, // New format: array of {userId, isLeader}
      statusId,
      points,
      dueDate,
      organizationId,
      departmentId,
    } = body;

    if (!id) {
      return NextResponse.json({ error: 'Task ID is required' }, { status: 400 });
    }

    // Find the task
    const existingTask = await prisma.task.findUnique({
      where: { id: Number(id) },
    });

    if (!existingTask) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Get user role
    const user = await prisma.user.findUnique({
      where: { id: auth.userId },
      select: { userRoleId: true },
    });

    // Check if user has permission to update the task
    // Only Boss role (role ID = 1) can edit tasks
    // if (user?.userRoleId !== 1) {
    //   return NextResponse.json(
    //     {
    //       error: 'You do not have permission to update this task. Only Boss users can edit tasks.',
    //     },
    //     { status: 403 }
    //   );
    // }

    // If changing assignedToUserIds, verify all users exist
    if (assignedToUserIds && Array.isArray(assignedToUserIds)) {
      const assignedUserIdNumbers = assignedToUserIds.map((id: any) => Number(id));
      const assignedUsers = await prisma.user.findMany({
        where: { id: { in: assignedUserIdNumbers } },
      });

      if (assignedUsers.length !== assignedUserIdNumbers.length) {
        const foundIds = assignedUsers.map(user => user.id);
        const missingIds = assignedUserIdNumbers.filter(id => !foundIds.includes(id));
        return NextResponse.json({
          error: `Assigned users not found: ${missingIds.join(', ')}`
        }, { status: 404 });
      }
    }

    // If changing statusId, verify the status exists
    if (statusId && statusId !== existingTask.statusId) {
      const status = await prisma.taskStatus.findUnique({
        where: { id: Number(statusId) },
      });

      if (!status) {
        return NextResponse.json({ error: 'Task status not found' }, { status: 404 });
      }
    }

    // Get the old status name if status is being changed
    let oldStatusName = '';
    if (statusId !== undefined && statusId !== existingTask.statusId) {
      const oldStatus = await prisma.taskStatus.findUnique({
        where: { id: existingTask.statusId },
      });
      oldStatusName = oldStatus?.name || 'Unknown';
    }

    // Update the task using a transaction to handle potential conflicts
    const updatedTask = await prisma.$transaction(async tx => {
      // Update the task
      const updated = await tx.task.update({
        where: { id: Number(id) },
        data: {
          taskTitle: taskTitle !== undefined ? taskTitle : undefined,
          taskDescription: taskDescription !== undefined ? taskDescription : undefined,
          statusId: statusId !== undefined ? Number(statusId) : undefined,
          organizationId: organizationId !== undefined ? Number(organizationId) : undefined,
          departmentId: departmentId !== undefined ? Number(departmentId) : undefined,
          points: points !== undefined ? (points === null ? null : Number(points)) : undefined,
          dueDate:
            dueDate !== undefined ? (dueDate === null ? null : new Date(dueDate)) : undefined,
        },
      });

      // Handle assignment updates - support both old and new formats
      let assignmentData: Array<{ userId: number, isLeader: boolean }> = [];
      let shouldUpdateAssignments = false;

      if (taskAssignments && Array.isArray(taskAssignments)) {
        // New format with leader information
        assignmentData = taskAssignments.map((assignment: any) => ({
          userId: Number(assignment.userId),
          isLeader: Boolean(assignment.isLeader || false)
        }));
        shouldUpdateAssignments = true;
      } else if (assignedToUserIds && Array.isArray(assignedToUserIds)) {
        // Old format - backward compatibility
        assignmentData = assignedToUserIds.map((userId: any) => ({
          userId: Number(userId),
          isLeader: false
        }));
        shouldUpdateAssignments = true;
      }

      if (shouldUpdateAssignments) {
        const assignedUserIds = assignmentData.map(assignment => assignment.userId);

        // Get current active assignments
        const currentAssignments = await tx.taskAssignment.findMany({
          where: { taskId: Number(id), isActive: true },
        });

        const currentUserIds = currentAssignments.map(a => a.userId);

        // Find users to add and remove
        const usersToAdd = assignedUserIds.filter(userId => !currentUserIds.includes(userId));
        const usersToRemove = currentUserIds.filter(userId => !assignedUserIds.includes(userId));
        const usersToUpdate = assignedUserIds.filter(userId => currentUserIds.includes(userId));

        // Deactivate removed assignments
        if (usersToRemove.length > 0) {
          await tx.taskAssignment.updateMany({
            where: {
              taskId: Number(id),
              userId: { in: usersToRemove },
              isActive: true,
            },
            data: { isActive: false },
          });
        }

        // Update existing assignments (for isLeader changes)
        if (usersToUpdate.length > 0) {
          for (const userId of usersToUpdate) {
            const assignmentInfo = assignmentData.find(a => a.userId === userId);
            if (assignmentInfo) {
              await tx.taskAssignment.updateMany({
                where: {
                  taskId: Number(id),
                  userId: userId,
                  isActive: true,
                },
                data: {
                  isLeader: assignmentInfo.isLeader,
                },
              });
            }
          }
        }

        // Handle new assignments - check for existing inactive assignments first
        if (usersToAdd.length > 0) {
          for (const userId of usersToAdd) {
            const assignmentInfo = assignmentData.find(a => a.userId === userId);
            if (!assignmentInfo) continue;

            // Check if there's an existing inactive assignment
            const existingAssignment = await tx.taskAssignment.findFirst({
              where: {
                taskId: Number(id),
                userId: userId,
                isActive: false,
              },
            });

            if (existingAssignment) {
              // Reactivate existing assignment
              await tx.taskAssignment.update({
                where: { id: existingAssignment.id },
                data: {
                  isActive: true,
                  isLeader: assignmentInfo.isLeader,
                  assignedBy: auth.userId,
                  assignedAt: new Date(),
                },
              });
            } else {
              // Create new assignment
              await tx.taskAssignment.create({
                data: {
                  taskId: Number(id),
                  userId: userId,
                  isLeader: assignmentInfo.isLeader,
                  assignedBy: auth.userId,
                },
              });
            }
          }
        }
      }

      // Fetch the complete updated task with all relations
      const taskWithRelations = await tx.task.findUnique({
        where: { id: Number(id) },
        include: {
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          taskAssignments: {
            where: { isActive: true },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          organization: true,
          department: true,
          status: true,
        },
      });

      // Create task progress entry with Activity Log type
      const updaterInfo = await tx.user.findUnique({
        where: { id: auth.userId },
        select: { firstName: true, lastName: true },
      });

      // Build a list of changes made
      const changes = [];
      if (taskTitle !== undefined && taskTitle !== existingTask!.taskTitle) {
        changes.push(`Title: "${existingTask!.taskTitle}" → "${taskTitle}"`);
      }
      if (taskDescription !== undefined && taskDescription !== existingTask!.taskDescription) {
        changes.push(`Description updated`);
      }
      if (assignedToUserIds && Array.isArray(assignedToUserIds)) {
        changes.push(`Assigned users updated`);
      }
      if (statusId !== undefined && Number(statusId) !== existingTask!.statusId) {
        // Get status names instead of just IDs
        const oldStatus = await tx.taskStatus.findUnique({
          where: { id: existingTask!.statusId },
          select: { name: true, displayName: true },
        });
        const newStatus = await tx.taskStatus.findUnique({
          where: { id: Number(statusId) },
          select: { name: true, displayName: true },
        });
        changes.push(
          `Status: ${oldStatus?.displayName || `ID ${existingTask!.statusId}`} → ${newStatus?.displayName || `ID ${statusId}`}`
        );
      }
      if (
        points !== undefined &&
        (points === null ? null : Number(points)) !== existingTask!.points
      ) {
        changes.push(
          `Points: ${existingTask!.points || 'Not specified'} → ${points || 'Not specified'}`
        );
      }
      if (dueDate !== undefined) {
        const oldDate = existingTask!.dueDate
          ? new Date(existingTask!.dueDate).toLocaleDateString()
          : 'Not set';
        const newDate = dueDate ? new Date(dueDate).toLocaleDateString() : 'Not set';
        if (oldDate !== newDate) {
          changes.push(`Due date: ${oldDate} → ${newDate}`);
        }
      }
      if (organizationId !== undefined && Number(organizationId) !== existingTask!.organizationId) {
        changes.push(`Organization: ID ${existingTask!.organizationId} → ID ${organizationId}`);
      }
      if (departmentId !== undefined && Number(departmentId) !== existingTask!.departmentId) {
        changes.push(`Department: ID ${existingTask!.departmentId} → ID ${departmentId}`);
      }

      // Ensure the Activity Log progress type exists
      const activityLogType = await tx.taskProgressType.upsert({
        where: { id: 3 },
        update: {},
        create: {
          id: 3,
          name: 'log',
          displayName: 'Activity Log',
          color: '#8B5CF6',
          description: 'Log of activity or work done on the task',
          isOwner: false,
          isAdmin: false,
          isMember: false,
        },
      });

      await tx.taskProgress.create({
        data: {
          taskId: updated.id,
          updatedByUserId: auth.userId,
          progressTypeId: activityLogType.id,
          progressDescription: `Task updated by ${updaterInfo?.firstName || ''} ${updaterInfo?.lastName || ''}

${changes.length > 0 ? changes.join('\n') : 'No changes detected'}`,
        },
      });

      return taskWithRelations;
    });

    // Update task chat participants if assignments changed
    try {
      if (assignedToUserIds && Array.isArray(assignedToUserIds)) {
        await updateTaskChatParticipants(Number(id), updatedTask!);
      }
    } catch (chatUpdateError) {
      console.error('Error updating task chat participants:', chatUpdateError);
      // Continue with the response even if chat update fails
    }

    // Send notifications based on what changed
    try {
      // If assignments changed, notify new assignees
      if (assignedToUserIds && Array.isArray(assignedToUserIds)) {
        const assignedUserIdNumbers = assignedToUserIds.map((id: any) => Number(id));

        // Get current assignments before the update
        const currentAssignments = await prisma.taskAssignment.findMany({
          where: { taskId: Number(id), isActive: true },
        });
        const currentUserIds = currentAssignments.map(a => a.userId);

        // Find newly assigned users
        const newlyAssignedUsers = assignedUserIdNumbers.filter(userId => !currentUserIds.includes(userId));

        // Send notifications to newly assigned users
        await Promise.all(
          newlyAssignedUsers.map(userId =>
            taskNotificationService.notifyTaskAssigned(
              updatedTask!.id,
              updatedTask!.taskTitle,
              userId,
              auth.userId
            )
          )
        );
      }

      // If the status changed, send a status change notification
      if (statusId !== undefined && Number(statusId) !== existingTask!.statusId) {
        // Check if the new status is a 'completed' status
        const isCompletedStatus =
          updatedTask!.status.name.toLowerCase().includes('complete') ||
          updatedTask!.status.name.toLowerCase().includes('done');

        // Get all currently assigned users
        const assignedUsers = updatedTask!.taskAssignments.map(assignment => assignment.userId);

        if (isCompletedStatus) {
          // Send a task completed notification to all assigned users
          await Promise.all(
            assignedUsers.map(userId =>
              taskNotificationService.notifyTaskCompleted(
                updatedTask!.id,
                updatedTask!.taskTitle,
                userId,
                auth.userId
              )
            )
          );
        } else {
          // Send a status change notification to all assigned users
          await Promise.all(
            assignedUsers.map(userId =>
              taskNotificationService.notifyTaskStatusChanged(
                updatedTask!.id,
                updatedTask!.taskTitle,
                userId,
                auth.userId,
                oldStatusName,
                updatedTask!.status.name
              )
            )
          );
        }
      }
    } catch (notificationError) {
      console.error('Error sending task update notification:', notificationError);
      // Continue with the response even if notification fails
    }

    return NextResponse.json({ task: updatedTask });
  } catch (error) {
    console.error('Error in PATCH /api/v1/task:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the task' },
      { status: 500 }
    );
  }
}

// DELETE to remove a task
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const taskId = url.searchParams.get('id');

    if (!taskId) {
      return NextResponse.json({ error: 'Task ID is required' }, { status: 400 });
    }

    const taskIdNum = Number(taskId);
    if (isNaN(taskIdNum)) {
      return NextResponse.json({ error: 'Invalid task ID' }, { status: 400 });
    }

    // Find the task
    const existingTask = await prisma.task.findUnique({
      where: { id: taskIdNum },
    });

    if (!existingTask) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Get user role
    const user = await prisma.user.findUnique({
      where: { id: auth.userId },
      select: { userRoleId: true },
    });

    // Check if user has permission to delete the task
    // Only admin (role ID = 1) or the task creator can delete the task
    if (user?.userRoleId !== 1 && existingTask.createdByUserId !== auth.userId) {
      return NextResponse.json(
        {
          error: 'You do not have permission to delete this task',
        },
        { status: 403 }
      );
    }

    // Check if there are related records that need to be deleted first
    const taskProgresses = await prisma.taskProgress.findMany({
      where: { taskId: taskIdNum },
    });

    const pointTransactions = await prisma.pointTransaction.findMany({
      where: { taskId: taskIdNum },
    });

    // Delete related records first
    if (taskProgresses.length > 0) {
      await prisma.taskProgress.deleteMany({
        where: { taskId: taskIdNum },
      });
    }

    if (pointTransactions.length > 0) {
      await prisma.pointTransaction.deleteMany({
        where: { taskId: taskIdNum },
      });
    }

    // Delete the task
    await prisma.task.delete({
      where: { id: taskIdNum },
    });

    return NextResponse.json({
      message: 'Task and related records deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/v1/task:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the task' },
      { status: 500 }
    );
  }
}
