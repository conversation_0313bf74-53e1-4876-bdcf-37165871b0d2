import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import bcrypt from 'bcrypt';
import { getUserOrganizationAdminPrivileges } from '@/lib/permissions';
import { USER_ROLES } from '@/constants/roles';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// GET endpoint to retrieve user information
export async function GET(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const token = getTokenFromRequest(request);
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const payload = verifyJwt(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 });
    }

    // Get user data with role (excluding deleted users)
    const user = await prisma.user.findUnique({
      where: {
        id: payload.userId,
        deletedAt: null, // Only return users who haven't been deleted
      },
      include: {
        userRole: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get user's owned organizations
    const ownedOrganizations = await prisma.organization.findMany({
      where: { ownerUserId: user.id },
      include: {
        departments: {
          include: {
            _count: {
              select: { members: true },
            },
          },
        },
        _count: {
          select: {
            departments: true,
            tasks: true,
          },
        },
      },
    });

    // Get user's departments (including organizations they don't own)
    const userDepartments = await prisma.departmentMember.findMany({
      where: { userId: user.id },
      include: {
        department: {
          include: {
            organization: {
              select: {
                id: true,
                name: true,
                description: true,
                imageUrl: true,
                ownerUserId: true,
              },
            },
            _count: {
              select: { members: true },
            },
          },
        },
      },
    });

    // Get organizations where user is admin
    const adminOrganizations = await prisma.organizationAdmin.findMany({
      where: {
        userId: user.id,
        isActive: true,
      },
      include: {
        organization: {
          include: {
            departments: {
              include: {
                _count: {
                  select: { members: true },
                },
              },
            },
            _count: {
              select: {
                departments: true,
                tasks: true,
              },
            },
          },
        },
      },
    });

    // Get all organizations user is associated with (owned + member + admin)
    const allUserOrganizations = new Map();

    // Add owned organizations
    ownedOrganizations.forEach(org => {
      allUserOrganizations.set(org.id, {
        id: org.id,
        name: org.name,
        description: org.description,
        imageUrl: org.imageUrl,
        isOwner: true,
        isAdmin: false, // Owner is not admin, they have higher privileges
        departmentCount: org._count.departments,
        taskCount: org._count.tasks,
        departments: org.departments.map(dept => {
          // Find if user is a leader of this department
          const membershipInfo = userDepartments.find(ud => ud.department.id === dept.id);
          return {
            id: dept.id,
            name: dept.name,
            description: dept.description,
            memberCount: dept._count.members,
            userJoinedAt: membershipInfo?.joinedAt || null,
            isDirectMember: !!membershipInfo,
            isLeader: membershipInfo?.isLeader || false,
          };
        }),
      });
    });

    // Add organizations from department memberships
    userDepartments.forEach(membership => {
      const org = membership.department.organization;
      if (allUserOrganizations.has(org.id)) {
        // Update existing organization with membership info
        const existingOrg = allUserOrganizations.get(org.id);
        const deptIndex = existingOrg.departments.findIndex(
          (d: any) => d.id === membership.department.id
        );
        if (deptIndex >= 0) {
          existingOrg.departments[deptIndex].userJoinedAt = membership.joinedAt;
          existingOrg.departments[deptIndex].isDirectMember = true;
          existingOrg.departments[deptIndex].isLeader = membership.isLeader;
        }
      } else {
        // Add new organization
        allUserOrganizations.set(org.id, {
          id: org.id,
          name: org.name,
          description: org.description,
          imageUrl: org.imageUrl,
          isOwner: false,
          isAdmin: false, // Will be updated later if user is admin
          departmentCount: 1, // We only know about this one department
          taskCount: 0, // We don't have access to task count for non-owned orgs
          departments: [
            {
              id: membership.department.id,
              name: membership.department.name,
              description: membership.department.description,
              memberCount: membership.department._count.members,
              userJoinedAt: membership.joinedAt,
              isDirectMember: true,
              isLeader: membership.isLeader,
            },
          ],
        });
      }
    });

    // Add organizations where user is admin
    adminOrganizations.forEach(adminRel => {
      const org = adminRel.organization;
      if (allUserOrganizations.has(org.id)) {
        // Update existing organization with admin info
        const existingOrg = allUserOrganizations.get(org.id);
        existingOrg.isAdmin = true;
        // If not owner, update with full organization data
        if (!existingOrg.isOwner) {
          existingOrg.departmentCount = org._count.departments;
          existingOrg.taskCount = org._count.tasks;
          // Update departments with full data
          existingOrg.departments = org.departments.map((dept: any) => {
            const existingDept = existingOrg.departments.find((d: any) => d.id === dept.id);
            const membershipInfo = userDepartments.find(ud => ud.department.id === dept.id);
            return {
              id: dept.id,
              name: dept.name,
              description: dept.description,
              memberCount: dept._count.members,
              userJoinedAt: existingDept?.userJoinedAt || membershipInfo?.joinedAt || null,
              isDirectMember: existingDept?.isDirectMember || !!membershipInfo || false,
              isLeader: membershipInfo?.isLeader || false,
            };
          });
        }
      } else {
        // Add new organization as admin
        allUserOrganizations.set(org.id, {
          id: org.id,
          name: org.name,
          description: org.description,
          imageUrl: org.imageUrl,
          isOwner: false,
          isAdmin: true,
          departmentCount: org._count.departments,
          taskCount: org._count.tasks,
          departments: org.departments.map((dept: any) => {
            const membershipInfo = userDepartments.find(ud => ud.department.id === dept.id);
            return {
              id: dept.id,
              name: dept.name,
              description: dept.description,
              memberCount: dept._count.members,
              userJoinedAt: membershipInfo?.joinedAt || null,
              isDirectMember: !!membershipInfo,
              isLeader: membershipInfo?.isLeader || false,
            };
          }),
        });
      }
    });

    // Convert Map to Array for response
    const organizationsList = Array.from(allUserOrganizations.values());

    // Return user information with comprehensive organization and department data
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        imageUrl: user.imageUrl,
        role: {
          id: user.userRole.id,
          name: user.userRole.name,
          description: user.userRole.description,
          isOwner: user.userRole.isOwner,
          isAdmin: user.userRole.isAdmin,
          isMember: user.userRole.isMember,
          isBot: user.userRole.isBot,
        },
        // Legacy fields for backward compatibility
        organization:
          ownedOrganizations.length > 0
            ? {
                id: ownedOrganizations[0].id,
                name: ownedOrganizations[0].name,
                description: ownedOrganizations[0].description,
                imageUrl: ownedOrganizations[0].imageUrl,
              }
            : null,
        departments: userDepartments.map(membership => ({
          id: membership.department.id,
          name: membership.department.name,
          description: membership.department.description,
          joinedAt: membership.joinedAt,
          organizationId: membership.department.organization.id,
          organizationName: membership.department.organization.name,
          isLeader: membership.isLeader,
        })),
        // Enhanced organization and department data
        organizations: organizationsList,
        summary: {
          totalOrganizations: organizationsList.length,
          ownedOrganizations: organizationsList.filter(org => org.isOwner).length,
          adminOrganizations: organizationsList.filter(org => org.isAdmin && !org.isOwner).length,
          memberOrganizations: organizationsList.filter(org => !org.isOwner && !org.isAdmin).length,
          totalDepartments: userDepartments.length,
          totalDepartmentsAcrossOrgs: organizationsList.reduce(
            (sum, org) => sum + org.departments.length,
            0
          ),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching user data:', error);
    return NextResponse.json({ error: 'Failed to fetch user data' }, { status: 500 });
  }
}

// PATCH endpoint to update user information
export async function PATCH(request: NextRequest) {
  try {
    // Extract and verify JWT token
    const token = getTokenFromRequest(request);
    if (!token) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const payload = verifyJwt(token);
    if (!payload) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();

    // Fields that are allowed to be updated
    const { firstName, lastName, phone, imageUrl, currentPassword, newPassword } = body;

    // Prepare update data
    const updateData: any = {};

    // Add fields to update data if they are provided
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;

    // Handle password update if requested
    if (newPassword && currentPassword) {
      // Get current user with password hash
      const user = await prisma.user.findUnique({
        where: { id: payload.userId },
        select: { passwordHash: true },
      });

      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      // Verify current password
      const passwordMatch = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!passwordMatch) {
        return NextResponse.json({ error: 'Current password is incorrect' }, { status: 400 });
      }

      // Hash new password
      const saltRounds = 10;
      updateData.passwordHash = await bcrypt.hash(newPassword, saltRounds);
    }

    // Update user data
    const updatedUser = await prisma.user.update({
      where: { id: payload.userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
            description: true,
            isOwner: true,
            isAdmin: true,
            isMember: true,
            isBot: true,
          },
        },
      },
    });

    // Return updated user data
    return NextResponse.json({
      message: 'User information updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        phone: updatedUser.phone,
        imageUrl: updatedUser.imageUrl,
        role: {
          id: updatedUser.userRole.id,
          name: updatedUser.userRole.name,
          description: updatedUser.userRole.description,
          isOwner: updatedUser.userRole.isOwner,
          isAdmin: updatedUser.userRole.isAdmin,
          isMember: updatedUser.userRole.isMember,
          isBot: updatedUser.userRole.isBot,
        },
      },
    });
  } catch (error) {
    console.error('Error updating user data:', error);
    return NextResponse.json({ error: 'Failed to update user data' }, { status: 500 });
  }
}
