import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import { 
  repairChatMemberships, 
  validateChatConsistency, 
  getChatSyncStatus 
} from '@/services/chatSyncService';

/**
 * GET - Get chat synchronization status or validate consistency
 * Query parameters:
 * - action: 'status' | 'validate'
 * - adminUserId: number (for status action)
 * - organizationIds: number[] (for status action)
 * - organizationId: number (optional, for validate action)
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners can access chat sync operations
    if (!auth.isOwner) {
      return NextResponse.json({ error: 'Only owners can access chat synchronization' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'status') {
      const adminUserId = searchParams.get('adminUserId');
      const organizationIdsParam = searchParams.get('organizationIds');

      if (!adminUserId || !organizationIdsParam) {
        return NextResponse.json({ 
          error: 'adminUserId and organizationIds are required for status action' 
        }, { status: 400 });
      }

      const organizationIds = organizationIdsParam.split(',').map(id => parseInt(id, 10));
      
      if (organizationIds.some(id => isNaN(id))) {
        return NextResponse.json({ error: 'Invalid organization IDs' }, { status: 400 });
      }

      const status = await getChatSyncStatus(parseInt(adminUserId, 10), organizationIds);
      
      return NextResponse.json({
        action: 'status',
        adminUserId: parseInt(adminUserId, 10),
        organizations: status,
      });
    }

    if (action === 'validate') {
      const organizationIdParam = searchParams.get('organizationId');
      const organizationId = organizationIdParam ? parseInt(organizationIdParam, 10) : undefined;

      if (organizationIdParam && isNaN(organizationId!)) {
        return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
      }

      const validation = await validateChatConsistency(organizationId);
      
      return NextResponse.json({
        action: 'validate',
        organizationId,
        ...validation,
      });
    }

    return NextResponse.json({ error: 'Invalid action. Use "status" or "validate"' }, { status: 400 });
  } catch (error) {
    console.error('Error in admin chat sync GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST - Repair chat memberships
 * Body:
 * - action: 'repair'
 * - organizationId: number (optional)
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners can perform chat sync operations
    if (!auth.isOwner) {
      return NextResponse.json({ error: 'Only owners can perform chat synchronization' }, { status: 403 });
    }

    const body = await request.json();
    const { action, organizationId } = body;

    if (action !== 'repair') {
      return NextResponse.json({ error: 'Invalid action. Use "repair"' }, { status: 400 });
    }

    if (organizationId && (typeof organizationId !== 'number' || organizationId <= 0)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    const result = await repairChatMemberships(organizationId);
    
    return NextResponse.json({
      action: 'repair',
      organizationId,
      ...result,
    });
  } catch (error) {
    console.error('Error in admin chat sync POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT - Manual sync operation (for emergency use)
 * Body:
 * - adminUserId: number
 * - organizationIds: number[]
 * - operation: 'add' | 'remove'
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners can perform manual sync operations
    if (!auth.isOwner) {
      return NextResponse.json({ error: 'Only owners can perform manual sync operations' }, { status: 403 });
    }

    const body = await request.json();
    const { adminUserId, organizationIds, operation } = body;

    // Validate input
    if (!adminUserId || typeof adminUserId !== 'number') {
      return NextResponse.json({ error: 'Valid adminUserId is required' }, { status: 400 });
    }

    if (!Array.isArray(organizationIds) || organizationIds.some(id => typeof id !== 'number')) {
      return NextResponse.json({ error: 'Valid organizationIds array is required' }, { status: 400 });
    }

    if (!['add', 'remove'].includes(operation)) {
      return NextResponse.json({ error: 'Operation must be "add" or "remove"' }, { status: 400 });
    }

    // Import the sync functions
    const { syncAdminChatsOnCreation, syncAdminChatsOnRemoval } = await import('@/services/chatSyncService');

    let result;
    if (operation === 'add') {
      result = await syncAdminChatsOnCreation(adminUserId, organizationIds);
    } else {
      result = await syncAdminChatsOnRemoval(adminUserId, organizationIds);
    }

    return NextResponse.json({
      operation,
      adminUserId,
      organizationIds,
      ...result,
    });
  } catch (error) {
    console.error('Error in admin chat sync PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
