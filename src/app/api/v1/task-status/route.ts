import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { log } from 'console';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// GET all task statuses
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Build filter conditions based on user role
    const filter: { isMemberDisplay?: boolean } = {};

    // If user has isMember role, only show statuses with isMemberDisplay = true
    // This applies even if they also have other roles
    if (auth.isMember) {
      filter.isMemberDisplay = true;
    }

    // Fetch task statuses with filtering and sorting
    const taskStatuses = await prisma.taskStatus.findMany({
      where: filter,
      orderBy: {
        index: 'asc', // Sort by index in ascending order
      },
      include: {
        // Include transitions where this status is the target status
        toStatusTransitions: {
          include: {
            fromStatus: true, // Include the source status details
          },
        },
      },
    });

    // Process the task statuses to include only valid transitions based on user role
    const processedTaskStatuses = taskStatuses.map(status => {
      // Filter transitions based on user role
      const filteredTransitions = status.toStatusTransitions?.filter(transition => {
        if (auth.isOwner && transition.allowOwner) return true;
        if (auth.isAdmin && transition.allowAdmin) return true;
        if (auth.isMember && transition.allowMember) return true;
        return false;
      });

      return {
        ...status,
        toStatusTransitions: filteredTransitions,
      };
    });

    return NextResponse.json({ taskStatuses: processedTaskStatuses });
  } catch (error) {
    console.error('Error in GET /api/v1/task-status:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching task statuses' },
      { status: 500 }
    );
  }
}
