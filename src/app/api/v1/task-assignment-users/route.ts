import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to authenticate user
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authorization token required', status: 401 };
  }

  try {
    const decoded = await verifyJwt(token);
    if (!decoded || !decoded.userId) {
      return { error: 'Invalid token payload', status: 401 };
    }
    return { userId: decoded.userId };
  } catch (error) {
    return { error: 'Invalid or expired token', status: 401 };
  }
}

// Helper function to verify organization access
async function verifyOrganizationAccess(organizationId: number, userId: number) {
  // Check if user is the owner of the organization
  const organization = await prisma.organization.findFirst({
    where: {
      id: organizationId,
      ownerUserId: userId,
    },
  });

  if (organization) {
    return { hasAccess: true };
  }

  // Check if user is an admin of the organization
  const organizationAdmin = await prisma.organizationAdmin.findFirst({
    where: {
      organizationId,
      userId,
      isActive: true,
    },
  });

  if (organizationAdmin) {
    return { hasAccess: true };
  }

  // Check if user is a member of any department in the organization
  const departmentMember = await prisma.departmentMember.findFirst({
    where: {
      userId,
      department: {
        organizationId,
      },
    },
  });

  if (departmentMember) {
    return { hasAccess: true };
  }

  return { error: 'You do not have access to this organization', status: 403 };
}

// Helper function to verify department access
async function verifyDepartmentAccess(departmentId: number, userId: number) {
  // Get department with organization info
  const department = await prisma.department.findUnique({
    where: { id: departmentId },
    include: { organization: true },
  });

  if (!department) {
    return { error: 'Department not found', status: 404 };
  }

  // Check organization access first
  const orgAccess = await verifyOrganizationAccess(department.organizationId, userId);
  if ('error' in orgAccess) {
    return orgAccess;
  }

  return { hasAccess: true, department };
}

/**
 * GET API for task assignment users
 * 
 * This endpoint returns a combined list of users that can be assigned to tasks:
 * - Regular organization members (from departments)
 * - Organization admins
 * - Organization owners
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization
 * - departmentId: Optional. If provided, includes department members + org admins + owner
 * - name: Optional. Search filter for user names and email
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');
    const nameSearch = url.searchParams.get('name');

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    const orgIdNum = Number(organizationId);
    if (isNaN(orgIdNum)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    // Verify access to the organization
    const accessCheck = await verifyOrganizationAccess(orgIdNum, auth.userId);
    if ('error' in accessCheck) {
      return NextResponse.json({ error: accessCheck.error }, { status: accessCheck.status });
    }

    const users: any[] = [];

    // 1. Get organization owner
    const organization = await prisma.organization.findUnique({
      where: { id: orgIdNum },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRoleId: true,
            deletedAt: true,
          },
        },
      },
    });

    if (organization?.owner) {
      const owner = organization.owner;
      const ownerName = `${owner.firstName} ${owner.lastName}`;
      
      // Apply name filter if provided
      const matchesNameFilter = !nameSearch || 
        ownerName.toLowerCase().includes(nameSearch.toLowerCase()) ||
        owner.email.toLowerCase().includes(nameSearch.toLowerCase());

      if (matchesNameFilter) {
        users.push({
          user: owner,
          departmentId: null,
          isLeader: false,
          department: {
            id: null,
            name: null,
            organization: {
              id: organization.id,
              name: organization.name,
            },
          },
          isOwner: true,
          isAdmin: false,
        });
      }
    }

    // 2. Get organization admins
    const nameFilter = nameSearch ? {
      OR: [
        {
          firstName: {
            contains: nameSearch,
            mode: 'insensitive' as const,
          },
        },
        {
          lastName: {
            contains: nameSearch,
            mode: 'insensitive' as const,
          },
        },
        {
          email: {
            contains: nameSearch,
            mode: 'insensitive' as const,
          },
        },
      ],
    } : undefined;

    const organizationAdmins = await prisma.organizationAdmin.findMany({
      where: {
        organizationId: orgIdNum,
        isActive: true,
        ...(nameFilter && { user: nameFilter }),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRoleId: true,
            deletedAt: true,
          },
        },
      },
    });

    // Add admins to users list (exclude if already added as owner)
    for (const admin of organizationAdmins) {
      if (!users.find(u => u.user.id === admin.user.id)) {
        users.push({
          user: admin.user,
          departmentId: null,
          isLeader: false,
          department: {
            id: null,
            name: null,
            organization: organization ? {
              id: organization.id,
              name: organization.name,
            } : null,
          },
          isOwner: false,
          isAdmin: true,
        });
      }
    }

    // 3. Get department members
    let departmentMembers: any[] = [];

    if (departmentId) {
      // Get members from specific department
      const deptIdNum = Number(departmentId);
      if (isNaN(deptIdNum)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }

      // Verify access to the department
      const deptAccessCheck = await verifyDepartmentAccess(deptIdNum, auth.userId);
      if ('error' in deptAccessCheck) {
        return NextResponse.json({ error: deptAccessCheck.error }, { status: deptAccessCheck.status });
      }

      departmentMembers = await prisma.departmentMember.findMany({
        where: {
          departmentId: deptIdNum,
          user: {
            userRoleId: 3, // Regular members only for department members
            ...(nameFilter || {}),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRoleId: true,
              deletedAt: true,
            },
          },
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });
    } else {
      // Get members from all departments in the organization
      const departments = await prisma.department.findMany({
        where: { organizationId: orgIdNum },
        select: { id: true },
      });

      const departmentIds = departments.map(dept => dept.id);

      departmentMembers = await prisma.departmentMember.findMany({
        where: {
          departmentId: { in: departmentIds },
          user: {
            userRoleId: 3, // Regular members only for department members
            ...(nameFilter || {}),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRoleId: true,
              deletedAt: true,
            },
          },
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });
    }

    // Add department members to users list (exclude if already added as owner or admin)
    for (const member of departmentMembers) {
      if (!users.find(u => u.user.id === member.user.id)) {
        users.push({
          user: member.user,
          departmentId: member.departmentId,
          isLeader: member.isLeader,
          department: member.department,
          isOwner: false,
          isAdmin: false,
        });
      }
    }

    // Transform to match the expected API response format
    const members = users.map(userRecord => ({
      user: userRecord.user,
      departmentId: userRecord.departmentId,
      isLeader: userRecord.isLeader,
      department: userRecord.department,
      isOwner: userRecord.isOwner,
      isAdmin: userRecord.isAdmin,
    }));

    return NextResponse.json({ members });
  } catch (error) {
    console.error('Error fetching task assignment users:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}
