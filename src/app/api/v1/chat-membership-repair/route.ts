import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { repairOrganizationChatMemberships, addAdminToAllChats } from '@/services/organizationChatService';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

/**
 * GET - Diagnose chat membership issues for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get user's organization memberships
    const userOrganizations = await prisma.organization.findMany({
      where: {
        OR: [
          { ownerUserId: auth.userId }, // User is owner
          {
            organizationAdmins: {
              some: {
                userId: auth.userId,
                isActive: true,
              },
            },
          }, // User is admin
        ],
      },
      include: {
        chats: {
          where: { chatType: 'ORGANIZATION' },
          include: {
            chatUsers: {
              where: { userId: auth.userId },
            },
          },
        },
        departments: {
          include: {
            chats: {
              where: { chatType: 'DEPARTMENT' },
              include: {
                chatUsers: {
                  where: { userId: auth.userId },
                },
              },
            },
          },
        },
      },
    });

    const diagnosis = {
      userId: auth.userId,
      userRole: { isOwner: auth.isOwner, isAdmin: auth.isAdmin, isMember: auth.isMember },
      organizations: userOrganizations.map(org => ({
        id: org.id,
        name: org.name,
        isOwner: org.ownerUserId === auth.userId,
        organizationChat: {
          exists: org.chats.length > 0,
          isMember: org.chats.length > 0 && org.chats[0].chatUsers.length > 0,
          chatId: org.chats.length > 0 ? org.chats[0].id : null,
        },
        departments: org.departments.map(dept => ({
          id: dept.id,
          name: dept.name,
          departmentChat: {
            exists: dept.chats.length > 0,
            isMember: dept.chats.length > 0 && dept.chats[0].chatUsers.length > 0,
            chatId: dept.chats.length > 0 ? dept.chats[0].id : null,
          },
        })),
      })),
    };

    return NextResponse.json({ diagnosis });
  } catch (error) {
    console.error('Error diagnosing chat membership:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST - Repair chat membership issues for the current user
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { action = 'repair' } = body;

    if (action !== 'repair') {
      return NextResponse.json({ error: 'Invalid action. Use "repair"' }, { status: 400 });
    }

    // Get user's organization IDs where they should have access
    const userOrganizations = await prisma.organization.findMany({
      where: {
        OR: [
          { ownerUserId: auth.userId }, // User is owner
          {
            organizationAdmins: {
              some: {
                userId: auth.userId,
                isActive: true,
              },
            },
          }, // User is admin
        ],
      },
      select: { id: true },
    });

    const organizationIds = userOrganizations.map(org => org.id);

    if (organizationIds.length === 0) {
      return NextResponse.json({
        message: 'No organizations found for user',
        repaired: 0,
        errors: [],
      });
    }

    // Repair chat memberships
    const result = await prisma.$transaction(async (tx) => {
      const repairResult = await addAdminToAllChats(auth.userId, organizationIds, tx);
      return repairResult;
    });

    return NextResponse.json({
      message: 'Chat membership repair completed',
      organizationChats: result.organizationChats,
      departmentChats: result.departmentChats,
      errors: result.errors,
    });
  } catch (error) {
    console.error('Error repairing chat membership:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
