import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';

/**
 * GET API for AssistantMessageType
 *
 * This API retrieves all AssistantMessageType records.
 * These are needed for creating AssistantMessage records.
 *
 * Path: /api/v1/assistant-message-type
 * Returns: List of all AssistantMessageType records
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get all AssistantMessageType records
    const assistantMessageTypes = await prisma.assistantMessageType.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        id: 'asc',
      },
    });

    return NextResponse.json({
      assistantMessageTypes,
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/v1/assistant-message-type:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching AssistantMessageType records' },
      { status: 500 }
    );
  }
}
