/**
 * Test file for the unread-count API endpoint
 * 
 * This file contains tests for:
 * - Getting unread count for a specific chat
 * - Getting unread counts for all user's chats
 * - Getting total unread count across all chats
 * - Error handling and validation
 */

import { NextRequest } from 'next/server';
import { GET } from './route';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    chatUser: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    chatMessage: {
      count: jest.fn(),
    },
  },
}));

jest.mock('@/lib/jwt', () => ({
  verifyJwt: jest.fn(),
}));

// Mock NextRequest
const createMockRequest = (searchParams: Record<string, string> = {}, headers: Record<string, string> = {}) => {
  const url = new URL('http://localhost:3000/api/v1/chat-message/unread-count');
  Object.entries(searchParams).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });

  return {
    headers: {
      get: jest.fn((key: string) => headers[key] || null),
    },
    url: url.toString(),
  } as unknown as NextRequest;
};

describe('/api/v1/chat-message/unread-count', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (verifyJwt as jest.Mock).mockReturnValue({ userId: 1 });
  });

  describe('GET - Get unread counts', () => {
    it('should get unread count for a specific chat', async () => {
      const mockChatUser = {
        chatId: 1,
        userId: 1,
      };

      (prisma.chatUser.findUnique as jest.Mock).mockResolvedValue(mockChatUser);
      (prisma.chatMessage.count as jest.Mock).mockResolvedValue(5);

      const request = createMockRequest(
        { chatId: '1' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.chatId).toBe(1);
      expect(data.unreadCount).toBe(5);
      expect(prisma.chatMessage.count).toHaveBeenCalledWith({
        where: {
          chatId: 1,
          userId: { not: 1 },
          messageReads: {
            none: {
              userId: 1,
            },
          },
        },
      });
    });

    it('should return error when user is not a chat participant', async () => {
      (prisma.chatUser.findUnique as jest.Mock).mockResolvedValue(null);

      const request = createMockRequest(
        { chatId: '1' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('You are not a participant in this chat');
    });

    it('should get total unread count across all chats', async () => {
      const mockUserChats = [
        { chatId: 1 },
        { chatId: 2 },
        { chatId: 3 },
      ];

      (prisma.chatUser.findMany as jest.Mock).mockResolvedValue(mockUserChats);
      (prisma.chatMessage.count as jest.Mock).mockResolvedValue(15);

      const request = createMockRequest(
        { total: 'true' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.totalUnreadCount).toBe(15);
      expect(prisma.chatMessage.count).toHaveBeenCalledWith({
        where: {
          chatId: { in: [1, 2, 3] },
          userId: { not: 1 },
          messageReads: {
            none: {
              userId: 1,
            },
          },
        },
      });
    });

    it('should return zero total unread count when user has no chats', async () => {
      (prisma.chatUser.findMany as jest.Mock).mockResolvedValue([]);

      const request = createMockRequest(
        { total: 'true' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.totalUnreadCount).toBe(0);
    });

    it('should get unread counts for all user chats', async () => {
      const mockUserChats = [
        {
          chatId: 1,
          chat: {
            id: 1,
            name: 'Chat 1',
            chatType: 'PRIVATE',
          },
        },
        {
          chatId: 2,
          chat: {
            id: 2,
            name: 'Chat 2',
            chatType: 'TASK',
          },
        },
      ];

      (prisma.chatUser.findMany as jest.Mock).mockResolvedValue(mockUserChats);
      (prisma.chatMessage.count as jest.Mock)
        .mockResolvedValueOnce(3) // First chat has 3 unread
        .mockResolvedValueOnce(7); // Second chat has 7 unread

      const request = createMockRequest(
        {},
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.unreadCounts).toHaveLength(2);
      expect(data.unreadCounts[0]).toEqual({
        chatId: 1,
        chatName: 'Chat 1',
        chatType: 'PRIVATE',
        unreadCount: 3,
      });
      expect(data.unreadCounts[1]).toEqual({
        chatId: 2,
        chatName: 'Chat 2',
        chatType: 'TASK',
        unreadCount: 7,
      });
    });

    it('should return empty array when user has no chats', async () => {
      (prisma.chatUser.findMany as jest.Mock).mockResolvedValue([]);

      const request = createMockRequest(
        {},
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.unreadCounts).toEqual([]);
    });

    it('should return error for invalid chat ID', async () => {
      const request = createMockRequest(
        { chatId: 'invalid' },
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid chat ID');
    });

    it('should handle authentication errors', async () => {
      const request = createMockRequest({ chatId: '1' });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });
  });
});
