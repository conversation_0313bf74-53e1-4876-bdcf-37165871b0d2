import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

/**
 * GET API to get unread message counts
 *
 * This API supports multiple modes:
 *
 * 1. Get unread count for a specific chat:
 *    - Path: /api/v1/chat-message/unread-count?chatId=123
 *    - Returns: { chatId: 123, unreadCount: 5 }
 *
 * 2. Get unread counts for all user's chats:
 *    - Path: /api/v1/chat-message/unread-count
 *    - Returns: { unreadCounts: [{ chatId: 123, unreadCount: 5 }, ...] }
 *
 * 3. Get total unread count across all chats:
 *    - Path: /api/v1/chat-message/unread-count?total=true
 *    - Returns: { totalUnreadCount: 15 }
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');
    const total = searchParams.get('total') === 'true';

    // Mode 1: Get unread count for a specific chat
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      // Check if user is a participant in the chat
      const chatUser = await prisma.chatUser.findUnique({
        where: {
          chatId_userId: {
            chatId: chatIdNum,
            userId: auth.userId,
          },
        },
      });

      if (!chatUser) {
        return NextResponse.json(
          { error: 'You are not a participant in this chat' },
          { status: 403 }
        );
      }

      // Count unread messages in the chat
      const unreadCount = await prisma.chatMessage.count({
        where: {
          chatId: chatIdNum,
          userId: { not: auth.userId }, // Exclude own messages
          messageReads: {
            none: {
              userId: auth.userId,
            },
          },
        },
      });

      return NextResponse.json({
        chatId: chatIdNum,
        unreadCount,
      });
    }

    // Mode 3: Get total unread count across all chats
    if (total) {
      // Get all chats where user is a participant
      const userChats = await prisma.chatUser.findMany({
        where: { userId: auth.userId },
        select: { chatId: true },
      });

      const chatIds = userChats.map(uc => uc.chatId);

      if (chatIds.length === 0) {
        return NextResponse.json({ totalUnreadCount: 0 });
      }

      // Count total unread messages across all user's chats
      const totalUnreadCount = await prisma.chatMessage.count({
        where: {
          chatId: { in: chatIds },
          userId: { not: auth.userId }, // Exclude own messages
          messageReads: {
            none: {
              userId: auth.userId,
            },
          },
        },
      });

      return NextResponse.json({ totalUnreadCount });
    }

    // Mode 2: Get unread counts for all user's chats
    // Get all chats where user is a participant
    const userChats = await prisma.chatUser.findMany({
      where: { userId: auth.userId },
      include: {
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
          },
        },
      },
    });

    if (userChats.length === 0) {
      return NextResponse.json({ unreadCounts: [] });
    }

    // Get unread counts for each chat
    const unreadCounts = await Promise.all(
      userChats.map(async (userChat) => {
        const unreadCount = await prisma.chatMessage.count({
          where: {
            chatId: userChat.chatId,
            userId: { not: auth.userId }, // Exclude own messages
            messageReads: {
              none: {
                userId: auth.userId,
              },
            },
          },
        });

        return {
          chatId: userChat.chatId,
          chatName: userChat.chat.name,
          chatType: userChat.chat.chatType,
          unreadCount,
        };
      })
    );

    return NextResponse.json({ unreadCounts });
  } catch (error) {
    console.error('Error getting unread counts:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
