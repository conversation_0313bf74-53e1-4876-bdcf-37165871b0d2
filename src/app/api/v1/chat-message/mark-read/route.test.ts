/**
 * Test file for the mark-read API endpoint
 * 
 * This file contains tests for:
 * - Marking single messages as read
 * - Marking all messages in a chat as read
 * - Marking messages as unread
 * - Error handling and validation
 */

import { NextRequest } from 'next/server';
import { POST, DELETE } from './route';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    chatMessage: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    chatUser: {
      findUnique: jest.fn(),
    },
    messageRead: {
      upsert: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
  },
}));

jest.mock('@/lib/jwt', () => ({
  verifyJwt: jest.fn(),
}));

// Mock NextRequest
const createMockRequest = (body: any, headers: Record<string, string> = {}) => {
  return {
    json: jest.fn().mockResolvedValue(body),
    headers: {
      get: jest.fn((key: string) => headers[key] || null),
    },
    url: 'http://localhost:3000/api/v1/chat-message/mark-read',
  } as unknown as NextRequest;
};

describe('/api/v1/chat-message/mark-read', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (verifyJwt as jest.Mock).mockReturnValue({ userId: 1 });
  });

  describe('POST - Mark messages as read', () => {
    it('should mark a single message as read', async () => {
      const mockMessage = {
        id: 1,
        userId: 2,
        chat: {
          chatUsers: [{ userId: 1 }],
        },
      };

      const mockMessageRead = {
        id: 1,
        messageId: 1,
        userId: 1,
        readAt: new Date(),
      };

      (prisma.chatMessage.findUnique as jest.Mock).mockResolvedValue(mockMessage);
      (prisma.messageRead.upsert as jest.Mock).mockResolvedValue(mockMessageRead);

      const request = createMockRequest(
        { messageId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Message marked as read');
      expect(data.messageRead).toEqual(mockMessageRead);
      expect(prisma.messageRead.upsert).toHaveBeenCalledWith({
        where: {
          messageId_userId: {
            messageId: 1,
            userId: 1,
          },
        },
        update: {
          readAt: expect.any(Date),
        },
        create: {
          messageId: 1,
          userId: 1,
          readAt: expect.any(Date),
        },
      });
    });

    it('should mark all messages in a chat as read', async () => {
      const mockChatUser = {
        chatId: 1,
        userId: 1,
      };

      const mockMessages = [
        { id: 1 },
        { id: 2 },
        { id: 3 },
      ];

      const mockCreateResult = { count: 3 };

      (prisma.chatUser.findUnique as jest.Mock).mockResolvedValue(mockChatUser);
      (prisma.chatMessage.findMany as jest.Mock).mockResolvedValue(mockMessages);
      (prisma.messageRead.createMany as jest.Mock).mockResolvedValue(mockCreateResult);

      const request = createMockRequest(
        { chatId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Messages marked as read');
      expect(data.markedCount).toBe(3);
      expect(data.totalMessages).toBe(3);
      expect(prisma.messageRead.createMany).toHaveBeenCalledWith({
        data: [
          { messageId: 1, userId: 1, readAt: expect.any(Date) },
          { messageId: 2, userId: 1, readAt: expect.any(Date) },
          { messageId: 3, userId: 1, readAt: expect.any(Date) },
        ],
        skipDuplicates: true,
      });
    });

    it('should return error when trying to mark own message as read', async () => {
      const mockMessage = {
        id: 1,
        userId: 1, // Same as authenticated user
        chat: {
          chatUsers: [{ userId: 1 }],
        },
      };

      (prisma.chatMessage.findUnique as jest.Mock).mockResolvedValue(mockMessage);

      const request = createMockRequest(
        { messageId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Cannot mark your own message as read');
    });

    it('should return error when user is not a chat participant', async () => {
      const mockMessage = {
        id: 1,
        userId: 2,
        chat: {
          chatUsers: [], // User not in chat
        },
      };

      (prisma.chatMessage.findUnique as jest.Mock).mockResolvedValue(mockMessage);

      const request = createMockRequest(
        { messageId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('You are not a participant in this chat');
    });

    it('should return error when neither messageId nor chatId is provided', async () => {
      const request = createMockRequest(
        {},
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Either messageId or chatId is required');
    });

    it('should return error when both messageId and chatId are provided', async () => {
      const request = createMockRequest(
        { messageId: 1, chatId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Cannot specify both messageId and chatId');
    });
  });

  describe('DELETE - Mark message as unread', () => {
    it('should mark a message as unread', async () => {
      const mockMessage = {
        id: 1,
        userId: 2,
        chat: {
          chatUsers: [{ userId: 1 }],
        },
      };

      const mockDeleteResult = { count: 1 };

      (prisma.chatMessage.findUnique as jest.Mock).mockResolvedValue(mockMessage);
      (prisma.messageRead.deleteMany as jest.Mock).mockResolvedValue(mockDeleteResult);

      const request = createMockRequest(
        { messageId: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Message marked as unread');
      expect(data.deletedCount).toBe(1);
      expect(prisma.messageRead.deleteMany).toHaveBeenCalledWith({
        where: {
          messageId: 1,
          userId: 1,
        },
      });
    });

    it('should return error when messageId is not provided', async () => {
      const request = createMockRequest(
        {},
        { authorization: 'Bearer valid-token' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Message ID is required');
    });
  });
});
