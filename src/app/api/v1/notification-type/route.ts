import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

// GET notification types (all or single by ID)
export async function GET(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    // If ID is provided, get a specific notification type
    if (id) {
      const notificationTypeId = parseInt(id);
      if (isNaN(notificationTypeId)) {
        return NextResponse.json({ error: 'Invalid notification type ID' }, { status: 400 });
      }

      // Find the notification type
      const notificationType = await prisma.notificationType.findUnique({
        where: { id: notificationTypeId },
      });

      if (!notificationType) {
        return NextResponse.json({ error: 'Notification type not found' }, { status: 404 });
      }

      return NextResponse.json(notificationType);
    }

    // Otherwise, get all notification types with pagination and filtering
    // Parse pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Parse filter parameters
    const isActive = url.searchParams.get('isActive');
    const search = url.searchParams.get('search');

    // Build filter conditions
    const filterConditions: any = {};

    if (isActive !== null) {
      filterConditions.isActive = isActive === 'true';
    }

    if (search) {
      filterConditions.OR = [
        { name: { contains: search } },
        { displayName: { contains: search } },
        { description: { contains: search } },
      ];
    }

    // Get notification types with pagination
    const notificationTypes = await prisma.notificationType.findMany({
      where: filterConditions,
      orderBy: {
        name: 'asc',
      },
      skip: offset,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.notificationType.count({
      where: filterConditions,
    });

    return NextResponse.json({
      data: notificationTypes,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching notification types:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching notification types' },
      { status: 500 }
    );
  }
}

// POST to create a new notification type
export async function POST(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Parse the request body
    const body = await request.json();
    const { name, displayName, description, template, color, isActive } = body;

    // Validate required fields
    if (!name || !displayName || !template) {
      return NextResponse.json(
        { error: 'Name, display name, and template are required' },
        { status: 400 }
      );
    }

    // Check if notification type with the same name already exists
    const existingType = await prisma.notificationType.findUnique({
      where: { name },
    });

    if (existingType) {
      return NextResponse.json(
        { error: 'A notification type with this name already exists' },
        { status: 409 }
      );
    }

    // Create the notification type
    const notificationType = await prisma.notificationType.create({
      data: {
        name,
        displayName,
        description,
        template,
        color,
        isActive: isActive !== undefined ? isActive : true,
      },
    });

    return NextResponse.json({
      message: 'Notification type created successfully',
      notificationType,
    });
  } catch (error) {
    console.error('Error creating notification type:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the notification type' },
      { status: 500 }
    );
  }
}

// PUT to update a notification type
export async function PUT(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const idParam = url.searchParams.get('id');

    if (!idParam) {
      return NextResponse.json({ error: 'Notification type ID is required' }, { status: 400 });
    }

    const id = parseInt(idParam);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid notification type ID' }, { status: 400 });
    }

    // Check if the notification type exists
    const existingType = await prisma.notificationType.findUnique({
      where: { id },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Notification type not found' }, { status: 404 });
    }

    // Parse the request body
    const body = await request.json();
    const { name, displayName, description, template, color, isActive } = body;

    // Validate required fields
    if (!name || !displayName || !template) {
      return NextResponse.json(
        { error: 'Name, display name, and template are required' },
        { status: 400 }
      );
    }

    // Check if another notification type with the same name exists (excluding the current one)
    if (name !== existingType.name) {
      const duplicateName = await prisma.notificationType.findUnique({
        where: { name },
      });

      if (duplicateName) {
        return NextResponse.json(
          { error: 'A notification type with this name already exists' },
          { status: 409 }
        );
      }
    }

    // Update the notification type
    const updatedType = await prisma.notificationType.update({
      where: { id },
      data: {
        name,
        displayName,
        description,
        template,
        color,
        isActive: isActive !== undefined ? isActive : existingType.isActive,
      },
    });

    return NextResponse.json({
      message: 'Notification type updated successfully',
      notificationType: updatedType,
    });
  } catch (error) {
    console.error('Error updating notification type:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the notification type' },
      { status: 500 }
    );
  }
}

// DELETE to remove a notification type
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate the user
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const idParam = url.searchParams.get('id');

    if (!idParam) {
      return NextResponse.json({ error: 'Notification type ID is required' }, { status: 400 });
    }

    const id = parseInt(idParam);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid notification type ID' }, { status: 400 });
    }

    // Check if the notification type exists
    const existingType = await prisma.notificationType.findUnique({
      where: { id },
    });

    if (!existingType) {
      return NextResponse.json({ error: 'Notification type not found' }, { status: 404 });
    }

    // Check if there are any notifications using this type
    const notificationsCount = await prisma.notification.count({
      where: { typeId: id },
    });

    if (notificationsCount > 0) {
      // Instead of deleting, just mark as inactive
      const deactivatedType = await prisma.notificationType.update({
        where: { id },
        data: { isActive: false },
      });

      return NextResponse.json({
        message:
          'Notification type has existing notifications and was deactivated instead of deleted',
        notificationType: deactivatedType,
      });
    }

    // Delete the notification type if no notifications are using it
    await prisma.notificationType.delete({
      where: { id },
    });

    return NextResponse.json({
      message: 'Notification type deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting notification type:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the notification type' },
      { status: 500 }
    );
  }
}
