import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

/**
 * GET API for AssistantChatUser
 *
 * This API supports multiple modes:
 *
 * 1. Get a single AssistantChatUser by ID:
 *    - Path: /api/v1/assistant-chat-user?id=123
 *    - Returns: Detailed AssistantChatUser information
 *
 * 2. Get AssistantChatUsers filtered by user ID and chat ID:
 *    - Path: /api/v1/assistant-chat-user?userId=123&chatId=456
 *    - Optional query parameters:
 *      - userId: Filter by user ID
 *      - chatId: Filter by chat ID
 *      - isActive: Filter by active status (true/false)
 *    - Returns: List of AssistantChatUsers matching the filters
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const userId = searchParams.get('userId');
    const chatId = searchParams.get('chatId');
    const isActive = searchParams.get('isActive');

    // Mode 1: Get single AssistantChatUser by ID
    if (id) {
      const assistantChatUserId = Number(id);
      if (isNaN(assistantChatUserId)) {
        return NextResponse.json({ error: 'Invalid AssistantChatUser ID' }, { status: 400 });
      }

      const assistantChatUser = await prisma.assistantChatUser.findUnique({
        where: { id: assistantChatUserId },
        include: {
          chat: {
            select: {
              id: true,
              name: true,
              chatType: true,
              isBot: true,
              botDuration: true,
              createdAt: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                  isOwner: true,
                  isAdmin: true,
                  isMember: true,
                },
              },
            },
          },
        },
      });

      if (!assistantChatUser) {
        return NextResponse.json({ error: 'AssistantChatUser not found' }, { status: 404 });
      }

      // Check if user has access to this record
      if (!auth.isOwner && !auth.isAdmin && assistantChatUser.userId !== auth.userId) {
        return NextResponse.json({ error: 'Access denied' }, { status: 403 });
      }

      return NextResponse.json({
        assistantChatUser,
        userRole: {
          isOwner: auth.isOwner,
          isAdmin: auth.isAdmin,
          isMember: auth.isMember,
        },
      });
    }

    // Mode 2: Get filtered list of AssistantChatUsers
    const whereConditions: any = {};

    // Apply filters
    if (userId) {
      const userIdNum = Number(userId);
      if (isNaN(userIdNum)) {
        return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
      }
      whereConditions.userId = userIdNum;
    }

    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }
      whereConditions.chatId = chatIdNum;
    }

    if (isActive !== null && isActive !== undefined) {
      whereConditions.isActive = isActive === 'true';
    }

    // Non-admin users can only see their own records
    if (!auth.isOwner && !auth.isAdmin) {
      whereConditions.userId = auth.userId;
    }

    const assistantChatUsers = await prisma.assistantChatUser.findMany({
      where: whereConditions,
      include: {
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
            isBot: true,
            botDuration: true,
            createdAt: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({
      assistantChatUsers,
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/v1/assistant-chat-user:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching AssistantChatUser records' },
      { status: 500 }
    );
  }
}

/**
 * POST API to create a new AssistantChatUser record
 *
 * Request body:
 * {
 *   "name": "Assistant Chat Name" (required),
 *   "chatId": 123 (required),
 *   "isActive": true (optional, defaults to true)
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { name, chatId, isActive = true } = body;

    // Validate required fields
    if (!name || !chatId) {
      return NextResponse.json({
        error: 'Name and chatId are required'
      }, { status: 400 });
    }

    const chatIdNum = Number(chatId);
    if (isNaN(chatIdNum)) {
      return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
    }

    // Validate name length and truncate if necessary
    const maxNameLength = 100;
    const truncatedName = name.trim().length > maxNameLength
      ? name.trim().substring(0, maxNameLength).trim()
      : name.trim();

    if (!truncatedName) {
      return NextResponse.json({ error: 'Name cannot be empty' }, { status: 400 });
    }

    // Check if chat exists and user has access
    const chat = await prisma.chat.findUnique({
      where: { id: chatIdNum },
      include: {
        chatUsers: {
          where: { userId: auth.userId },
          select: { id: true }
        }
      }
    });

    if (!chat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Check if user is a member of the chat (unless they're admin/owner)
    if (!auth.isOwner && !auth.isAdmin && chat.chatUsers.length === 0) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Create a new assistant_chat_user record for each AI interaction
    // Each session gets a unique sessionId and can have multiple sessions per user/chat
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const assistantChatUser = await prisma.assistantChatUser.create({
      data: {
        name: truncatedName,
        userId: auth.userId,
        chatId: chatIdNum,
        sessionId: sessionId,
        isActive: Boolean(isActive),
        lastInteractionAt: new Date()
      },
      include: {
        chat: {
          select: {
            id: true,
            name: true,
            chatType: true,
            isBot: true,
            botDuration: true,
            createdAt: true,
          },
        },
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
      },
    });

    // Emit socket notification for real-time updates
    socketServerService.emitNotification('assistant', assistantChatUser.id, 'CREATE');

    return NextResponse.json({
      assistantChatUser,
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/v1/assistant-chat-user:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the AssistantChatUser record' },
      { status: 500 }
    );
  }
}

/**
 * DELETE API to delete an AssistantChatUser record
 *
 * Request body:
 * {
 *   "id": 123 (required)
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'AssistantChatUser ID is required' }, { status: 400 });
    }

    const assistantChatUserId = Number(id);
    if (isNaN(assistantChatUserId)) {
      return NextResponse.json({ error: 'Invalid AssistantChatUser ID' }, { status: 400 });
    }

    // Check if the record exists
    const existingRecord = await prisma.assistantChatUser.findUnique({
      where: { id: assistantChatUserId },
    });

    if (!existingRecord) {
      return NextResponse.json({ error: 'AssistantChatUser not found' }, { status: 404 });
    }

    // Check permissions - only owners, admins, or the user themselves can delete
    if (!auth.isOwner && !auth.isAdmin && existingRecord.userId !== auth.userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete the record
    await prisma.assistantChatUser.delete({
      where: { id: assistantChatUserId },
    });

    // Emit socket notification for real-time updates
    socketServerService.emitNotification('assistant', assistantChatUserId, 'DELETE');

    return NextResponse.json({
      message: 'AssistantChatUser deleted successfully',
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    });
  } catch (error) {
    console.error('Error in DELETE /api/v1/assistant-chat-user:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the AssistantChatUser record' },
      { status: 500 }
    );
  }
}
