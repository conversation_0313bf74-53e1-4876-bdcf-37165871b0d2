import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { addAdminToAllChats, removeAdminFromAllChats } from '@/services/organizationChatService';
import bcrypt from 'bcrypt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Helper function to verify user is owner of the organization
async function verifyOrganizationOwnership(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  if (organization.ownerUserId !== userId) {
    return {
      error: 'You do not have permission to manage admins in this organization. Owner access required.',
      status: 403,
    };
  }

  return { organization };
}

// Helper function to verify user has access to organization (owner or admin)
async function verifyOrganizationAccess(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  // Check if user is owner
  if (organization.ownerUserId === userId) {
    return { organization, isOwner: true };
  }

  // Check if user is admin of this organization
  const adminRecord = await prisma.organizationAdmin.findFirst({
    where: {
      organizationId: organizationId,
      userId: userId,
      isActive: true,
    },
  });

  if (adminRecord) {
    return { organization, isOwner: false };
  }

  return { error: 'You do not have access to this organization', status: 403 };
}

/**
 * GET API for organization admins
 *
 * Query parameters:
 * - organizationId: Optional. ID of the organization to get admins for
 * - id: Optional. Get specific admin by ID
 * - all: Optional. If 'true', get all admins from all organizations the user owns
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const adminId = url.searchParams.get('id');
    const getAllAdmins = url.searchParams.get('all') === 'true';

    // Handle getting all admins from all owned organizations
    if (getAllAdmins) {
      // Get user's owned organizations using permissions
      const { getUserOrganizationAdminPrivileges } = await import('@/lib/permissions');
      const privileges = await getUserOrganizationAdminPrivileges(auth.userId);

      if (privileges.ownedOrganizations.length === 0) {
        return NextResponse.json({ admins: [] });
      }

      const allAdmins = await prisma.organizationAdmin.findMany({
        where: {
          organizationId: {
            in: privileges.ownedOrganizations,
          },
          isActive: true,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          assignedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: [
          { organization: { name: 'asc' } },
          { assignedAt: 'desc' },
        ],
      });

      // Group admins by user to show multiple organizations per admin
      const adminMap = new Map();

      allAdmins.forEach(admin => {
        const userId = admin.user.id;
        if (adminMap.has(userId)) {
          // Add organization to existing admin
          adminMap.get(userId).organizations.push({
            id: admin.organization.id,
            name: admin.organization.name,
            assignedAt: admin.assignedAt,
          });
        } else {
          // Create new admin entry
          adminMap.set(userId, {
            id: admin.id,
            userId: admin.userId,
            organizationId: admin.organizationId, // Keep for compatibility
            assignedAt: admin.assignedAt,
            isActive: admin.isActive,
            user: admin.user,
            organization: admin.organization, // Keep for compatibility
            assignedByUser: admin.assignedByUser,
            organizations: [{
              id: admin.organization.id,
              name: admin.organization.name,
              assignedAt: admin.assignedAt,
            }],
          });
        }
      });

      const groupedAdmins = Array.from(adminMap.values());

      return NextResponse.json({ admins: groupedAdmins });
    }

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgIdNum = Number(organizationId);
    if (isNaN(orgIdNum)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    // Verify user has access to organization (owner or admin)
    const hasAccess = await verifyOrganizationAccess(orgIdNum, auth.userId);
    if ('error' in hasAccess) {
      return NextResponse.json({ error: hasAccess.error }, { status: hasAccess.status });
    }

    // Get specific admin
    if (adminId) {
      const adminIdNum = Number(adminId);
      if (isNaN(adminIdNum)) {
        return NextResponse.json({ error: 'Invalid admin ID' }, { status: 400 });
      }

      const admin = await prisma.organizationAdmin.findUnique({
        where: { id: adminIdNum },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          assignedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!admin) {
        return NextResponse.json({ error: 'Admin not found' }, { status: 404 });
      }

      // Verify admin belongs to the requested organization
      if (admin.organizationId !== orgIdNum) {
        return NextResponse.json({ error: 'Admin not found in this organization' }, { status: 404 });
      }

      return NextResponse.json({ admin });
    }

    // Get all admins for the organization
    const admins = await prisma.organizationAdmin.findMany({
      where: {
        organizationId: orgIdNum,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        assignedAt: 'desc',
      },
    });

    return NextResponse.json({ admins });
  } catch (error) {
    console.error('Error fetching organization admins:', error);
    return NextResponse.json({ error: 'Failed to fetch organization admins' }, { status: 500 });
  }
}

/**
 * POST API to add a new organization admin
 *
 * Body parameters:
 * - organizationId: Optional. Single organization ID (for backward compatibility)
 * - organizationIds: Optional. Array of organization IDs (for multiple organizations)
 * - userId: Optional. ID of existing user to make admin
 * - createUser: Optional. Object with user data to create new user
 *   - email: Required. User email
 *   - firstName: Required. User first name
 *   - lastName: Required. User last name
 *   - phone: Optional. User phone number
 *   - password: Required. User password
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { organizationId, organizationIds, userId, createUser } = body;

    // Determine which organizations to process
    let targetOrganizationIds: number[] = [];
    if (organizationIds && Array.isArray(organizationIds)) {
      targetOrganizationIds = organizationIds;
    } else if (organizationId) {
      targetOrganizationIds = [organizationId];
    }

    // Validate required fields
    if (targetOrganizationIds.length === 0) {
      return NextResponse.json({ error: 'At least one organization ID is required' }, { status: 400 });
    }

    if (!userId && !createUser) {
      return NextResponse.json({ error: 'Either User ID or createUser data is required' }, { status: 400 });
    }

    if (createUser) {
      const { email, firstName, lastName, password } = createUser;
      if (!email || !firstName || !lastName || !password) {
        return NextResponse.json({
          error: 'Email, firstName, lastName, and password are required for creating new user'
        }, { status: 400 });
      }
    }

    // Verify organization ownership for all target organizations
    for (const orgId of targetOrganizationIds) {
      const ownershipCheck = await verifyOrganizationOwnership(orgId, auth.userId);
      if ('error' in ownershipCheck) {
        return NextResponse.json({ error: `${ownershipCheck.error} (Organization ID: ${orgId})` }, { status: ownershipCheck.status });
      }
    }

    let targetUserId = userId;
    let user = null;

    // If creating new user
    if (createUser) {
      const { email, firstName, lastName, phone, password } = createUser;

      // Check if email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 });
      }

      // Hash password
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Create new user with admin role (userRoleId = 2)
      user = await prisma.user.create({
        data: {
          email,
          firstName,
          lastName,
          phone: phone || null,
          passwordHash,
          userRoleId: 2, // Fixed admin role ID
        },
        include: {
          userRole: true,
        },
      });

      targetUserId = user.id;
    } else {
      // Check if existing user exists and has admin role
      user = await prisma.user.findUnique({
        where: { id: userId },
        include: { userRole: true },
      });

      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
      }

      if (!user.userRole.isAdmin) {
        return NextResponse.json({ error: 'User must have admin role to be assigned as organization admin' }, { status: 400 });
      }
    }

    // Process organizations and chat synchronization in a transaction
    const transactionResult = await prisma.$transaction(async (tx) => {
      const results = [];
      const errors = [];
      const successfulOrganizations = [];

      // Process each organization
      for (const orgId of targetOrganizationIds) {
        try {
          // Check if user is already an admin of this organization
          const existingAdmin = await tx.organizationAdmin.findUnique({
            where: {
              userId_organizationId: {
                userId: targetUserId,
                organizationId: orgId,
              },
            },
          });

          if (existingAdmin) {
            if (existingAdmin.isActive) {
              errors.push(`User is already an admin of organization ${orgId}`);
              continue;
            } else {
              // Reactivate existing admin
              const reactivatedAdmin = await tx.organizationAdmin.update({
                where: { id: existingAdmin.id },
                data: {
                  isActive: true,
                  assignedAt: new Date(),
                  assignedBy: auth.userId,
                },
                include: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                      email: true,
                      imageUrl: true,
                      userRole: {
                        select: {
                          id: true,
                          name: true,
                        },
                      },
                    },
                  },
                  organization: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              });

              results.push({
                action: 'reactivated',
                admin: reactivatedAdmin,
              });
              successfulOrganizations.push(orgId);
            }
          } else {
            // Create new organization admin
            const newAdmin = await tx.organizationAdmin.create({
              data: {
                userId: targetUserId,
                organizationId: orgId,
                assignedBy: auth.userId,
              },
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                    userRole: {
                      select: {
                        id: true,
                        name: true,
                      },
                    },
                  },
                },
                organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            });

            results.push({
              action: 'created',
              admin: newAdmin,
            });
            successfulOrganizations.push(orgId);
          }
        } catch (error) {
          console.error(`Error processing organization ${orgId}:`, error);
          errors.push(`Failed to process organization ${orgId}`);
        }
      }

      // Add admin to organization and department chats for successful assignments
      if (successfulOrganizations.length > 0) {
        try {
          const chatResult = await addAdminToAllChats(targetUserId, successfulOrganizations, tx);
          if (chatResult.errors.length > 0) {
            console.warn('Chat synchronization warnings:', chatResult.errors);
            // Add chat warnings to errors but don't fail the transaction
            errors.push(...chatResult.errors.map(err => `Chat: ${err}`));
          }

          // Log success for both organization and department chats
          if (chatResult.organizationChats > 0 || chatResult.departmentChats > 0) {
            console.log(`Admin added to ${chatResult.organizationChats} organization chat(s) and ${chatResult.departmentChats} department chat(s)`);
          }
        } catch (chatError) {
          console.error('Error adding admin to chats:', chatError);
          errors.push(`Failed to add admin to chats: ${chatError instanceof Error ? chatError.message : 'Unknown error'}`);
        }
      }

      return { results, errors };
    });

    const { results, errors } = transactionResult;

    // Return results
    const successCount = results.length;
    const errorCount = errors.length;

    if (successCount === 0) {
      return NextResponse.json(
        {
          error: 'Failed to assign admin to any organization',
          details: errors,
        },
        { status: 400 }
      );
    }

    const message = createUser
      ? `User created and assigned as admin to ${successCount} organization(s)`
      : `User assigned as admin to ${successCount} organization(s)`;

    return NextResponse.json(
      {
        message,
        results,
        errors: errorCount > 0 ? errors : undefined,
        userCreated: !!createUser,
        successCount,
        errorCount,
      },
      { status: successCount === targetOrganizationIds.length ? 201 : 207 } // 207 = Multi-Status
    );
  } catch (error) {
    console.error('Error adding organization admin:', error);
    return NextResponse.json({ error: 'Failed to add organization admin' }, { status: 500 });
  }
}

/**
 * PATCH API to update organization admin status
 *
 * Body parameters:
 * - id: Required. ID of the organization admin to update
 * - isActive: Optional. Update active status
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, isActive } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'Admin ID is required' }, { status: 400 });
    }

    // Get the admin to update
    const admin = await prisma.organizationAdmin.findUnique({
      where: { id },
      include: {
        organization: true,
      },
    });

    if (!admin) {
      return NextResponse.json({ error: 'Organization admin not found' }, { status: 404 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(admin.organizationId, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Prepare update data
    const updateData: any = {};
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update admin
    const updatedAdmin = await prisma.organizationAdmin.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Organization admin updated successfully',
      admin: updatedAdmin,
    });
  } catch (error) {
    console.error('Error updating organization admin:', error);
    return NextResponse.json({ error: 'Failed to update organization admin' }, { status: 500 });
  }
}

/**
 * DELETE API to remove organization admin
 *
 * Query parameters:
 * - id: Required. ID of the organization admin to remove
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const adminId = url.searchParams.get('id');

    if (!adminId || isNaN(Number(adminId))) {
      return NextResponse.json({ error: 'Valid admin ID is required' }, { status: 400 });
    }

    const adminIdNum = Number(adminId);

    // Get the admin to delete
    const admin = await prisma.organizationAdmin.findUnique({
      where: { id: adminIdNum },
      include: {
        organization: true,
      },
    });

    if (!admin) {
      return NextResponse.json({ error: 'Organization admin not found' }, { status: 404 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(admin.organizationId, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Remove admin and update chat access in a transaction
    await prisma.$transaction(async (tx) => {
      // Soft delete by setting isActive to false
      await tx.organizationAdmin.update({
        where: { id: adminIdNum },
        data: { isActive: false },
      });

      // Remove admin from organization and department chats
      try {
        const chatResult = await removeAdminFromAllChats(admin.userId, [admin.organizationId], tx);
        if (chatResult.errors.length > 0) {
          console.warn('Chat removal warnings:', chatResult.errors);
        }

        // Log success for both organization and department chats
        if (chatResult.organizationChats > 0 || chatResult.departmentChats > 0) {
          console.log(`Admin removed from ${chatResult.organizationChats} organization chat(s) and ${chatResult.departmentChats} department chat(s)`);
        }
      } catch (chatError) {
        console.error('Error removing admin from chats:', chatError);
        // Don't fail the transaction for chat errors, just log them
      }
    });

    return NextResponse.json({
      message: 'Organization admin removed successfully',
    });
  } catch (error) {
    console.error('Error removing organization admin:', error);
    return NextResponse.json({ error: 'Failed to remove organization admin' }, { status: 500 });
  }
}
