import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Helper function to verify user is owner of the organization
async function verifyOrganizationOwnership(organizationId: number, userId: number) {
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
  });

  if (!organization) {
    return { error: 'Organization not found', status: 404 };
  }

  if (organization.ownerUserId !== userId) {
    return {
      error: 'You do not have permission to manage admins in this organization. Owner access required.',
      status: 403,
    };
  }

  return { organization };
}

/**
 * GET API for available users that can be assigned as organization admin
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization
 * - search: Optional. Search term for user name or email
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const search = url.searchParams.get('search');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgIdNum = Number(organizationId);
    if (isNaN(orgIdNum)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    // Verify organization ownership
    const ownershipCheck = await verifyOrganizationOwnership(orgIdNum, auth.userId);
    if ('error' in ownershipCheck) {
      return NextResponse.json({ error: ownershipCheck.error }, { status: ownershipCheck.status });
    }

    // Get users who already are admins of this organization
    const existingAdmins = await prisma.organizationAdmin.findMany({
      where: {
        organizationId: orgIdNum,
        isActive: true,
      },
      select: {
        userId: true,
      },
    });

    const existingAdminIds = existingAdmins.map(admin => admin.userId);

    // Build search conditions
    const searchConditions: any = {
      deletedAt: null, // Only active users
      userRole: {
        isAdmin: true, // Only users with admin role
      },
    };

    // Exclude users who are already admins of this organization
    if (existingAdminIds.length > 0) {
      searchConditions.id = {
        notIn: existingAdminIds,
      };
    }

    // Add search filter if provided
    if (search) {
      searchConditions.OR = [
        {
          firstName: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          lastName: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Get available users
    const availableUsers = await prisma.user.findMany({
      where: searchConditions,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        imageUrl: true,
        userRole: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [
        { firstName: 'asc' },
        { lastName: 'asc' },
      ],
      take: 50, // Limit results to prevent large responses
    });

    return NextResponse.json({ users: availableUsers });
  } catch (error) {
    console.error('Error fetching available users:', error);
    return NextResponse.json({ error: 'Failed to fetch available users' }, { status: 500 });
  }
}
