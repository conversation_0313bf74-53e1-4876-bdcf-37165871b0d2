import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';

/**
 * GET API for feedback users
 * 
 * This API supports getting feedback user details:
 * - Path: /api/v1/feedback-user?feedbackId=123
 * - Returns: All users assigned to the feedback
 * 
 * - Path: /api/v1/feedback-user?feedbackId=123&userId=456
 * - Returns: Specific user's feedback details
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('feedbackId');
    const userId = url.searchParams.get('userId');

    if (!feedbackId) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    if (isNaN(feedbackIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
    }

    // Check if user can view this feedback
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackIdNum },
      select: { createFromId: true },
    });

    if (!feedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    const canView = feedback.createFromId === auth.userId || 
                   auth.isOwner || auth.isAdmin ||
                   await prisma.feedbackUser.findFirst({
                     where: { feedbackId: feedbackIdNum, userId: auth.userId },
                   });

    if (!canView) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get specific user's feedback details
    if (userId) {
      const userIdNum = Number(userId);
      if (isNaN(userIdNum)) {
        return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
      }

      const feedbackUser = await prisma.feedbackUser.findUnique({
        where: {
          feedbackId_userId: {
            feedbackId: feedbackIdNum,
            userId: userIdNum,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          feedback: {
            select: {
              id: true,
              situation: true,
              behavior: true,
              impact: true,
              actionable: true,
              appreciation: true,
              growthToken: true,
              createFromId: true,
            },
          },
        },
      });

      if (!feedbackUser) {
        return NextResponse.json({ error: 'Feedback user not found' }, { status: 404 });
      }

      return NextResponse.json({ feedbackUser });
    }

    // Get all users assigned to the feedback
    const feedbackUsers = await prisma.feedbackUser.findMany({
      where: { feedbackId: feedbackIdNum },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    return NextResponse.json({ feedbackUsers });
  } catch (error) {
    console.error('Error fetching feedback users:', error);
    return NextResponse.json({ error: 'Failed to fetch feedback users' }, { status: 500 });
  }
}

/**
 * PUT API to update feedback user status (accept, discard, share, reflection)
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      feedbackId,
      userId,
      isAccept,
      isDiscard,
      reflection,
      isShare,
    } = body;

    // Validate required fields
    if (!feedbackId || !userId) {
      return NextResponse.json({ error: 'Feedback ID and User ID are required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    const userIdNum = Number(userId);

    if (isNaN(feedbackIdNum) || isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID or user ID' }, { status: 400 });
    }

    // Check if the authenticated user is the target user (users can only update their own feedback status)
    if (auth.userId !== userIdNum) {
      return NextResponse.json({ error: 'You can only update your own feedback status' }, { status: 403 });
    }

    // Check if feedback user exists
    const existingFeedbackUser = await prisma.feedbackUser.findUnique({
      where: {
        feedbackId_userId: {
          feedbackId: feedbackIdNum,
          userId: userIdNum,
        },
      },
    });

    if (!existingFeedbackUser) {
      return NextResponse.json({ error: 'Feedback user not found' }, { status: 404 });
    }

    // Update feedback user
    const updatedFeedbackUser = await prisma.feedbackUser.update({
      where: {
        feedbackId_userId: {
          feedbackId: feedbackIdNum,
          userId: userIdNum,
        },
      },
      data: {
        ...(typeof isAccept === 'boolean' && { isAccept }),
        ...(typeof isDiscard === 'boolean' && { isDiscard }),
        ...(reflection !== undefined && { reflection }),
        ...(typeof isShare === 'boolean' && { isShare }),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        feedback: {
          select: {
            id: true,
            createFromId: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Feedback user status updated successfully',
      feedbackUser: updatedFeedbackUser,
    });
  } catch (error) {
    console.error('Error updating feedback user:', error);
    return NextResponse.json({ error: 'Failed to update feedback user' }, { status: 500 });
  }
}

/**
 * POST API to add users to existing feedback
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { feedbackId, userIds } = body;

    // Validate required fields
    if (!feedbackId) {
      return NextResponse.json({ error: 'Feedback ID is required' }, { status: 400 });
    }

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'At least one user ID is required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    if (isNaN(feedbackIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID' }, { status: 400 });
    }

    // Get feedback and check permissions
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackIdNum },
      include: {
        feedbackType: true,
      },
    });

    if (!feedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check if user can modify this feedback (only creator can add users)
    if (feedback.createFromId !== auth.userId) {
      return NextResponse.json({ error: 'You can only add users to feedback you created' }, { status: 403 });
    }

    // Check for existing assignments
    const existingAssignments = await prisma.feedbackUser.findMany({
      where: {
        feedbackId: feedbackIdNum,
        userId: { in: userIds },
      },
      select: { userId: true },
    });

    const existingUserIds = existingAssignments.map(assignment => assignment.userId);
    const newUserIds = userIds.filter(userId => !existingUserIds.includes(userId));

    if (newUserIds.length === 0) {
      return NextResponse.json({ error: 'All specified users are already assigned to this feedback' }, { status: 400 });
    }

    // Create new feedback user assignments
    const newFeedbackUsers = await Promise.all(
      newUserIds.map(userId =>
        prisma.feedbackUser.create({
          data: {
            feedbackId: feedbackIdNum,
            userId,
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        })
      )
    );

    return NextResponse.json({
      message: 'Users added to feedback successfully',
      addedUsers: newFeedbackUsers,
      skippedUsers: existingUserIds,
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding users to feedback:', error);
    return NextResponse.json({ error: 'Failed to add users to feedback' }, { status: 500 });
  }
}

/**
 * DELETE API to remove users from feedback
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const feedbackId = url.searchParams.get('feedbackId');
    const userId = url.searchParams.get('userId');

    if (!feedbackId || !userId) {
      return NextResponse.json({ error: 'Feedback ID and User ID are required' }, { status: 400 });
    }

    const feedbackIdNum = Number(feedbackId);
    const userIdNum = Number(userId);

    if (isNaN(feedbackIdNum) || isNaN(userIdNum)) {
      return NextResponse.json({ error: 'Invalid feedback ID or user ID' }, { status: 400 });
    }

    // Get feedback and check permissions
    const feedback = await prisma.feedback.findUnique({
      where: { id: feedbackIdNum },
      select: { createFromId: true },
    });

    if (!feedback) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    // Check if user can modify this feedback (only creator can remove users)
    if (feedback.createFromId !== auth.userId) {
      return NextResponse.json({ error: 'You can only remove users from feedback you created' }, { status: 403 });
    }

    // Check if feedback user exists
    const feedbackUser = await prisma.feedbackUser.findUnique({
      where: {
        feedbackId_userId: {
          feedbackId: feedbackIdNum,
          userId: userIdNum,
        },
      },
    });

    if (!feedbackUser) {
      return NextResponse.json({ error: 'User is not assigned to this feedback' }, { status: 404 });
    }

    // Remove user from feedback
    await prisma.feedbackUser.delete({
      where: {
        feedbackId_userId: {
          feedbackId: feedbackIdNum,
          userId: userIdNum,
        },
      },
    });

    return NextResponse.json({
      message: 'User removed from feedback successfully',
    });
  } catch (error) {
    console.error('Error removing user from feedback:', error);
    return NextResponse.json({ error: 'Failed to remove user from feedback' }, { status: 500 });
  }
}
