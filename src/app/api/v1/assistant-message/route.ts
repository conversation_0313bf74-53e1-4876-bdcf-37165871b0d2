import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth-utils';
import socketServerService from '@/lib/socket-server';

/**
 * GET API for AssistantMessage
 *
 * This API retrieves AssistantMessage records filtered by AssistantC<PERSON><PERSON>ser ID with pagination support.
 *
 * Path: /api/v1/assistant-message?assistantChatUserId=123
 * Optional query parameters:
 *   - assistantChatUserId: Filter by AssistantChatUser ID (required)
 *   - page: Page number (default: 1)
 *   - limit: Messages per page (default: 50, max: 100)
 *   - messageTypeId: Filter by message type ID
 *   - order: Sort order 'asc' or 'desc' (default: 'desc')
 * Returns: Paginated list of AssistantMessage records
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const assistantChatUserId = searchParams.get('assistantChatUserId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const messageTypeId = searchParams.get('messageTypeId');
    const order = searchParams.get('order') || 'desc';

    // Validate required parameters
    if (!assistantChatUserId) {
      return NextResponse.json({ error: 'AssistantChatUser ID is required' }, { status: 400 });
    }

    const assistantChatUserIdNum = Number(assistantChatUserId);
    if (isNaN(assistantChatUserIdNum)) {
      return NextResponse.json({ error: 'Invalid AssistantChatUser ID' }, { status: 400 });
    }

    // Validate pagination parameters
    if (page < 1) {
      return NextResponse.json({ error: 'Page must be greater than 0' }, { status: 400 });
    }

    if (limit < 1) {
      return NextResponse.json({ error: 'Limit must be greater than 0' }, { status: 400 });
    }

    // Check if the AssistantChatUser exists and user has access
    const assistantChatUser = await prisma.assistantChatUser.findUnique({
      where: { id: assistantChatUserIdNum },
      select: { userId: true },
    });

    if (!assistantChatUser) {
      return NextResponse.json({ error: 'AssistantChatUser not found' }, { status: 404 });
    }

    // Check permissions - only owners, admins, or the user themselves can access
    if (!auth.isOwner && !auth.isAdmin && assistantChatUser.userId !== auth.userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Build where conditions
    const whereConditions: any = {
      assistantChatUserId: assistantChatUserIdNum,
    };

    if (messageTypeId) {
      const messageTypeIdNum = Number(messageTypeId);
      if (isNaN(messageTypeIdNum)) {
        return NextResponse.json({ error: 'Invalid message type ID' }, { status: 400 });
      }
      whereConditions.assistantMessageTypeId = messageTypeIdNum;
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get messages with pagination
    const assistantMessages = await prisma.assistantMessage.findMany({
      where: whereConditions,
      include: {
        assistantChatUser: {
          select: {
            id: true,
            name: true,
            userId: true,
            chatId: true,
            isActive: true,
          },
        },
        assistantMessageType: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: {
        createdAt: order === 'asc' ? 'asc' : 'desc',
      },
      skip: offset,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.assistantMessage.count({
      where: whereConditions,
    });

    return NextResponse.json({
      assistantMessages,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/v1/assistant-message:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching AssistantMessage records' },
      { status: 500 }
    );
  }
}

/**
 * POST API to create a new AssistantMessage record
 *
 * Request body:
 * {
 *   "assistantChatUserId": 123 (required),
 *   "assistantMessageTypeId": 1 (required),
 *   "content": "Message content" (required),
 *   "metadata": {} (optional),
 *   "completionTokens": 100 (optional),
 *   "promptTokens": 50 (optional),
 *   "totalTokens": 150 (optional)
 * }
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const {
      assistantChatUserId,
      assistantMessageTypeId,
      content,
      metadata,
      completionTokens,
      promptTokens,
      totalTokens,
    } = body;

    // Validate required fields
    if (!assistantChatUserId) {
      return NextResponse.json({ error: 'AssistantChatUser ID is required' }, { status: 400 });
    }

    if (!assistantMessageTypeId) {
      return NextResponse.json({ error: 'AssistantMessageType ID is required' }, { status: 400 });
    }

    if (!content || content.trim() === '') {
      return NextResponse.json({ error: 'Message content is required' }, { status: 400 });
    }

    const assistantChatUserIdNum = Number(assistantChatUserId);
    const assistantMessageTypeIdNum = Number(assistantMessageTypeId);

    if (isNaN(assistantChatUserIdNum)) {
      return NextResponse.json({ error: 'Invalid AssistantChatUser ID' }, { status: 400 });
    }

    if (isNaN(assistantMessageTypeIdNum)) {
      return NextResponse.json({ error: 'Invalid AssistantMessageType ID' }, { status: 400 });
    }

    // Validate token counts if provided
    if (completionTokens !== undefined && (isNaN(Number(completionTokens)) || Number(completionTokens) < 0)) {
      return NextResponse.json({ error: 'Invalid completion tokens count' }, { status: 400 });
    }

    if (promptTokens !== undefined && (isNaN(Number(promptTokens)) || Number(promptTokens) < 0)) {
      return NextResponse.json({ error: 'Invalid prompt tokens count' }, { status: 400 });
    }

    if (totalTokens !== undefined && (isNaN(Number(totalTokens)) || Number(totalTokens) < 0)) {
      return NextResponse.json({ error: 'Invalid total tokens count' }, { status: 400 });
    }

    // Check if AssistantChatUser exists and user has access
    const assistantChatUser = await prisma.assistantChatUser.findUnique({
      where: { id: assistantChatUserIdNum },
      select: { userId: true },
    });

    if (!assistantChatUser) {
      return NextResponse.json({ error: 'AssistantChatUser not found' }, { status: 404 });
    }

    // Check permissions - only owners, admins, or the user themselves can create
    if (!auth.isOwner && !auth.isAdmin && assistantChatUser.userId !== auth.userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if AssistantMessageType exists
    const assistantMessageType = await prisma.assistantMessageType.findUnique({
      where: { id: assistantMessageTypeIdNum },
    });

    if (!assistantMessageType) {
      return NextResponse.json({ error: 'AssistantMessageType not found' }, { status: 404 });
    }

    // Create the AssistantMessage
    const assistantMessage = await prisma.assistantMessage.create({
      data: {
        assistantChatUserId: assistantChatUserIdNum,
        assistantMessageTypeId: assistantMessageTypeIdNum,
        content: content.trim(),
        metadata: metadata || null,
        completionTokens: completionTokens ? Number(completionTokens) : null,
        promptTokens: promptTokens ? Number(promptTokens) : null,
        totalTokens: totalTokens ? Number(totalTokens) : null,
      },
      include: {
        assistantChatUser: {
          select: {
            id: true,
            name: true,
            userId: true,
            chatId: true,
            isActive: true,
          },
        },
        assistantMessageType: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    // Emit socket notification for real-time updates
    socketServerService.emitNotification('assistant', assistantMessage.id, 'CREATE');

    return NextResponse.json({
      assistantMessage,
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/v1/assistant-message:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the AssistantMessage record' },
      { status: 500 }
    );
  }
}

/**
 * DELETE API to delete an AssistantMessage record
 *
 * Request body:
 * {
 *   "id": 123 (required)
 * }
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json({ error: 'AssistantMessage ID is required' }, { status: 400 });
    }

    const assistantMessageId = Number(id);
    if (isNaN(assistantMessageId)) {
      return NextResponse.json({ error: 'Invalid AssistantMessage ID' }, { status: 400 });
    }

    // Check if the record exists and get associated AssistantChatUser
    const existingMessage = await prisma.assistantMessage.findUnique({
      where: { id: assistantMessageId },
      include: {
        assistantChatUser: {
          select: { userId: true },
        },
      },
    });

    if (!existingMessage) {
      return NextResponse.json({ error: 'AssistantMessage not found' }, { status: 404 });
    }

    // Check permissions - only owners, admins, or the user themselves can delete
    if (!auth.isOwner && !auth.isAdmin && existingMessage.assistantChatUser.userId !== auth.userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete the record
    await prisma.assistantMessage.delete({
      where: { id: assistantMessageId },
    });

    // Emit socket notification for real-time updates
    socketServerService.emitNotification('assistant', assistantMessageId, 'DELETE');

    return NextResponse.json({
      message: 'AssistantMessage deleted successfully',
      userRole: {
        isOwner: auth.isOwner,
        isAdmin: auth.isAdmin,
        isMember: auth.isMember,
      },
    });
  } catch (error) {
    console.error('Error in DELETE /api/v1/assistant-message:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the AssistantMessage record' },
      { status: 500 }
    );
  }
}
