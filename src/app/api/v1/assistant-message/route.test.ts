/**
 * Test file for the AssistantMessage API endpoint
 * 
 * This file contains tests for:
 * - Getting AssistantMessage records with pagination
 * - Creating new AssistantMessage records
 * - Deleting AssistantMessage records
 * - Error handling and validation
 */

import { NextRequest } from 'next/server';
import { GET, POST, DELETE } from './route';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import socketServerService from '@/lib/socket-server';

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    assistantChatUser: {
      findUnique: jest.fn(),
    },
    assistantMessageType: {
      findUnique: jest.fn(),
    },
    assistantMessage: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  },
}));

jest.mock('@/lib/jwt', () => ({
  verifyJwt: jest.fn(),
}));

jest.mock('@/lib/socket-server', () => ({
  emitNotification: jest.fn(),
}));

// Mock NextRequest
const createMockRequest = (searchParams: Record<string, string> = {}, body: any = null, headers: Record<string, string> = {}) => {
  const url = new URL('http://localhost:3000/api/v1/assistant-message');
  Object.entries(searchParams).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });

  return {
    headers: {
      get: jest.fn((key: string) => headers[key] || null),
    },
    url: url.toString(),
    json: jest.fn().mockResolvedValue(body),
  } as unknown as NextRequest;
};

describe('/api/v1/assistant-message', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (verifyJwt as jest.Mock).mockReturnValue({ userId: 1 });
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 1,
      userRole: { isOwner: false, isAdmin: false, isMember: true },
    });
  });

  describe('GET - Get AssistantMessage records', () => {
    it('should get paginated AssistantMessage records', async () => {
      const mockAssistantChatUser = { userId: 1 };
      const mockMessages = [
        {
          id: 1,
          content: 'Test message',
          assistantChatUser: { id: 1, name: 'Test User', userId: 1, chatId: 1, isActive: true },
          assistantMessageType: { id: 1, name: 'User', description: 'User message' },
        },
      ];

      (prisma.assistantChatUser.findUnique as jest.Mock).mockResolvedValue(mockAssistantChatUser);
      (prisma.assistantMessage.findMany as jest.Mock).mockResolvedValue(mockMessages);
      (prisma.assistantMessage.count as jest.Mock).mockResolvedValue(1);

      const request = createMockRequest(
        { assistantChatUserId: '1' },
        null,
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.assistantMessages).toHaveLength(1);
      expect(data.pagination.total).toBe(1);
      expect(data.userRole.isMember).toBe(true);
    });

    it('should return error when assistantChatUserId is missing', async () => {
      const request = createMockRequest(
        {},
        null,
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('AssistantChatUser ID is required');
    });

    it('should return error when user lacks access', async () => {
      const mockAssistantChatUser = { userId: 2 }; // Different user

      (prisma.assistantChatUser.findUnique as jest.Mock).mockResolvedValue(mockAssistantChatUser);

      const request = createMockRequest(
        { assistantChatUserId: '1' },
        null,
        { authorization: 'Bearer valid-token' }
      );

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('Access denied');
    });
  });

  describe('POST - Create AssistantMessage', () => {
    it('should create a new AssistantMessage', async () => {
      const mockAssistantChatUser = { userId: 1 };
      const mockAssistantMessageType = { id: 1, name: 'User' };
      const mockCreatedMessage = {
        id: 1,
        content: 'Test message',
        assistantChatUser: { id: 1, name: 'Test User', userId: 1, chatId: 1, isActive: true },
        assistantMessageType: { id: 1, name: 'User', description: 'User message' },
      };

      (prisma.assistantChatUser.findUnique as jest.Mock).mockResolvedValue(mockAssistantChatUser);
      (prisma.assistantMessageType.findUnique as jest.Mock).mockResolvedValue(mockAssistantMessageType);
      (prisma.assistantMessage.create as jest.Mock).mockResolvedValue(mockCreatedMessage);

      const request = createMockRequest(
        {},
        {
          assistantChatUserId: 1,
          assistantMessageTypeId: 1,
          content: 'Test message',
        },
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.assistantMessage.content).toBe('Test message');
      expect(socketServerService.emitNotification).toHaveBeenCalledWith('assistant', 1, 'CREATE');
    });

    it('should return error when required fields are missing', async () => {
      const request = createMockRequest(
        {},
        { content: 'Test message' }, // Missing assistantChatUserId and assistantMessageTypeId
        { authorization: 'Bearer valid-token' }
      );

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('AssistantChatUser ID is required');
    });
  });

  describe('DELETE - Delete AssistantMessage', () => {
    it('should delete an AssistantMessage', async () => {
      const mockExistingMessage = {
        id: 1,
        assistantChatUser: { userId: 1 },
      };

      (prisma.assistantMessage.findUnique as jest.Mock).mockResolvedValue(mockExistingMessage);
      (prisma.assistantMessage.delete as jest.Mock).mockResolvedValue({});

      const request = createMockRequest(
        {},
        { id: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('AssistantMessage deleted successfully');
      expect(socketServerService.emitNotification).toHaveBeenCalledWith('assistant', 1, 'DELETE');
    });

    it('should return error when message not found', async () => {
      (prisma.assistantMessage.findUnique as jest.Mock).mockResolvedValue(null);

      const request = createMockRequest(
        {},
        { id: 1 },
        { authorization: 'Bearer valid-token' }
      );

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('AssistantMessage not found');
    });
  });
});
