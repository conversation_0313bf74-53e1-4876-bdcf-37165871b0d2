import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { ChatType } from '@/generated/prisma';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user info
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  // Get user with role information
  const user = await prisma.user.findUnique({
    where: { id: payload.userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  return {
    userId: user.id,
    isOwner: user.userRole.isOwner,
    isAdmin: user.userRole.isAdmin,
    isMember: user.userRole.isMember,
  };
}

// Helper function to check if user has admin or owner role
async function verifyAdminRole(userId: number) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: { userRole: true },
  });

  if (!user) {
    return { error: 'User not found', status: 404 };
  }

  if (!user.userRole.isAdmin && !user.userRole.isOwner) {
    return {
      error: 'You do not have permission to perform this action. Admin role required.',
      status: 403,
    };
  }

  return { isAdmin: true };
}

/**
 * GET API for chats
 *
 * This API supports two modes:
 *
 * 1. Get a single chat by ID:
 *    - Path: /api/v1/chat?id=123
 *    - Returns: Detailed chat information including participants and recent messages
 *
 * 2. Get a list of chats with filtering:
 *    - Path: /api/v1/chat
 *    - Optional query parameters:
 *      - chatType: Filter by chat type (private, task, department, organization)
 *      - userId: Filter chats where user is participant
 *      - organizationId: Filter by organization
 *      - departmentId: Filter by department
 *      - taskId: Filter by task
 *    - Returns: List of chats matching the filters
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const chatId = url.searchParams.get('id');
    const chatType = url.searchParams.get('chatType');
    const userId = url.searchParams.get('userId');
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');
    const taskId = url.searchParams.get('taskId');

    // Get a specific chat by ID
    if (chatId) {
      const chatIdNum = Number(chatId);
      if (isNaN(chatIdNum)) {
        return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
      }

      const chat = await prisma.chat.findUnique({
        where: { id: chatIdNum },
        include: {
          organization: {
            select: { id: true, name: true, description: true },
          },
          department: {
            select: { id: true, name: true, description: true },
          },
          task: {
            select: { id: true, taskTitle: true, taskDescription: true },
          },
          chatUsers: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                },
              },
            },
            orderBy: { joinedAt: 'asc' },
          },
          messages: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  imageUrl: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 50, // Get last 50 messages
          },
        },
      });

      if (!chat) {
        return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
      }

      // Check if user is participant in the chat
      // All users (including admin/owner) must be participants to access a chat
      const isParticipant = chat.chatUsers.some(chatUser => chatUser.userId === auth.userId);
      if (!isParticipant) {
        return NextResponse.json({ error: 'You do not have access to this chat' }, { status: 403 });
      }

      return NextResponse.json({ chat });
    }

    // Build the where clause for filtering
    const where: any = {};

    if (chatType) {
      where.chatType = chatType.toUpperCase() as ChatType;
    }

    if (organizationId) {
      where.organizationId = Number(organizationId);
    }

    if (departmentId) {
      where.departmentId = Number(departmentId);
    }

    if (taskId) {
      where.taskId = Number(taskId);
    }

    // Always filter by user membership in chat_users table
    // All users (including admin/owner) should only see chats they're participants in
    const targetUserId = userId ? Number(userId) : auth.userId;
    where.chatUsers = {
      some: {
        userId: targetUserId,
      },
    };

    const chats = await prisma.chat.findMany({
      where,
      include: {
        organization: {
          select: { id: true, name: true },
        },
        department: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, taskTitle: true },
        },
        chatUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                imageUrl: true,
              },
            },
          },
          take: 5, // Show first 5 participants
        },
        messages: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                imageUrl: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: 1, // Get only the last message
        },
        _count: {
          select: {
            messages: true,
            chatUsers: true,
          },
        },
      },
    });

    // Sort chats by their latest message createdAt (chats with no messages go to the end)
    const sortedChats = chats.sort((a, b) => {
      const aLatestMessage = a.messages[0]?.createdAt;
      const bLatestMessage = b.messages[0]?.createdAt;

      // If both have messages, sort by latest message time (desc)
      if (aLatestMessage && bLatestMessage) {
        return bLatestMessage.getTime() - aLatestMessage.getTime();
      }

      // If only one has messages, prioritize the one with messages
      if (aLatestMessage && !bLatestMessage) return -1;
      if (!aLatestMessage && bLatestMessage) return 1;

      // If neither has messages, maintain original order (or sort by chat creation time)
      return b.createdAt.getTime() - a.createdAt.getTime();
    });

    return NextResponse.json({ chats: sortedChats });
  } catch (error) {
    console.error('Error fetching chats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST API to create a new chat
 *
 * Request body:
 * {
 *   "name": "Chat Name (optional for group chats)",
 *   "chatType": "private" | "task" | "department" | "organization",
 *   "organizationId": 123 (required for organization/department chats),
 *   "departmentId": 123 (required for department chats),
 *   "taskId": 123 (required for task chats),
 *   "participantIds": [1, 2, 3] (user IDs to add as participants - only used for private chats),
 *   "isBot": false (optional, default: false - indicates if this is a bot/AI assistant chat),
 *   "botDuration": 30 (optional, duration in minutes for bot session - only valid when isBot is true)
 * }
 *
 * Automatic participant assignment:
 * - PRIVATE: Creator + selected participants from participantIds
 * - TASK: Creator + task creator + assigned user from the task
 * - DEPARTMENT: All users in the specified department
 * - ORGANIZATION: All users in all departments of the specified organization
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { name, chatType, organizationId, departmentId, taskId, participantIds = [], isBot = false, botDuration } = body;

    // Validate required fields
    if (!chatType) {
      return NextResponse.json({ error: 'Chat type is required' }, { status: 400 });
    }

    // Validate bot-related fields
    if (isBot !== undefined && typeof isBot !== 'boolean') {
      return NextResponse.json({ error: 'isBot must be a boolean value' }, { status: 400 });
    }

    if (botDuration !== undefined) {
      const botDurationNum = Number(botDuration);

      if (!isBot) {
        // If bot mode is disabled, allow botDuration to be 0
        if (botDurationNum !== 0) {
          return NextResponse.json({ error: 'botDuration must be 0 when isBot is false' }, { status: 400 });
        }
      } else {
        // If bot mode is enabled, validate duration is between 1-480 minutes
        if (isNaN(botDurationNum) || botDurationNum < 1 || botDurationNum > 480) {
          return NextResponse.json({ error: 'botDuration must be between 1 and 480 minutes when isBot is true' }, { status: 400 });
        }
      }
    }

    const validChatTypes = ['PRIVATE', 'TASK', 'DEPARTMENT', 'ORGANIZATION'];
    if (!validChatTypes.includes(chatType.toUpperCase())) {
      return NextResponse.json(
        { error: 'Invalid chat type. Must be one of: private, task, department, organization' },
        { status: 400 }
      );
    }

    // Validate type-specific requirements
    if (chatType.toUpperCase() === 'ORGANIZATION' && !organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required for organization chats' },
        { status: 400 }
      );
    }

    if (chatType.toUpperCase() === 'DEPARTMENT' && (!organizationId || !departmentId)) {
      return NextResponse.json(
        { error: 'Organization ID and Department ID are required for department chats' },
        { status: 400 }
      );
    }

    if (chatType.toUpperCase() === 'TASK' && !taskId) {
      return NextResponse.json({ error: 'Task ID is required for task chats' }, { status: 400 });
    }

    // Check for duplicate chats based on type
    let duplicateCheck: any = {
      chatType: chatType.toUpperCase() as ChatType,
    };

    switch (chatType.toUpperCase()) {
      case 'TASK':
        if (taskId) {
          duplicateCheck.taskId = Number(taskId);
        }
        break;
      case 'DEPARTMENT':
        if (departmentId) {
          duplicateCheck.departmentId = Number(departmentId);
        }
        break;
      case 'ORGANIZATION':
        if (organizationId) {
          duplicateCheck.organizationId = Number(organizationId);
        }
        break;
      case 'PRIVATE':
        // For private chats, check if a chat with the same participants already exists
        if (participantIds.length > 0) {
          const allParticipants = [...new Set([auth.userId, ...participantIds.map(Number)])];
          const existingPrivateChat = await prisma.chat.findFirst({
            where: {
              chatType: 'PRIVATE',
              chatUsers: {
                every: {
                  userId: {
                    in: allParticipants,
                  },
                },
              },
            },
            include: {
              _count: {
                select: {
                  chatUsers: true,
                },
              },
            },
          });

          if (
            existingPrivateChat &&
            existingPrivateChat._count.chatUsers === allParticipants.length
          ) {
            return NextResponse.json(
              { error: 'A private chat with these participants already exists' },
              { status: 409 }
            );
          }
        }
        break;
    }

    // Check for duplicate non-private chats
    if (chatType.toUpperCase() !== 'PRIVATE') {
      const existingChat = await prisma.chat.findFirst({
        where: duplicateCheck,
      });

      if (existingChat) {
        return NextResponse.json(
          {
            error: `A ${chatType.toLowerCase()} chat already exists for this ${chatType.toLowerCase() === 'task' ? 'task' : chatType.toLowerCase() === 'department' ? 'department' : 'organization'}`,
          },
          { status: 409 }
        );
      }
    }

    // Verify related entities exist
    if (organizationId) {
      const organization = await prisma.organization.findUnique({
        where: { id: Number(organizationId) },
      });
      if (!organization) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }
    }

    if (departmentId) {
      const department = await prisma.department.findUnique({
        where: { id: Number(departmentId) },
      });
      if (!department) {
        return NextResponse.json({ error: 'Department not found' }, { status: 404 });
      }
    }

    if (taskId) {
      const task = await prisma.task.findUnique({
        where: { id: Number(taskId) },
      });
      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 });
      }
    }

    // Verify participant users exist
    if (participantIds.length > 0) {
      const participants = await prisma.user.findMany({
        where: { id: { in: participantIds.map(Number) } },
      });
      if (participants.length !== participantIds.length) {
        return NextResponse.json(
          { error: 'One or more participant users not found' },
          { status: 404 }
        );
      }
    }

    // Create the chat
    const chat = await prisma.chat.create({
      data: {
        name,
        chatType: chatType.toUpperCase() as ChatType,
        organizationId: organizationId ? Number(organizationId) : null,
        departmentId: departmentId ? Number(departmentId) : null,
        taskId: taskId ? Number(taskId) : null,
        isBot: isBot,
        botDuration: isBot ? (botDuration ? Number(botDuration) : null) : 0,
      },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        department: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, taskTitle: true },
        },
      },
    });

    // Determine participants based on chat type
    let allParticipantIds: number[] = [];

    switch (chatType.toUpperCase()) {
      case 'PRIVATE':
        // For private chats: creator + selected participants
        allParticipantIds = [...new Set([auth.userId, ...participantIds.map(Number)])];
        break;

      case 'TASK':
        // For task chats: creator + all assigned users from the task
        const taskWithAssignees = await prisma.task.findUnique({
          where: { id: Number(taskId) },
          select: {
            id: true,
            createdByUserId: true,
            taskAssignments: {
              where: { isActive: true },
              select: { userId: true },
            },
          },
        });

        if (taskWithAssignees) {
          const assignedUserIds = taskWithAssignees.taskAssignments.map(assignment => assignment.userId);
          console.log('Task assignments found:', taskWithAssignees.taskAssignments);
          console.log('Assigned user IDs:', assignedUserIds);
          allParticipantIds = [
            ...new Set([
              auth.userId,
              taskWithAssignees.createdByUserId,
              ...assignedUserIds,
            ].filter(id => id != null)), // Filter out null/undefined values
          ];
          console.log('Final participant IDs for task chat:', allParticipantIds);
        } else {
          console.log('No task found or no assignments');
          allParticipantIds = [auth.userId];
        }
        break;

      case 'DEPARTMENT':
        // For department chats: all users in the department
        const departmentMembers = await prisma.departmentMember.findMany({
          where: { departmentId: Number(departmentId) },
          select: { userId: true },
        });

        const departmentUserIds = departmentMembers.map(member => member.userId);
        allParticipantIds = [...new Set([auth.userId, ...departmentUserIds])];
        break;

      case 'ORGANIZATION':
        // For organization chats: all users in all departments of the organization
        const organizationDepartments = await prisma.department.findMany({
          where: { organizationId: Number(organizationId) },
          include: {
            members: {
              select: { userId: true },
            },
          },
        });

        const organizationUserIds: number[] = [];
        organizationDepartments.forEach(dept => {
          dept.members.forEach(member => {
            organizationUserIds.push(member.userId);
          });
        });

        allParticipantIds = [...new Set([auth.userId, ...organizationUserIds])];
        break;

      default:
        // Fallback to provided participants
        allParticipantIds = [...new Set([auth.userId, ...participantIds.map(Number)])];
    }

    // Add participants to the chat
    console.log('Creating chat users for participants:', allParticipantIds);
    await prisma.chatUser.createMany({
      data: allParticipantIds.map(userId => ({
        chatId: chat.id,
        userId,
        isAdmin: userId === auth.userId, // Creator is admin
      })),
    });

    // Fetch the complete chat with participants
    const createdChat = await prisma.chat.findUnique({
      where: { id: chat.id },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        department: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, taskTitle: true },
        },
        chatUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ chat: createdChat }, { status: 201 });
  } catch (error) {
    console.error('Error creating chat:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH API to update a chat
 *
 * Request body:
 * {
 *   "id": 123 (required),
 *   "name": "Updated Chat Name",
 *   "isActive": true/false,
 *   "isBot": true/false (optional - indicates if this is a bot/AI assistant chat),
 *   "botDuration": 30 (optional - duration in minutes for bot session, only valid when isBot is true)
 * }
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, name, isActive, isBot, botDuration } = body;

    if (!id) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    const chatId = Number(id);
    if (isNaN(chatId)) {
      return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
    }

    // Check if chat exists and user has permission
    const existingChat = await prisma.chat.findUnique({
      where: { id: chatId },
      include: {
        chatUsers: {
          where: { userId: auth.userId },
        },
      },
    });

    if (!existingChat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Check if user is admin of the chat or has admin role
    const userChatRole = existingChat.chatUsers[0];
    if (!userChatRole?.isAdmin && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to update this chat' },
        { status: 403 }
      );
    }

    // Validate bot-related fields
    if (isBot !== undefined && typeof isBot !== 'boolean') {
      return NextResponse.json({ error: 'isBot must be a boolean value' }, { status: 400 });
    }

    // Determine the final bot state
    const willBeBot = isBot !== undefined ? isBot : existingChat.isBot;

    if (botDuration !== undefined) {
      const botDurationNum = Number(botDuration);

      // If bot mode is being disabled, allow botDuration to be 0
      if (!willBeBot) {
        if (botDurationNum !== 0) {
          return NextResponse.json({ error: 'botDuration must be 0 when isBot is false' }, { status: 400 });
        }
      } else {
        // If bot mode is enabled, validate duration is between 1-480 minutes
        if (isNaN(botDurationNum) || botDurationNum < 1 || botDurationNum > 480) {
          return NextResponse.json({ error: 'botDuration must be between 1 and 480 minutes when isBot is true' }, { status: 400 });
        }
      }
    }

    // Build update data
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (isBot !== undefined) {
      updateData.isBot = isBot;
      // If disabling bot mode, automatically set botDuration to 0
      if (!isBot) {
        updateData.botDuration = 0;
      }
    }
    if (botDuration !== undefined) updateData.botDuration = Number(botDuration);

    const updatedChat = await prisma.chat.update({
      where: { id: chatId },
      data: updateData,
      include: {
        organization: {
          select: { id: true, name: true },
        },
        department: {
          select: { id: true, name: true },
        },
        task: {
          select: { id: true, taskTitle: true },
        },
        chatUsers: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ chat: updatedChat });
  } catch (error) {
    console.error('Error updating chat:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE API to delete a chat
 *
 * Query parameter:
 * - id: Chat ID to delete
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const chatId = url.searchParams.get('id');

    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
    }

    const chatIdNum = Number(chatId);
    if (isNaN(chatIdNum)) {
      return NextResponse.json({ error: 'Invalid chat ID' }, { status: 400 });
    }

    // Check if chat exists and user has permission
    const existingChat = await prisma.chat.findUnique({
      where: { id: chatIdNum },
      include: {
        chatUsers: {
          where: { userId: auth.userId },
        },
      },
    });

    if (!existingChat) {
      return NextResponse.json({ error: 'Chat not found' }, { status: 404 });
    }

    // Check if user is admin of the chat or has admin role
    const userChatRole = existingChat.chatUsers[0];
    if (!userChatRole?.isAdmin && !auth.isAdmin && !auth.isOwner) {
      return NextResponse.json(
        { error: 'You do not have permission to delete this chat' },
        { status: 403 }
      );
    }

    // Delete the chat (cascade will handle related records)
    await prisma.chat.delete({
      where: { id: chatIdNum },
    });

    return NextResponse.json({ message: 'Chat deleted successfully' });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
