import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { hasOrganizationAdminPrivileges } from '@/lib/permissions';

/**
 * GET API for fetching departments based on organization and user role for chat modal
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization
 * 
 * Role-based access:
 * - Owner: Can see all departments in organizations they own
 * - Admin: Can see all departments in organizations where they have admin privileges
 * - Member: Can see all departments in organizations where they are a member
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgId = parseInt(organizationId);
    if (isNaN(orgId)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: orgId },
      select: { id: true, ownerUserId: true },
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    let hasAccess = false;

    if (auth.isOwner) {
      // Owners can access departments in organizations they own
      hasAccess = organization.ownerUserId === auth.userId;
    } else if (auth.isAdmin) {
      // Admins can access departments in organizations where they have admin privileges
      hasAccess = await hasOrganizationAdminPrivileges(auth.userId, orgId);
    } else if (auth.isMember) {
      // Members can access departments in organizations where they are a member
      const membershipCount = await prisma.departmentMember.count({
        where: {
          userId: auth.userId,
          department: {
            organizationId: orgId,
          },
        },
      });
      hasAccess = membershipCount > 0;
    }

    if (!hasAccess) {
      return NextResponse.json({ error: 'You do not have access to this organization' }, { status: 403 });
    }

    // Fetch departments based on user role
    let departments: any[] = [];

    if (auth.isOwner || auth.isAdmin) {
      // Owners and admins can see all departments in the organization
      departments = await prisma.department.findMany({
        where: {
          organizationId: orgId,
        },
        select: {
          id: true,
          name: true,
          description: true,
          _count: {
            select: {
              members: true,
            },
          },
        },
        orderBy: { name: 'asc' },
      });
    } else if (auth.isMember) {
      // Members can see all departments within organizations they belong to
      departments = await prisma.department.findMany({
        where: {
          organizationId: orgId,
        },
        select: {
          id: true,
          name: true,
          description: true,
          _count: {
            select: {
              members: true,
            },
          },
        },
        orderBy: { name: 'asc' },
      });
    }

    return NextResponse.json({ departments });
  } catch (error) {
    console.error('Error fetching departments for chat modal:', error);
    return NextResponse.json({ error: 'Failed to fetch departments' }, { status: 500 });
  }
}
