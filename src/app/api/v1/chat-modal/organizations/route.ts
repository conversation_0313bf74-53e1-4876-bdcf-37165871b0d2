import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { getUserOrganizationAdminPrivileges } from '@/lib/permissions';

/**
 * GET API for fetching organizations based on user role for chat modal
 * 
 * Role-based access:
 * - Owner: Can see all organizations they own
 * - Admin: Can see all organizations where they have admin privileges
 * - Member: Can see all organizations where they are a member
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    let organizations: any[] = [];

    if (auth.isOwner) {
      // Owners can see all organizations they own
      organizations = await prisma.organization.findMany({
        where: {
          ownerUserId: auth.userId,
        },
        select: {
          id: true,
          name: true,
          description: true,
          imageUrl: true,
        },
        orderBy: { name: 'asc' },
      });
    } else if (auth.isAdmin) {
      // <PERSON><PERSON> can see all organizations where they have admin privileges
      const privileges = await getUserOrganizationAdminPrivileges(auth.userId);
      
      if (privileges.allAdminOrganizations.length > 0) {
        organizations = await prisma.organization.findMany({
          where: {
            id: { in: privileges.allAdminOrganizations },
          },
          select: {
            id: true,
            name: true,
            description: true,
            imageUrl: true,
          },
          orderBy: { name: 'asc' },
        });
      }
    } else if (auth.isMember) {
      // Members can see organizations where they are a member (through department membership)
      const memberDepartments = await prisma.departmentMember.findMany({
        where: {
          userId: auth.userId,
        },
        include: {
          department: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  imageUrl: true,
                },
              },
            },
          },
        },
      });

      // Extract unique organizations
      const orgMap = new Map();
      memberDepartments.forEach(member => {
        const org = member.department.organization;
        if (!orgMap.has(org.id)) {
          orgMap.set(org.id, org);
        }
      });

      organizations = Array.from(orgMap.values()).sort((a, b) => a.name.localeCompare(b.name));
    }

    return NextResponse.json({ organizations });
  } catch (error) {
    console.error('Error fetching organizations for chat modal:', error);
    return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
  }
}
