import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';
import { taskNotificationService } from '@/services/taskNotificationService';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

/**
 * GET API for task progress
 *
 * This API supports two modes:
 *
 * 1. Get a single task progress by ID:
 *    - Path: /api/v1/task-progress?id=123
 *    - Returns: Detailed task progress information including related data
 *
 * 2. Get a list of task progress entries with filtering:
 *    - Path: /api/v1/task-progress
 *    - Optional query parameters:
 *      - taskId: Filter by task ID
 *      - updatedByUserId: Filter by user who updated
 *      - progressTypeId: Filter by progress type
 *    - Returns: List of task progress entries matching the filters
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const progressId = url.searchParams.get('id');
    const taskId = url.searchParams.get('taskId');
    const updatedByUserId = url.searchParams.get('updatedByUserId');
    const progressTypeId = url.searchParams.get('progressTypeId');

    // Get a specific task progress by ID
    if (progressId) {
      const progressIdNum = Number(progressId);
      if (isNaN(progressIdNum)) {
        return NextResponse.json({ error: 'Invalid task progress ID' }, { status: 400 });
      }

      const taskProgress = await prisma.taskProgress.findUnique({
        where: { id: progressIdNum },
        include: {
          task: {
            include: {
              taskAssignments: {
                where: { isActive: true },
                select: {
                  user: {
                    select: {
                      id: true,
                      firstName: true,
                      lastName: true,
                      email: true,
                      imageUrl: true,
                    },
                  },
                },
              },
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: true,
            },
          },
          progressType: true,
        },
      });

      if (!taskProgress) {
        return NextResponse.json({ error: 'Task progress not found' }, { status: 404 });
      }

      return NextResponse.json({ taskProgress });
    }

    // Build the where clause for filtering
    const where: any = {};

    // Apply filters
    if (taskId) {
      where.taskId = Number(taskId);
    }

    if (updatedByUserId) {
      where.updatedByUserId = Number(updatedByUserId);
    }

    if (progressTypeId) {
      where.progressTypeId = Number(progressTypeId);
    }

    // Get all task progress entries with filters
    const taskProgresses = await prisma.taskProgress.findMany({
      where,
      include: {
        task: {
          include: {
            taskAssignments: {
              where: { isActive: true },
              select: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                  },
                },
              },
            },
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: true,
          },
        },
        progressType: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({ taskProgresses });
  } catch (error) {
    console.error('Error in GET /api/v1/task-progress:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching task progress data' },
      { status: 500 }
    );
  }
}

/**
 * POST API to create a new task progress entry
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { taskId, progressTypeId, progressDescription } = body;

    // Validate required fields
    if (!taskId) {
      return NextResponse.json({ error: 'Task ID is required' }, { status: 400 });
    }

    if (!progressTypeId) {
      return NextResponse.json({ error: 'Progress type ID is required' }, { status: 400 });
    }

    if (!progressDescription) {
      return NextResponse.json({ error: 'Progress description is required' }, { status: 400 });
    }

    // Verify task exists
    const task = await prisma.task.findUnique({
      where: { id: Number(taskId) },
    });

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Verify progress type exists
    const progressType = await prisma.taskProgressType.findUnique({
      where: { id: Number(progressTypeId) },
    });

    if (!progressType) {
      return NextResponse.json({ error: 'Progress type not found' }, { status: 404 });
    }

    // Create the task progress entry
    const taskProgress = await prisma.taskProgress.create({
      data: {
        taskId: Number(taskId),
        updatedByUserId: auth.userId,
        progressTypeId: Number(progressTypeId),
        progressDescription,
      },
      include: {
        task: {
          include: {
            taskAssignments: {
              where: { isActive: true },
              select: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                  },
                },
              },
            },
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: true,
          },
        },
        progressType: true,
      },
    });

    // Create notification for task progress comment or request
    if (Number(progressTypeId) === 1 || Number(progressTypeId) === 2) {
      const task = taskProgress.task;
      const assignedUserIds = task.taskAssignments?.map(assignment => assignment.user.id) || [];
      
      // Determine recipients based on who created the progress entry
      let recipientUserIds: number[] = [];

      // If the current user is the task creator, notify all assigned users
      if (auth.userId === task.createdByUserId) {
        recipientUserIds = assignedUserIds;
      }
      // If the current user is one of the assigned users, notify the task creator and other assigned users
      else if (assignedUserIds.includes(auth.userId)) {
        recipientUserIds = [task.createdByUserId, ...assignedUserIds.filter(id => id !== auth.userId)];
      }
      // Otherwise, notify task creator and all assigned users
      else {
        recipientUserIds = [task.createdByUserId, ...assignedUserIds];
      }

      // Remove duplicates and current user from recipients
      recipientUserIds = [...new Set(recipientUserIds)].filter(id => id !== auth.userId);

      // Send notifications to all recipients
      for (const recipientUserId of recipientUserIds) {
        if (Number(progressTypeId) === 1) {
          // Comment
          await taskNotificationService.notifyTaskProgressComment(
            task.id,
            task.taskTitle,
            progressDescription,
            recipientUserId,
            auth.userId
          );
        } else if (Number(progressTypeId) === 2) {
          // Request
          await taskNotificationService.notifyTaskProgressRequest(
            task.id,
            task.taskTitle,
            progressDescription,
            recipientUserId,
            auth.userId
          );
        }
      }
    }

    return NextResponse.json({ taskProgress }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/v1/task-progress:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating the task progress entry' },
      { status: 500 }
    );
  }
}

/**
 * PATCH API to update an existing task progress entry
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const body = await request.json();
    const { id, progressTypeId, progressDescription } = body;

    if (!id) {
      return NextResponse.json({ error: 'Task progress ID is required' }, { status: 400 });
    }

    // Find the task progress entry
    const existingTaskProgress = await prisma.taskProgress.findUnique({
      where: { id: Number(id) },
      include: {
        task: true,
      },
    });

    if (!existingTaskProgress) {
      return NextResponse.json({ error: 'Task progress entry not found' }, { status: 404 });
    }

    // Check if user has permission to update the task progress
    // Only the user who created the progress entry can update it
    if (existingTaskProgress.updatedByUserId !== auth.userId) {
      return NextResponse.json(
        {
          error: 'You do not have permission to update this task progress entry',
        },
        { status: 403 }
      );
    }

    // If changing progressTypeId, verify it exists
    if (progressTypeId && progressTypeId !== existingTaskProgress.progressTypeId) {
      const progressType = await prisma.taskProgressType.findUnique({
        where: { id: Number(progressTypeId) },
      });

      if (!progressType) {
        return NextResponse.json({ error: 'Progress type not found' }, { status: 404 });
      }
    }

    // Update the task progress entry
    const updatedTaskProgress = await prisma.taskProgress.update({
      where: { id: Number(id) },
      data: {
        progressTypeId: progressTypeId !== undefined ? Number(progressTypeId) : undefined,
        progressDescription: progressDescription !== undefined ? progressDescription : undefined,
      },
      include: {
        task: {
          include: {
            taskAssignments: {
              where: { isActive: true },
              select: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    imageUrl: true,
                  },
                },
              },
            },
          },
        },
        updatedByUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: true,
          },
        },
        progressType: true,
      },
    });

    // Create notification for task progress comment or request if the type is 1 or 2
    // or if it was changed to type 1 or 2
    const finalProgressTypeId =
      progressTypeId !== undefined ? Number(progressTypeId) : existingTaskProgress.progressTypeId;

    if (finalProgressTypeId === 1 || finalProgressTypeId === 2) {
      const task = updatedTaskProgress.task;
      const assignedUserIds = task.taskAssignments?.map(assignment => assignment.user.id) || [];
      const updatedDescription =
        progressDescription !== undefined
          ? progressDescription
          : existingTaskProgress.progressDescription;

      // Determine recipients based on who updated the progress entry
      let recipientUserIds: number[] = [];

      // If the current user is the task creator, notify all assigned users
      if (auth.userId === task.createdByUserId) {
        recipientUserIds = assignedUserIds;
      }
      // If the current user is one of the assigned users, notify the task creator and other assigned users
      else if (assignedUserIds.includes(auth.userId)) {
        recipientUserIds = [task.createdByUserId, ...assignedUserIds.filter(id => id !== auth.userId)];
      }
      // Otherwise, notify task creator and all assigned users
      else {
        recipientUserIds = [task.createdByUserId, ...assignedUserIds];
      }

      // Remove duplicates and current user from recipients
      recipientUserIds = [...new Set(recipientUserIds)].filter(id => id !== auth.userId);

      // Send notifications to all recipients
      for (const recipientUserId of recipientUserIds) {
        if (finalProgressTypeId === 1) {
          // Comment
          await taskNotificationService.notifyTaskProgressComment(
            task.id,
            task.taskTitle,
            updatedDescription,
            recipientUserId,
            auth.userId
          );
        } else if (finalProgressTypeId === 2) {
          // Request
          await taskNotificationService.notifyTaskProgressRequest(
            task.id,
            task.taskTitle,
            updatedDescription,
            recipientUserId,
            auth.userId
          );
        }
      }
    }

    return NextResponse.json({ taskProgress: updatedTaskProgress }, { status: 200 });
  } catch (error) {
    console.error('Error in PATCH /api/v1/task-progress:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the task progress entry' },
      { status: 500 }
    );
  }
}

/**
 * DELETE API to remove a task progress entry
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const progressId = url.searchParams.get('id');

    if (!progressId) {
      return NextResponse.json({ error: 'Task progress ID is required' }, { status: 400 });
    }

    const progressIdNum = Number(progressId);
    if (isNaN(progressIdNum)) {
      return NextResponse.json({ error: 'Invalid task progress ID' }, { status: 400 });
    }

    // Find the task progress entry
    const existingTaskProgress = await prisma.taskProgress.findUnique({
      where: { id: progressIdNum },
    });

    if (!existingTaskProgress) {
      return NextResponse.json({ error: 'Task progress entry not found' }, { status: 404 });
    }

    // Check if user has permission to delete the task progress entry
    // Only the user who created the progress entry can delete it
    if (existingTaskProgress.updatedByUserId !== auth.userId) {
      return NextResponse.json(
        {
          error: 'You do not have permission to delete this task progress entry',
        },
        { status: 403 }
      );
    }

    // Delete the task progress entry
    await prisma.taskProgress.delete({
      where: { id: progressIdNum },
    });

    return NextResponse.json({
      message: 'Task progress entry deleted successfully',
    });
  } catch (error) {
    console.error('Error in DELETE /api/v1/task-progress:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the task progress entry' },
      { status: 500 }
    );
  }
}
