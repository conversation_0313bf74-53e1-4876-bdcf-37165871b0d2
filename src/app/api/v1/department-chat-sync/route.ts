import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth';
import { 
  ensureDepartmentChatWithMembers,
  getMemberChatStatus,
  syncMemberChatsOnCreation,
  syncMemberChatsOnDepartmentChange,
  syncMemberChatsOnRemoval
} from '@/services/memberChatService';

/**
 * GET - Get member chat status or department chat information
 * Query parameters:
 * - action: 'member-status' | 'department-info'
 * - userId: number (for member-status action)
 * - departmentId: number
 * - organizationId: number (for member-status action)
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners and admins can access department chat sync operations
    if (!auth.isOwner && !auth.isAdmin) {
      return NextResponse.json({ error: 'Only owners and admins can access department chat synchronization' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'member-status') {
      const userId = searchParams.get('userId');
      const departmentId = searchParams.get('departmentId');
      const organizationId = searchParams.get('organizationId');

      if (!userId || !departmentId || !organizationId) {
        return NextResponse.json({ 
          error: 'userId, departmentId, and organizationId are required for member-status action' 
        }, { status: 400 });
      }

      const userIdNum = parseInt(userId, 10);
      const departmentIdNum = parseInt(departmentId, 10);
      const organizationIdNum = parseInt(organizationId, 10);

      if (isNaN(userIdNum) || isNaN(departmentIdNum) || isNaN(organizationIdNum)) {
        return NextResponse.json({ error: 'Invalid ID parameters' }, { status: 400 });
      }

      const status = await getMemberChatStatus(userIdNum, departmentIdNum, organizationIdNum);
      
      return NextResponse.json({
        action: 'member-status',
        userId: userIdNum,
        departmentId: departmentIdNum,
        organizationId: organizationIdNum,
        chatStatus: status,
      });
    }

    return NextResponse.json({ error: 'Invalid action. Use "member-status"' }, { status: 400 });
  } catch (error) {
    console.error('Error in department chat sync GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST - Repair department chat or sync member chats
 * Body:
 * - action: 'repair-department' | 'sync-member-creation' | 'sync-member-removal' | 'sync-department-change'
 * - departmentId: number (for repair-department)
 * - userId: number (for sync actions)
 * - organizationId: number (for sync actions)
 * - oldDepartmentId: number (for sync-department-change)
 * - newDepartmentId: number (for sync-department-change)
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners and admins can perform department chat sync operations
    if (!auth.isOwner && !auth.isAdmin) {
      return NextResponse.json({ error: 'Only owners and admins can perform department chat synchronization' }, { status: 403 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'repair-department') {
      const { departmentId } = body;

      if (!departmentId || typeof departmentId !== 'number') {
        return NextResponse.json({ error: 'Valid departmentId is required' }, { status: 400 });
      }

      const result = await ensureDepartmentChatWithMembers(departmentId);
      
      return NextResponse.json({
        action: 'repair-department',
        departmentId,
        ...result,
      });
    }

    if (action === 'sync-member-creation') {
      const { userId, departmentId, organizationId } = body;

      if (!userId || !departmentId || !organizationId) {
        return NextResponse.json({ error: 'userId, departmentId, and organizationId are required' }, { status: 400 });
      }

      const result = await syncMemberChatsOnCreation(userId, departmentId, organizationId);
      
      return NextResponse.json({
        action: 'sync-member-creation',
        userId,
        departmentId,
        organizationId,
        ...result,
      });
    }

    if (action === 'sync-member-removal') {
      const { userId, departmentId, organizationId } = body;

      if (!userId || !departmentId || !organizationId) {
        return NextResponse.json({ error: 'userId, departmentId, and organizationId are required' }, { status: 400 });
      }

      const result = await syncMemberChatsOnRemoval(userId, departmentId, organizationId);
      
      return NextResponse.json({
        action: 'sync-member-removal',
        userId,
        departmentId,
        organizationId,
        ...result,
      });
    }

    if (action === 'sync-department-change') {
      const { userId, oldDepartmentId, newDepartmentId } = body;

      if (!userId || !oldDepartmentId || !newDepartmentId) {
        return NextResponse.json({ error: 'userId, oldDepartmentId, and newDepartmentId are required' }, { status: 400 });
      }

      const result = await syncMemberChatsOnDepartmentChange(userId, oldDepartmentId, newDepartmentId);
      
      return NextResponse.json({
        action: 'sync-department-change',
        userId,
        oldDepartmentId,
        newDepartmentId,
        ...result,
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in department chat sync POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT - Bulk sync operations for departments
 * Body:
 * - action: 'bulk-repair-departments'
 * - organizationId: number
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only owners can perform bulk sync operations
    if (!auth.isOwner) {
      return NextResponse.json({ error: 'Only owners can perform bulk sync operations' }, { status: 403 });
    }

    const body = await request.json();
    const { action, organizationId } = body;

    if (action === 'bulk-repair-departments') {
      if (!organizationId || typeof organizationId !== 'number') {
        return NextResponse.json({ error: 'Valid organizationId is required' }, { status: 400 });
      }

      // Get all departments in the organization
      const { prisma } = await import('@/lib/prisma');
      const departments = await prisma.department.findMany({
        where: { organizationId },
        select: { id: true, name: true },
      });

      const results = [];
      const errors = [];

      for (const department of departments) {
        try {
          const result = await ensureDepartmentChatWithMembers(department.id);
          results.push({
            departmentId: department.id,
            departmentName: department.name,
            ...result,
          });
        } catch (error) {
          errors.push(`Failed to repair department ${department.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return NextResponse.json({
        action: 'bulk-repair-departments',
        organizationId,
        results,
        summary: {
          total: departments.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length,
          errors,
        },
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in department chat sync PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
