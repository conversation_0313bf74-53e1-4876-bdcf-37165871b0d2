import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyJwt } from '@/lib/jwt';

// Helper function to extract token from request
function getTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Helper function to verify user is authenticated and get user ID
async function authenticateUser(request: NextRequest) {
  const token = getTokenFromRequest(request);
  if (!token) {
    return { error: 'Authentication required', status: 401 };
  }

  const payload = verifyJwt(token);
  if (!payload) {
    return { error: 'Invalid or expired token', status: 401 };
  }

  return { userId: payload.userId };
}

/**
 * GET API to fetch all task progress types
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Fetch all task progress types
    const progressTypes = await prisma.taskProgressType.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    return NextResponse.json({ progressTypes });
  } catch (error) {
    console.error('Error in GET /api/v1/task-progress-types:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching task progress types' },
      { status: 500 }
    );
  }
}
