'use client';

import RegisterForm from './components/RegisterForm';
import styled from 'styled-components';
import Squares from '../login/Squares';
import RotatingText from '@/components/ui/RotationText';

const RegisterPageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: transparent;
  position: relative;
  min-width: 100vw;
  height: 100vh;
  align-items: top;
  justify-content: center;
`;

const FormSection = styled.div`
  flex: 1;
  display: flex;
  align-items: start;
  justify-content: center;
  position: absolute;
  z-index: 2;
  max-height: 100vh;
  overflow-y: auto;
  padding-top: 4rem;
  padding-bottom: 2rem;
  width: 100%;

  /* Tablet */
  @media (max-width: 1023px) {
    padding-top: 3rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding-top: 2rem;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-bottom: 1rem;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding-top: 1.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
`;

const FormCard = styled.div`
  width: 100%;
  max-width: 450px;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #4f46e5;
  position: relative;
  z-index: 10;

  /* Tablet */
  @media (max-width: 1023px) {
    max-width: 400px;
    padding: 2rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    max-width: 100%;
    padding: 1.5rem;
    margin: 0 0.5rem;
    border-radius: 12px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 1.25rem;
    margin: 0 0.25rem;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    padding: 1rem;
    margin: 0 0.125rem;
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  font-size: 2.25rem;
  font-weight: 700;
  color: #474747;
  flex-wrap: wrap;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 2rem;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 1.75rem;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
    justify-content: center;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 1.5rem;
    gap: 0.25rem;
    margin-bottom: 1rem;
  }
`;



export default function RegisterPage() {
  return (
    <RegisterPageContainer>
      <Squares
        speed={0.1}
        squareSize={40}
        direction="diagonal"
        borderColor="#dedede66"
        hoverFillColor="#4F46E566"
      />
      <FormSection>
        <FormCard>
          <Logo>
            <span style={{ color: '#474747' }}>เฮีย</span> ·{' '}
            <span style={{ color: '#f56042' }}>สั่ง</span> ·{' '}
            <span style={{ color: '#474747' }}>มา</span>
            <RotatingText
              texts={['Task', 'Management', 'Reward', 'Productivity']}
              mainClassName="flex items-center text-gray-500 text-xs md:text-md font-bold px-2 md:px-4 h-8 md:h-10 rounded-md bg-[#474747] text-white"
              staggerFrom={'last'}
              staggerDuration={0.025}
              splitLevelClassName="overflow-hidden"
              transition={{ type: 'spring', damping: 30, stiffness: 400 }}
              rotationInterval={2000}
            />
          </Logo>
          <RegisterForm />
        </FormCard>
      </FormSection>
    </RegisterPageContainer>
  );
}
