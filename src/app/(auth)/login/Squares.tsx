import React, { useRef, useEffect } from 'react';

type CanvasStrokeStyle = string | CanvasGradient | CanvasPattern;

interface GridOffset {
  x: number;
  y: number;
}

interface SquaresProps {
  direction?: 'diagonal' | 'up' | 'right' | 'down' | 'left';
  speed?: number;
  borderColor?: CanvasStrokeStyle;
  squareSize?: number;
  hoverFillColor?: CanvasStrokeStyle;
}

const Squares: React.FC<SquaresProps> = ({
  direction = 'right',
  speed = 1,
  borderColor = '#999',
  squareSize = 40,
  hoverFillColor = '#222',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const requestRef = useRef<number | null>(null);
  const numSquaresX = useRef<number>(0);
  const numSquaresY = useRef<number>(0);
  const gridOffset = useRef<GridOffset>({ x: 0, y: 0 });
  const hoveredSquareRef = useRef<GridOffset | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');

    const getResponsiveSquareSize = () => {
      const width = window.innerWidth;
      if (width <= 360) return Math.max(squareSize * 0.5, 15); // Very small mobile
      if (width <= 480) return Math.max(squareSize * 0.6, 20); // Small mobile
      if (width <= 767) return Math.max(squareSize * 0.75, 25); // Mobile
      if (width <= 1023) return Math.max(squareSize * 0.9, 30); // Tablet
      return squareSize; // Desktop
    };

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
      const responsiveSize = getResponsiveSquareSize();
      numSquaresX.current = Math.ceil(canvas.width / responsiveSize) + 1;
      numSquaresY.current = Math.ceil(canvas.height / responsiveSize) + 1;
    };

    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();

    const drawGrid = () => {
      if (!ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const responsiveSize = getResponsiveSquareSize();
      const startX = Math.floor(gridOffset.current.x / responsiveSize) * responsiveSize;
      const startY = Math.floor(gridOffset.current.y / responsiveSize) * responsiveSize;

      for (let x = startX; x < canvas.width + responsiveSize; x += responsiveSize) {
        for (let y = startY; y < canvas.height + responsiveSize; y += responsiveSize) {
          const squareX = x - (gridOffset.current.x % responsiveSize);
          const squareY = y - (gridOffset.current.y % responsiveSize);

          if (
            hoveredSquareRef.current &&
            Math.floor((x - startX) / responsiveSize) === hoveredSquareRef.current.x &&
            Math.floor((y - startY) / responsiveSize) === hoveredSquareRef.current.y
          ) {
            ctx.fillStyle = hoverFillColor;
            ctx.fillRect(squareX, squareY, responsiveSize, responsiveSize);
          }

          ctx.strokeStyle = borderColor;
          ctx.strokeRect(squareX, squareY, responsiveSize, responsiveSize);
        }
      }

      const gradient = ctx.createRadialGradient(
        canvas.width / 2,
        canvas.height / 2,
        0,
        canvas.width / 2,
        canvas.height / 2,
        Math.sqrt(canvas.width ** 2 + canvas.height ** 2) / 2
      );
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    };

    const updateAnimation = () => {
      const effectiveSpeed = Math.max(speed, 0.1);
      const responsiveSize = getResponsiveSquareSize();

      switch (direction) {
        case 'right':
          gridOffset.current.x = (gridOffset.current.x - effectiveSpeed + responsiveSize) % responsiveSize;
          break;
        case 'left':
          gridOffset.current.x = (gridOffset.current.x + effectiveSpeed + responsiveSize) % responsiveSize;
          break;
        case 'up':
          gridOffset.current.y = (gridOffset.current.y + effectiveSpeed + responsiveSize) % responsiveSize;
          break;
        case 'down':
          gridOffset.current.y = (gridOffset.current.y - effectiveSpeed + responsiveSize) % responsiveSize;
          break;
        case 'diagonal':
          gridOffset.current.x = (gridOffset.current.x - effectiveSpeed + responsiveSize) % responsiveSize;
          gridOffset.current.y = (gridOffset.current.y - effectiveSpeed + responsiveSize) % responsiveSize;
          break;
        default:
          break;
      }

      drawGrid();
      requestRef.current = requestAnimationFrame(updateAnimation);
    };

    const handleMouseMove = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      const responsiveSize = getResponsiveSquareSize();
      const startX = Math.floor(gridOffset.current.x / responsiveSize) * responsiveSize;
      const startY = Math.floor(gridOffset.current.y / responsiveSize) * responsiveSize;

      const hoveredSquareX = Math.floor((mouseX + gridOffset.current.x - startX) / responsiveSize);
      const hoveredSquareY = Math.floor((mouseY + gridOffset.current.y - startY) / responsiveSize);

      if (
        !hoveredSquareRef.current ||
        hoveredSquareRef.current.x !== hoveredSquareX ||
        hoveredSquareRef.current.y !== hoveredSquareY
      ) {
        hoveredSquareRef.current = { x: hoveredSquareX, y: hoveredSquareY };
      }
    };

    const handleMouseLeave = () => {
      hoveredSquareRef.current = null;
    };

    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseleave', handleMouseLeave);
    requestRef.current = requestAnimationFrame(updateAnimation);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (requestRef.current) cancelAnimationFrame(requestRef.current);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [direction, speed, borderColor, hoverFillColor, squareSize]);

  return (
    <canvas
      ref={canvasRef}
      className="squares-canvas"
      style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', zIndex: 0 }}
    ></canvas>
  );
};

export default Squares;
