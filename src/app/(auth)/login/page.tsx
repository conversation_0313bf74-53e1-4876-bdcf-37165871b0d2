'use client';

import LoginForm from './components/LoginForm';
import styled from 'styled-components';
import Squares from './Squares';
import RotatingText from '@/components/ui/RotationText';

const LoginPageContainer = styled.div`
  display: flex;
  min-height: 100vh;
  overflow: hidden;
  background: transparent;
  position: relative;
  min-width: 100vw;
  align-items: center;
  justify-content: center;
`;

const FormSection = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: absolute;
  z-index: 2;
  width: 100%;

  /* Tablet */
  @media (max-width: 1023px) {
    padding: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    padding: 1rem;
    align-items: flex-start;
    padding-top: 2rem;
  }
`;

const FormCard = styled.div`
  width: 100%;
  max-width: 450px;
  padding: 2.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  position: relative;
  z-index: 10;

  /* Tablet */
  @media (max-width: 1023px) {
    max-width: 400px;
    padding: 2rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    max-width: 100%;
    padding: 1.5rem;
    margin: 0 0.5rem;
    border-radius: 12px;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    padding: 1.25rem;
    margin: 0 0.25rem;
  }

  /* Very small mobile */
  @media (max-width: 360px) {
    padding: 1rem;
    margin: 0 0.125rem;
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  font-size: 2.25rem;
  font-weight: 700;
  color: #474747;
  flex-wrap: wrap;

  /* Tablet */
  @media (max-width: 1023px) {
    font-size: 2rem;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Mobile */
  @media (max-width: 767px) {
    font-size: 1.75rem;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
    justify-content: center;
  }

  /* Small mobile */
  @media (max-width: 480px) {
    font-size: 1.5rem;
    gap: 0.25rem;
    margin-bottom: 1rem;
  }
`;



export default function LoginPage() {
  return (
    <LoginPageContainer>
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          minWidth: '100vw',
          minHeight: '100vh',
          zIndex: 0,
        }}
      >
        <Squares
          speed={0.1}
          squareSize={40}
          direction="diagonal"
          borderColor="#dedede66"
          hoverFillColor="#4F46E566"
        />
      </div>
      <FormSection>
        <FormCard>
          <Logo>
            <span style={{ color: '#474747' }}>เฮีย</span> ·{' '}
            <span style={{ color: '#f56042' }}>สั่ง</span> ·{' '}
            <span style={{ color: '#474747' }}>มา</span>
            <RotatingText
              texts={['Task', 'Management', 'Reward', 'Productivity']}
              mainClassName="flex items-center text-gray-500 text-xs md:text-md font-bold px-2 md:px-4 h-8 md:h-10 rounded-md bg-[#474747] text-white"
              staggerFrom={'last'}
              staggerDuration={0.025}
              splitLevelClassName="overflow-hidden"
              transition={{ type: 'spring', damping: 30, stiffness: 400 }}
              rotationInterval={2000}
            />
          </Logo>
          <LoginForm />
        </FormCard>
      </FormSection>
    </LoginPageContainer>
  );
}
