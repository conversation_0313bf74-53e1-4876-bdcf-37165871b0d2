'use client';

import React from 'react';
import styled from 'styled-components';
import { Inbox, Send, Share2, <PERSON><PERSON><PERSON><PERSON>, Clock, Award } from 'lucide-react';
import { appTheme } from '@/app/theme';

const StatsContainer = styled.div`
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.main};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.md};
`;

const StatCard = styled.div`
  padding: ${appTheme.spacing.sm};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
  text-align: center;
  border: 1px solid ${appTheme.colors.border};
`;

const StatIcon = styled.div<{ $color: string }>`
  width: 24px;
  height: 24px;
  margin: 0 auto ${appTheme.spacing.xs} auto;
  color: ${props => props.$color};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const StatValue = styled.div`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const StatLabel = styled.div`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: ${appTheme.colors.text.secondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const QuickStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.sm};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid ${appTheme.colors.border};
`;

const QuickStatItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.xs};
  color: ${appTheme.colors.text.secondary};
`;

interface FeedbackStatsProps {
  myFeedback: {
    created: any[];
    assigned: any[];
    shared: any[];
  };
}

export default function FeedbackStats({ myFeedback }: FeedbackStatsProps) {
  // Calculate stats
  const receivedCount = myFeedback.assigned?.length || 0;
  const givenCount = myFeedback.created?.length || 0;
  const sharedCount = myFeedback.shared?.length || 0;

  // Calculate pending and accepted feedback
  const pendingCount = myFeedback.assigned?.filter((feedback: any) => 
    feedback.feedbackUsers?.some((fu: any) => fu.isAccept === null)
  ).length || 0;

  const acceptedCount = myFeedback.assigned?.filter((feedback: any) => 
    feedback.feedbackUsers?.some((fu: any) => fu.isAccept === true)
  ).length || 0;

  // Calculate total growth tokens
  const totalGrowthTokens = myFeedback.assigned?.reduce((total: number, feedback: any) => {
    const userFeedback = feedback.feedbackUsers?.find((fu: any) => fu.isAccept === true);
    return total + (userFeedback ? (feedback.growthToken || 0) : 0);
  }, 0) || 0;

  return (
    <StatsContainer>
      <StatsGrid>
        <StatCard>
          <StatIcon $color={appTheme.colors.primary}>
            <Inbox size={16} />
          </StatIcon>
          <StatValue>{receivedCount}</StatValue>
          <StatLabel>Received</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon $color={appTheme.colors.secondary}>
            <Send size={16} />
          </StatIcon>
          <StatValue>{givenCount}</StatValue>
          <StatLabel>Given</StatLabel>
        </StatCard>

        <StatCard>
          <StatIcon $color="#10b981">
            <Share2 size={16} />
          </StatIcon>
          <StatValue>{sharedCount}</StatValue>
          <StatLabel>Shared</StatLabel>
        </StatCard>
      </StatsGrid>

      <QuickStats>
        <QuickStatItem>
          <Clock size={12} />
          {pendingCount} pending
        </QuickStatItem>
        
        <QuickStatItem>
          <CheckCircle size={12} />
          {acceptedCount} accepted
        </QuickStatItem>
        
        <QuickStatItem>
          <Award size={12} />
          {totalGrowthTokens} tokens
        </QuickStatItem>
      </QuickStats>
    </StatsContainer>
  );
}
