'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { formatDistanceToNow } from 'date-fns';
import { 
  User, 
  Clock, 
  CheckCircle, 
  XCircle, 
  MessageSquare,
  Award,
  Share2,
  Edit3,
  Menu,
  Calendar
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import { feedbackUserApi } from '@/services/feedbackService';

const DetailContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: ${appTheme.colors.background.main};
`;

const DetailHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.light};
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
`;

const MobileMenuButton = styled.button`
  display: none;
  
  @media (max-width: ${appTheme.breakpoints.md}) {
    display: flex;
    padding: ${appTheme.spacing.sm};
    background: ${appTheme.colors.background.main};
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.md};
    cursor: pointer;
    align-items: center;
    justify-content: center;
  }
`;

const HeaderTitle = styled.h2`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0;
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'success' | 'danger' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  border: 1px solid ${props => {
    switch (props.$variant) {
      case 'primary': return appTheme.colors.primary;
      case 'success': return appTheme.colors.success.main;
      case 'danger': return appTheme.colors.error.main;
      default: return appTheme.colors.border;
    }
  }};
  background-color: ${props => {
    switch (props.$variant) {
      case 'primary': return appTheme.colors.primary;
      case 'success': return appTheme.colors.success.main;
      case 'danger': return appTheme.colors.error.main;
      default: return appTheme.colors.background.main;
    }
  }};
  color: ${props => {
    switch (props.$variant) {
      case 'primary':
      case 'success':
      case 'danger':
        return 'white';
      default:
        return appTheme.colors.text.secondary;
    }
  }};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  transition: ${appTheme.transitions.default};

  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const DetailContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${appTheme.spacing.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: ${appTheme.colors.text.secondary};
  gap: ${appTheme.spacing.md};
`;

const EmptyIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: ${appTheme.colors.background.lighter};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.tertiary};
`;

const FeedbackMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.md};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.secondary};
`;

const FeedbackSection = styled.div`
  margin-bottom: ${appTheme.spacing.xl};
`;

const SectionTitle = styled.h3`
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
  margin: 0 0 ${appTheme.spacing.md} 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const SectionContent = styled.div`
  font-size: ${appTheme.typography.fontSizes.base};
  line-height: 1.6;
  color: ${appTheme.colors.text.secondary};
  background: ${appTheme.colors.background.light};
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  border-left: 4px solid ${appTheme.colors.primary};
`;

const ReflectionSection = styled.div`
  margin-top: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.lg};
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
`;

const ReflectionTextarea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.base};
  font-family: inherit;
  resize: vertical;
  background: ${appTheme.colors.background.main};

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 2px ${appTheme.colors.primaryLight};
  }
`;

const ReflectionActions = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
  margin-top: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

type FeedbackTab = 'received' | 'given' | 'shared';

interface FeedbackDetailProps {
  feedback: any;
  activeTab: FeedbackTab;
  onRefresh: () => void;
  onMobileSidebarOpen: () => void;
}

export default function FeedbackDetail({
  feedback,
  activeTab,
  onRefresh,
  onMobileSidebarOpen,
}: FeedbackDetailProps) {
  const [reflection, setReflection] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showReflection, setShowReflection] = useState(false);

  if (!feedback) {
    return (
      <DetailContainer>
        <DetailHeader>
          <HeaderLeft>
            <MobileMenuButton onClick={onMobileSidebarOpen}>
              <Menu size={20} />
            </MobileMenuButton>
            <HeaderTitle>Select Feedback</HeaderTitle>
          </HeaderLeft>
        </DetailHeader>
        <DetailContent>
          <EmptyState>
            <EmptyIcon>
              <MessageSquare size={40} />
            </EmptyIcon>
            <div>
              <h3>No feedback selected</h3>
              <p>Choose a feedback from the list to view details</p>
            </div>
          </EmptyState>
        </DetailContent>
      </DetailContainer>
    );
  }

  const handleAcceptFeedback = async () => {
    if (!feedback || activeTab !== 'received') return;

    try {
      setIsSubmitting(true);
      // Get current user ID from feedback users
      const currentUserFeedback = feedback.feedbackUsers?.find((fu: any) => fu.isAccept === null);
      if (!currentUserFeedback) return;

      await feedbackUserApi.updateFeedbackUserStatus({
        feedbackId: feedback.id,
        userId: currentUserFeedback.user.id,
        isAccept: true,
        isDiscard: false,
        reflection: reflection || undefined,
      });

      onRefresh();
    } catch (error) {
      console.error('Failed to accept feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeclineFeedback = async () => {
    if (!feedback || activeTab !== 'received') return;

    try {
      setIsSubmitting(true);
      const currentUserFeedback = feedback.feedbackUsers?.find((fu: any) => fu.isAccept === null);
      if (!currentUserFeedback) return;

      await feedbackUserApi.updateFeedbackUserStatus({
        feedbackId: feedback.id,
        userId: currentUserFeedback.user.id,
        isAccept: false,
        isDiscard: true,
      });

      onRefresh();
    } catch (error) {
      console.error('Failed to decline feedback:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const canTakeAction = activeTab === 'received' && 
    feedback.feedbackUsers?.some((fu: any) => fu.isAccept === null);

  const userFeedback = feedback.feedbackUsers?.find((fu: any) => fu.isAccept !== null);
  const isAccepted = userFeedback?.isAccept === true;
  const isDeclined = userFeedback?.isAccept === false || userFeedback?.isDiscard;

  return (
    <DetailContainer>
      <DetailHeader>
        <HeaderLeft>
          <MobileMenuButton onClick={onMobileSidebarOpen}>
            <Menu size={20} />
          </MobileMenuButton>
          <HeaderTitle>
            {feedback.feedbackType?.displayName || feedback.feedbackType?.name || 'Feedback'}
          </HeaderTitle>
        </HeaderLeft>
        
        <HeaderActions>
          {canTakeAction && (
            <>
              <ActionButton
                $variant="danger"
                onClick={handleDeclineFeedback}
                disabled={isSubmitting}
              >
                <XCircle size={16} />
                Decline
              </ActionButton>
              <ActionButton
                $variant="success"
                onClick={() => setShowReflection(!showReflection)}
              >
                <CheckCircle size={16} />
                Accept
              </ActionButton>
            </>
          )}
        </HeaderActions>
      </DetailHeader>

      <DetailContent>
        <FeedbackMeta>
          <MetaItem>
            <User size={16} />
            {activeTab === 'received' 
              ? `From: ${feedback.createFrom?.firstName} ${feedback.createFrom?.lastName}`
              : `Created by you`
            }
          </MetaItem>
          <MetaItem>
            <Calendar size={16} />
            {formatDistanceToNow(new Date(feedback.createdAt), { addSuffix: true })}
          </MetaItem>
          {feedback.growthToken && feedback.growthToken > 0 && (
            <MetaItem>
              <Award size={16} />
              {feedback.growthToken} Growth Token{feedback.growthToken > 1 ? 's' : ''}
            </MetaItem>
          )}
          {isAccepted && (
            <MetaItem style={{ color: appTheme.colors.success.main }}>
              <CheckCircle size={16} />
              Accepted
            </MetaItem>
          )}
          {isDeclined && (
            <MetaItem style={{ color: appTheme.colors.error.main }}>
              <XCircle size={16} />
              Declined
            </MetaItem>
          )}
        </FeedbackMeta>

        {feedback.situation && (
          <FeedbackSection>
            <SectionTitle>Situation</SectionTitle>
            <SectionContent>{feedback.situation}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.behavior && (
          <FeedbackSection>
            <SectionTitle>Behavior</SectionTitle>
            <SectionContent>{feedback.behavior}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.impact && (
          <FeedbackSection>
            <SectionTitle>Impact</SectionTitle>
            <SectionContent>{feedback.impact}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.actionable && (
          <FeedbackSection>
            <SectionTitle>Actionable</SectionTitle>
            <SectionContent>{feedback.actionable}</SectionContent>
          </FeedbackSection>
        )}

        {feedback.appreciation && (
          <FeedbackSection>
            <SectionTitle>Appreciation</SectionTitle>
            <SectionContent>{feedback.appreciation}</SectionContent>
          </FeedbackSection>
        )}

        {userFeedback?.reflection && (
          <FeedbackSection>
            <SectionTitle>Your Reflection</SectionTitle>
            <SectionContent>{userFeedback.reflection}</SectionContent>
          </FeedbackSection>
        )}

        {showReflection && canTakeAction && (
          <ReflectionSection>
            <SectionTitle>
              <Edit3 size={20} />
              Add Your Reflection (Optional)
            </SectionTitle>
            <ReflectionTextarea
              value={reflection}
              onChange={(e) => setReflection(e.target.value)}
              placeholder="Share your thoughts about this feedback..."
            />
            <ReflectionActions>
              <ActionButton onClick={() => setShowReflection(false)}>
                Cancel
              </ActionButton>
              <ActionButton
                $variant="success"
                onClick={handleAcceptFeedback}
                disabled={isSubmitting}
              >
                <CheckCircle size={16} />
                {isSubmitting ? 'Accepting...' : 'Accept Feedback'}
              </ActionButton>
            </ReflectionActions>
          </ReflectionSection>
        )}
      </DetailContent>
    </DetailContainer>
  );
}
