# Feedback UI Implementation

This directory contains the complete UI implementation for the feedback feature, following the patterns established in the chat system.

## Structure

```
src/app/(main)/feedback/
├── page.tsx                    # Main feedback page with data loading and socket integration
├── components/
│   ├── FeedbackLayout.tsx      # Main layout component with sidebar and content areas
│   ├── FeedbackTabs.tsx        # Tab navigation (Received, Given, Shared)
│   ├── FeedbackList.tsx        # List of feedback items in sidebar
│   ├── FeedbackDetail.tsx      # Detailed view of selected feedback
│   ├── FeedbackStats.tsx       # Statistics summary component
│   └── CreateFeedbackModal.tsx # Modal for creating new feedback
└── README.md                   # This documentation
```

## Features

### 📱 Responsive Design
- **Desktop**: Full sidebar + main content layout
- **Tablet**: Responsive spacing and sizing adjustments
- **Mobile**: Collapsible sidebar with overlay, optimized for touch

### 🔄 Real-time Updates
- Socket.io integration for live feedback notifications
- Automatic refresh when feedback is created, updated, or responded to
- Page visibility change detection for data refresh

### 📊 Feedback Management
- **Three main views**:
  - **Received**: Feedback assigned to the current user
  - **Given**: Feedback created by the current user
  - **Shared**: Feedback shared with the current user

### ⚡ Interactive Features
- **Accept/Decline feedback** with optional reflection
- **Create new feedback** with full form validation
- **Real-time statistics** showing counts and growth tokens
- **Feedback type filtering** and visual indicators

### 🎨 UI Components

#### FeedbackLayout
- Main container with responsive sidebar and content areas
- Mobile-friendly navigation with overlay
- Socket connection status integration

#### FeedbackTabs
- Tab navigation with badge counts
- Icons for each feedback type
- Active state management

#### FeedbackList
- Scrollable list of feedback items
- Status indicators (pending, accepted, declined)
- Preview text and metadata
- Empty state handling

#### FeedbackDetail
- Full feedback content display
- Action buttons for accept/decline
- Reflection input for accepted feedback
- Responsive header with mobile menu

#### FeedbackStats
- Quick overview of feedback counts
- Growth token summary
- Pending vs accepted statistics

#### CreateFeedbackModal
- Full-featured feedback creation form
- User selection interface
- Form validation and error handling
- All feedback fields (situation, behavior, impact, actionable, appreciation)

## API Integration

The UI integrates with the feedback API through the `feedbackService`:

- **feedbackApi**: CRUD operations for feedback
- **feedbackTypeApi**: Feedback type master data
- **feedbackUserApi**: User assignment and response management
- **feedbackService**: Helper functions and data formatting

## Socket Events

Listens for `feedback_notification` events to trigger real-time updates:

```typescript
useSocketEvent('feedback_notification', (data: any) => {
  if (data?.service === 'feedback') {
    refreshFeedbackData();
  }
});
```

## Styling

Uses styled-components with the application theme:
- Consistent spacing and colors
- Responsive breakpoints
- Smooth transitions and hover effects
- Mobile-optimized touch targets

## Usage

The feedback page is accessible at `/feedback` and automatically:
1. Loads user's feedback data on mount
2. Establishes socket connection for real-time updates
3. Provides full CRUD functionality for feedback management
4. Handles responsive layout for all device sizes

## Dependencies

- React 18+ with hooks
- styled-components for styling
- date-fns for date formatting
- lucide-react for icons
- Socket.io client for real-time updates
- Custom hooks for socket events and authentication

## Future Enhancements

- [ ] Feedback search and filtering
- [ ] Bulk actions for feedback management
- [ ] Advanced analytics and reporting
- [ ] Feedback templates and quick actions
- [ ] Integration with notification system
- [ ] Offline support with sync capabilities
