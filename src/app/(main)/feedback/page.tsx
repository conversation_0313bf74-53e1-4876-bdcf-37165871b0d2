'use client';

import React, { useState, useEffect, Suspense } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import FeedbackLayout from './components/FeedbackLayout';
import { feedbackService } from '@/services/feedbackService';
import { useSocket } from '@/lib/socket-context';
import { useSocketEvent } from '@/hooks/useSocket';

const FeedbackContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 100px);
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;

  /* Desktop (1024px+) - maintain current layout */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    height: calc(100vh - 100px);
    border-radius: ${appTheme.borderRadius.lg};
    box-shadow: ${appTheme.shadows.lg};
    margin: 0 auto;
  }

  /* Tablet (768px - 1023px) - adjust spacing and sizing */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    height: calc(100vh - 90px);
    border-radius: ${appTheme.borderRadius.md};
    box-shadow: ${appTheme.shadows.md};
    margin: 0 auto;
    max-width: 100%;
  }

  /* Mobile (< 768px) - account for footer menu at bottom */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: calc(100vh - 80px); /* Account for 80px footer menu */
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    max-width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 80px; /* Leave space for footer menu */
  }

  /* Small mobile (< 480px) - account for smaller footer menu */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    height: calc(100vh - 70px); /* Account for 70px footer menu */
    bottom: 70px; /* Leave space for smaller footer menu */
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${appTheme.colors.text.secondary};
  font-size: ${appTheme.typography.fontSizes.lg};
`;

interface FeedbackData {
  feedbackTypes: any[];
  myFeedback: {
    created: any[];
    assigned: any[];
    shared: any[];
  };
}

function FeedbackPageContent() {
  const [feedbackData, setFeedbackData] = useState<FeedbackData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isConnected } = useSocket();

  // Load initial feedback data
  useEffect(() => {
    const loadFeedbackData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await feedbackService.initializeFeedbackData();
        setFeedbackData(data);
      } catch (err) {
        console.error('Failed to load feedback data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load feedback data');
      } finally {
        setLoading(false);
      }
    };

    loadFeedbackData();
  }, []);

  // Refresh feedback data
  const refreshFeedbackData = async () => {
    try {
      const data = await feedbackService.initializeFeedbackData();
      setFeedbackData(data);
    } catch (err) {
      console.error('Failed to refresh feedback data:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh feedback data');
    }
  };

  // Listen for feedback notifications via socket
  useSocketEvent('feedback_notification', (data: any) => {
    console.log('Received feedback notification:', data);
    
    // Refresh feedback data when notifications are received
    if (data?.service === 'feedback') {
      refreshFeedbackData();
    }
  });

  // Refresh data when page becomes visible
  useEffect(() => {
    const handlePageVisibilityChange = () => {
      if (!document.hidden && feedbackData) {
        refreshFeedbackData();
      }
    };

    document.addEventListener('visibilitychange', handlePageVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handlePageVisibilityChange);
    };
  }, [feedbackData]);

  if (loading) {
    return (
      <FeedbackContainer>
        <LoadingContainer>Loading feedback...</LoadingContainer>
      </FeedbackContainer>
    );
  }

  if (error) {
    return (
      <FeedbackContainer>
        <LoadingContainer>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: appTheme.colors.error.main, marginBottom: '1rem' }}>
              Error: {error}
            </div>
            <button
              onClick={refreshFeedbackData}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: appTheme.colors.primary,
                color: 'white',
                border: 'none',
                borderRadius: appTheme.borderRadius.md,
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          </div>
        </LoadingContainer>
      </FeedbackContainer>
    );
  }

  if (!feedbackData) {
    return (
      <FeedbackContainer>
        <LoadingContainer>No feedback data available</LoadingContainer>
      </FeedbackContainer>
    );
  }

  return (
    <FeedbackContainer>
      <FeedbackLayout
        feedbackTypes={feedbackData.feedbackTypes}
        myFeedback={feedbackData.myFeedback}
        onRefresh={refreshFeedbackData}
        isSocketConnected={isConnected}
      />
    </FeedbackContainer>
  );
}

// Loading component for Suspense fallback
const FeedbackPageLoading = () => (
  <FeedbackContainer>
    <LoadingContainer>Loading feedback...</LoadingContainer>
  </FeedbackContainer>
);

export default function FeedbackPage() {
  return (
    <Suspense fallback={<FeedbackPageLoading />}>
      <FeedbackPageContent />
    </Suspense>
  );
}
