'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { X, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';
import { toast } from 'react-hot-toast';
import { appTheme, appStyles } from '@/app/theme';
import { useSocketEmit } from '@/hooks/useSocket';

interface TaskProgress {
  id: number;
  taskId: number;
  updatedByUserId: number;
  progressTypeId: number;
  progressDescription: string;
  createdAt: Date;
  updatedAt: Date;
  updatedByUser: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  progressType?: {
    id: number;
    name: string;
    description?: string;
  };
}

interface DeleteProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  progress: TaskProgress;
}

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 100%;
  max-width: 500px;
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.error.main};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    color: ${appTheme.colors.text.secondary};
    background-color: ${appTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing['2xl']};
`;

const WarningText = styled.p`
  margin: 0 0 ${appTheme.spacing.base};
  color: ${appTheme.colors.text.primary};
  line-height: 1.5;
`;

const ProgressPreview = styled.div`
  background-color: ${appTheme.colors.background.light};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${appTheme.spacing.base};
  margin: ${appTheme.spacing.base} 0;
  border-left: 3px solid ${appTheme.colors.error.main};
`;

const ProgressDescription = styled.div`
  color: ${appTheme.colors.text.primary};
  line-height: 1.5;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
  margin-top: ${appTheme.spacing.sm};
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-top: 1px solid ${appTheme.colors.border};
`;

const Button = styled.button`
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
`;

const CancelButton = styled(Button)`
  ${appStyles.button.secondary}
`;

const DeleteButton = styled(Button)`
  padding: 0.75rem 1.25rem;
  background-color: ${appTheme.colors.error.main};
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  box-shadow: 0 1px 3px ${appTheme.colors.error.light};

  &:hover {
    background-color: #d32f2f;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.25);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

// DeleteProgressModal component for confirming task progress deletion
export default function DeleteProgressModal({
  isOpen,
  onClose,
  onSuccess,
  progress,
}: DeleteProgressModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { getToken } = useAuth();
  const { userData } = useUserStore();
  const emit = useSocketEmit();

  if (!isOpen) return null;

  const handleDelete = async () => {
    // Validate that the current user is the one who created the progress update or is a boss
    if (progress.updatedByUserId !== userData?.id && !userData?.role.isOwner) {
      toast.error('You can only delete progress updates that you created');
      return;
    }

    try {
      setIsDeleting(true);
      const token = await getToken();

      const response = await fetch(`/api/v1/task-progress?id=${progress.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete progress: ${response.statusText}`);
      }

      // Emit socket notification for task progress deletion
      emit('send_notification', {
        service: 'task',
        id: progress.taskId,
      });

      toast.success('Progress deleted successfully');
      onSuccess();
      onClose();
    } catch (err) {
      console.error('Error deleting progress:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to delete progress');
    } finally {
      setIsDeleting(false);
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <AlertTriangle size={20} />
            Delete Progress
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>
        <ModalBody>
          <WarningText>
            Are you sure you want to delete this progress update? This action cannot be undone.
          </WarningText>

          <ProgressPreview>
            <div style={{ fontSize: '0.875rem', color: '#666', marginBottom: '0.25rem' }}>
              {progress.updatedByUser.firstName} {progress.updatedByUser.lastName} •{' '}
              {formatDate(progress.createdAt)}
            </div>
            {progress.progressType && (
              <div
                style={{
                  display: 'inline-block',
                  padding: '0.15rem 0.5rem',
                  backgroundColor: '#e6f7ff',
                  color: '#0070f3',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  marginBottom: '0.5rem',
                }}
              >
                {progress.progressType.name}
              </div>
            )}
            <ProgressDescription>{progress.progressDescription}</ProgressDescription>
          </ProgressPreview>
        </ModalBody>
        <ModalFooter>
          <CancelButton type="button" onClick={onClose}>
            Cancel
          </CancelButton>
          <DeleteButton onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? 'Deleting...' : 'Delete Progress'}
          </DeleteButton>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
