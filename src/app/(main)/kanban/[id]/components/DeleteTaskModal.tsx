'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-hot-toast';
import { appTheme, appStyles } from '@/app/theme';
import { useSocketEmit } from '@/hooks/useSocket';

// Types
interface Task {
  id: number;
  taskTitle: string;
}

interface DeleteTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task | null;
  onSuccess: () => void;
}

// Styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing['2xl']};
  width: 100%;
  max-width: 500px;
  box-shadow: ${appTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${appTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    color: ${appTheme.colors.text.secondary};
    background-color: ${appTheme.colors.background.lighter};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  margin-top: ${appTheme.spacing.xl};
`;

const CancelButton = styled.button`
  ${appStyles.button.secondary}
`;

const DeleteButton = styled.button`
  padding: 0.75rem 1.25rem;
  background-color: ${appTheme.colors.error.main};
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
  box-shadow: 0 1px 3px ${appTheme.colors.error.light};

  &:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(239, 68, 68, 0.25);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

export default function DeleteTaskModal({
  isOpen,
  onClose,
  task,
  onSuccess,
}: DeleteTaskModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { getToken } = useAuth();
  const emit = useSocketEmit();

  const handleDelete = async () => {
    if (!task) return;

    setIsDeleting(true);

    try {
      const token = await getToken();
      const response = await fetch(`/api/v1/task?id=${task.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete task');
      }

      // Emit socket notification for task deletion
      emit('send_notification', {
        service: 'task',
        id: task.id,
      });

      toast.success('Task deleted successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete task');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !task) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Delete Task</ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>
        <div style={{ marginBottom: appTheme.spacing.xl }}>
          <p style={{ marginBottom: appTheme.spacing.base }}>
            Are you sure you want to delete the task <strong>{task.taskTitle}</strong>?
          </p>
          <p
            style={{
              color: appTheme.colors.error.main,
              fontSize: appTheme.typography.fontSizes.sm,
            }}
          >
            This action cannot be undone. All task data including progress history will be
            permanently removed.
          </p>
        </div>
        <ButtonGroup>
          <CancelButton type="button" onClick={onClose}>
            Cancel
          </CancelButton>
          <DeleteButton type="button" onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? 'Deleting...' : 'Delete Task'}
          </DeleteButton>
        </ButtonGroup>
      </ModalContent>
    </ModalOverlay>
  );
}
