'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-hot-toast';
import { appTheme, appStyles } from '@/app/theme';
import { useSocketEmit } from '@/hooks/useSocket';

interface ProgressType {
  id: number;
  name: string;
  description?: string;
}

interface TaskProgress {
  id: number;
  taskId: number;
  updatedByUserId: number;
  progressTypeId: number;
  progressDescription: string;
  createdAt: Date;
  updatedAt: Date;
  updatedByUser: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  progressType?: {
    id: number;
    name: string;
    description?: string;
  };
}

interface AddRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProgressAdded: (newProgress: TaskProgress) => void;
  taskId: number;
}

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 100%;
  max-width: 600px;
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${appTheme.spacing.xl} ${appTheme.spacing['2xl']};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.xl};
  font-weight: ${appTheme.typography.fontWeights.semibold};
  color: ${appTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${appTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: ${appTheme.transitions.default};

  &:hover {
    color: ${appTheme.colors.text.secondary};
    background-color: ${appTheme.colors.background.lighter};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing['xl']} ${appTheme.spacing['xl']} 0 ${appTheme.spacing['xl']};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xl};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${appTheme.spacing.base};
`;

const Label = styled.label`
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  color: ${appTheme.colors.text.secondary};
`;

const TextArea = styled.textarea`
  ${appStyles.input}
  min-height: 150px;
  resize: vertical;
  font-family: inherit;
`;

const Select = styled.select`
  ${appStyles.input}
  background-color: ${appTheme.colors.background.light};
  font-family: inherit;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.xl} 0 ${appTheme.spacing.xl} 0;
  border-top: 1px solid ${appTheme.colors.border};
`;

const Button = styled.button`
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: ${appTheme.typography.fontWeights.medium};
  cursor: pointer;
  transition: ${appTheme.transitions.default};
`;

const CancelButton = styled(Button)`
  ${appStyles.button.secondary}
`;

const SubmitButton = styled(Button)`
  ${appStyles.button.primary}
`;

export default function AddRequestModal({
  isOpen,
  onClose,
  onProgressAdded,
  taskId,
}: AddRequestModalProps) {
  const [progressDescription, setProgressDescription] = useState('');
  // Fixed progressTypeId to 2 for requests
  const progressTypeId = 2;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { getToken } = useAuth();
  const emit = useSocketEmit();

  // Reset description when modal opens
  useEffect(() => {
    if (isOpen) {
      setProgressDescription('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!progressDescription.trim()) {
      toast.error('Please enter request description');
      return;
    }

    try {
      setIsSubmitting(true);
      const token = await getToken();

      const response = await fetch('/api/v1/task-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          taskId,
          progressDescription,
          progressTypeId,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to add request: ${response.statusText}`);
      }

      const data = await response.json();
      onProgressAdded(data.taskProgress);

      // Emit socket notification for task progress creation
      emit('send_notification', {
        service: 'task',
        id: taskId,
      });

      toast.success('Request added successfully');
      setProgressDescription('');
      onClose();
    } catch (err) {
      console.error('Error adding request:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to add request');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Add Task Request</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>
        <ModalBody>
          <Form onSubmit={handleSubmit}>
            <FormGroup>
              <Label htmlFor="progressDescription">Request Description</Label>
              <TextArea
                id="progressDescription"
                value={progressDescription}
                onChange={e => setProgressDescription(e.target.value)}
                placeholder="Describe your request for this task..."
                required
              />
            </FormGroup>
            <ModalFooter>
              <CancelButton type="button" onClick={onClose}>
                Cancel
              </CancelButton>
              <SubmitButton type="submit" disabled={isSubmitting || isLoading}>
                {isSubmitting ? 'Adding...' : 'Add Request'}
              </SubmitButton>
            </ModalFooter>
          </Form>
        </ModalBody>
      </ModalContent>
    </ModalOverlay>
  );
}
