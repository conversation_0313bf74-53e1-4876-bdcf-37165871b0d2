'use client';

import React, { useState, ReactNode, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { Trophy, Code, Loader2, Filter } from 'lucide-react';
import axios from 'axios';
import { appTheme as rankingTheme } from '@/app/theme';
import { useAuth } from '@/hooks/useAuth';
import useUserStore from '@/store/userStore';

const RankingContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${rankingTheme.spacing.xl};
  gap: ${rankingTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${rankingTheme.borderRadius.lg};

  @media (max-width: ${rankingTheme.breakpoints.md}) {
    padding: 0;
    gap: ${rankingTheme.spacing.xl};
  }
`;

const RankingHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${rankingTheme.spacing.lg};
  border-radius: ${rankingTheme.borderRadius.lg};
  background-color: ${rankingTheme.colors.background.main};
  box-shadow: ${rankingTheme.shadows.sm};
`;

const RankingTitle = styled.h1`
  font-size: ${rankingTheme.typography.fontSizes['3xl']};
  font-weight: ${rankingTheme.typography.fontWeights.bold};
  color: ${rankingTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${rankingTheme.spacing.md};

  @media (max-width: ${rankingTheme.breakpoints.md}) {
    font-size: ${rankingTheme.typography.fontSizes['2xl']};
  }
`;

const RankingContent = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const ContentArea = styled.div`
  border-radius: ${rankingTheme.borderRadius.lg};
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  overflow: hidden;
`;

const Tabs = styled.div`
  display: flex;
  gap: ${rankingTheme.spacing.md};
  margin-bottom: ${rankingTheme.spacing.xl};
  padding: ${rankingTheme.spacing.sm} 0;
  justify-content: center;
`;

const TabContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${rankingTheme.spacing.sm};
`;

const Tab = styled.button<{ $active: boolean }>`
  width: 120px;
  height: 40px;
  border-radius: 20px;
  border: none;
  background: ${props =>
    props.$active
      ? 'linear-gradient(135deg,rgb(255, 255, 255), rgb(244, 244, 244))'
      : 'rgba(255, 255, 255, 0.1)'};
  color: ${props => (props.$active ? '#000' : rankingTheme.colors.text.secondary)};
  cursor: pointer;
  position: relative;
  transition: ${rankingTheme.transitions.default};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${rankingTheme.typography.fontWeights.medium};
  box-shadow: ${props => (props.$active ? '0 4px 8px rgba(0, 0, 0, 0.2)' : 'none')};
  overflow: hidden;

  &:hover {
    background: ${props =>
      props.$active ? 'linear-gradient(135deg, #1a1a2e, #16213e)' : 'rgba(255, 255, 255, 0.2)'};
    transform: translateY(-2px);
  }
`;

const TabText = styled.span`
  font-size: ${rankingTheme.typography.fontSizes.sm};
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: ${rankingTheme.spacing.lg};
`;

const FilterLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${rankingTheme.colors.text.secondary};
  font-size: 0.9rem;
`;

const FilterSelect = styled.select`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.25));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  }
`;

const FilterButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.25));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 0.75rem 1.25rem;
  font-size: 0.95rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.075), rgba(0, 0, 0, 0));
  }
`;

const RankingTable = styled.div`
  background-color: ${rankingTheme.colors.background.main};
  border: 1px solid ${rankingTheme.colors.border};
  border-radius: ${rankingTheme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${rankingTheme.shadows.sm};
`;

const RankingRow = styled.div`
  display: grid;
  grid-template-columns: 80px 1fr 1fr 1fr 70px;
  padding: ${rankingTheme.spacing.base} ${rankingTheme.spacing.xl};
  align-items: center;
  border-bottom: 1px solid ${rankingTheme.colors.border};
  transition: ${rankingTheme.transitions.default};

  &:hover {
    background-color: ${rankingTheme.colors.background.light};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const TableHeader = styled(RankingRow)`
  font-weight: ${rankingTheme.typography.fontWeights.semibold};
  color: ${rankingTheme.colors.text.secondary};
  background-color: ${rankingTheme.colors.background.light};
  border-bottom: 2px solid ${rankingTheme.colors.border};
  padding-top: ${rankingTheme.spacing.md};
  padding-bottom: ${rankingTheme.spacing.md};
`;

const TableBody = styled.div``;

const Place = styled.div<{ $place: number }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-weight: ${rankingTheme.typography.fontWeights.semibold};
  font-size: ${rankingTheme.typography.fontSizes.sm};
  background: ${props => {
    if (props.$place === 1) return 'linear-gradient(135deg, #FFD700, #FFC107)';
    if (props.$place === 2) return 'linear-gradient(135deg, #C0C0C0, #E0E0E0)';
    if (props.$place === 3) return 'linear-gradient(135deg, #CD7F32, #D2691E)';
    return 'linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02))';
  }};
  color: ${props => (props.$place <= 3 ? '#fff' : rankingTheme.colors.text.secondary)};
  box-shadow: ${props => (props.$place <= 3 ? rankingTheme.shadows.md : 'none')};
`;

const UserCell = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const Avatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  background-image: ${props => (props.$imageUrl ? `url(${props.$imageUrl})` : 'none')};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: #4b5563;
  font-size: 0.875rem;
  ${props => props.$imageUrl && 'color: transparent;'}
`;

const UserName = styled.div`
  font-weight: 500;
  color: #111827;
`;

const UserEmail = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const Points = styled.div`
  font-weight: ${rankingTheme.typography.fontWeights.medium};
  color: ${rankingTheme.colors.text.primary};
`;

const Department = styled.div`
  font-weight: ${rankingTheme.typography.fontWeights.medium};
  color: ${rankingTheme.colors.text.primary};
`;

const Organization = styled.div`
  font-weight: ${rankingTheme.typography.fontWeights.medium};
  color: ${rankingTheme.colors.text.primary};
`;

// Top 3 Ranking Card Components
const waveAnimation = keyframes`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`;

const RankingCardContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: ${rankingTheme.spacing.xl};
  margin-bottom: ${rankingTheme.spacing.xl};
  flex-wrap: wrap;
  position: relative;
  height: 400px;
`;

const RankingCard = styled.div<{ $place: number }>`
  background: transparent;
  box-shadow: 0px 8px 28px -9px rgba(0, 0, 0, 0.45);
  position: absolute;
  width: 240px;
  height: 330px;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.3s ease;
  z-index: ${props => (props.$place === 1 ? 3 : props.$place === 2 ? 2 : 1)};

  /* Position based on place */
  ${props => {
    if (props.$place === 1) {
      return `
        left: 50%;
        transform: translateX(-50%);
        top: 0;
      `;
    } else if (props.$place === 2) {
      return `
        left: calc(50% - 380px);
        top: 40px;
      `;
    } else {
      return `
        left: calc(50% + 140px);
        top: 40px;
      `;
    }
  }}

  &:hover {
    transform: ${props => (props.$place === 1 ? 'translateX(-50%) scale(1.05)' : 'scale(1.05)')};
  }
`;

const Wave = styled.div<{ $place: number }>`
  position: absolute;
  width: 540px;
  height: 700px;
  opacity: 0.6;
  left: 0;
  top: 0;
  margin-left: -50%;
  margin-top: -70%;
  background: ${props => {
    if (props.$place === 1) return 'linear-gradient(744deg, #FFD700, #FFC107 60%, #FFE57F)';
    if (props.$place === 2) return 'linear-gradient(744deg, #C0C0C0, #E0E0E0 60%, #F5F5F5)';
    if (props.$place === 3) return 'linear-gradient(744deg, #CD7F32, #D2691E 60%, #E59866)';
    return 'linear-gradient(744deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02))';
  }};
  border-radius: 40%;
  animation: ${waveAnimation} 55s infinite linear;

  &:nth-child(2) {
    animation-duration: 50s;
  }

  &:nth-child(3) {
    animation-duration: 45s;
    top: 210px;
  }
`;

const CardAvatar = styled.div<{ $imageUrl?: string }>`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  background-image: ${props => (props.$imageUrl ? `url(${props.$imageUrl})` : 'none')};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  font-size: 1.5rem;
  margin: 0 auto;
  margin-top: -2.5em;
  margin-bottom: 1.5em;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  ${props => props.$imageUrl && 'color: transparent;'}
`;

const CardInfoTop = styled.div`
  text-align: center;
  font-size: 20px;
  position: absolute;
  top: 5.6em;
  left: 0;
  right: 0;
  color: rgb(255, 255, 255);
  font-weight: 600;
`;

const CardName = styled.div`
  font-size: 14px;
  font-weight: 100;
  position: relative;
  margin-top: 0.5em;
  text-transform: lowercase;
`;

const CardOrganization = styled.div`
  font-size: 12px;
  font-weight: 100;
  position: relative;
  margin-top: 0.5em;
  opacity: 0.8;
`;

const CardPosition = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
`;

const CardPoints = styled.div`
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
`;

// Types for API response
interface RankingUser {
  place: number;
  userId: string;
  username: string;
  email: string;
  imageUrl: string | null;
  points: number;
  department: {
    id: number;
    name: string;
  } | null;
  organization: {
    id: number;
    name: string;
  } | null;
}

interface Organization {
  id: number;
  name: string;
  description?: string;
  departments?: Department[];
}

interface Department {
  id: number;
  name: string;
  description?: string;
  organizationId: number;
}

interface RankingResponse {
  rankings: RankingUser[];
  period: string;
  filters: {
    organizationId?: number;
    departmentId?: number;
  };
}

interface RankingLayoutProps {
  children?: ReactNode;
}

const NoDataContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: ${rankingTheme.spacing.xl};
  background-color: ${rankingTheme.colors.background.main};
  border-radius: ${rankingTheme.borderRadius.lg};
  box-shadow: ${rankingTheme.shadows.sm};
  min-height: 200px;
  text-align: center;
`;

const NoDataIcon = styled.div`
  font-size: 3rem;
  margin-bottom: ${rankingTheme.spacing.md};
  color: ${rankingTheme.colors.text.secondary};
  opacity: 0.5;
`;

const NoDataText = styled.div`
  font-size: ${rankingTheme.typography.fontSizes.lg};
  color: ${rankingTheme.colors.text.secondary};
  margin-bottom: ${rankingTheme.spacing.sm};
`;

const NoDataSubText = styled.div`
  font-size: ${rankingTheme.typography.fontSizes.sm};
  color: ${rankingTheme.colors.text.tertiary};
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
`;

const LoadingSpinner = styled(Loader2)`
  animation: spin 1s linear infinite;
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const ErrorContainer = styled.div`
  padding: ${rankingTheme.spacing.xl};
  text-align: center;
  color: ${rankingTheme.colors.error.main};
  background-color: rgba(255, 0, 0, 0.05);
  border-radius: ${rankingTheme.borderRadius.md};
  margin: ${rankingTheme.spacing.xl} 0;
`;

export default function RankingLayout({ children }: RankingLayoutProps) {
  const [activeTab, setActiveTab] = useState<'daily' | 'monthly' | 'alltime'>('daily');
  const [rankings, setRankings] = useState<RankingUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<number | undefined>(
    undefined
  );
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<number | undefined>(undefined);
  const [isLoadingOrganizations, setIsLoadingOrganizations] = useState<boolean>(false);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState<boolean>(false);
  const { getToken } = useAuth();
  const { userData } = useUserStore();
  // Fetch organizations data for admin/owner users
  useEffect(() => {
    const fetchOrganizations = async () => {
      if (!userData || (!userData.role.isAdmin && !userData.role.isOwner)) return;

      setIsLoadingOrganizations(true);

      try {
        const token = await getToken();
        if (!token) return;

        const response = await axios.get('/api/v1/organization', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        setOrganizations(response.data.organizations);

        // Set the first organization as default if available
        if (response.data.organizations.length > 0) {
          setSelectedOrganizationId(response.data.organizations[0].id);
        }
      } catch (err) {
        console.error('Error fetching organizations:', err);
      } finally {
        setIsLoadingOrganizations(false);
      }
    };

    fetchOrganizations();
  }, [userData]);

  // Fetch departments when organization is selected
  useEffect(() => {
    const fetchDepartments = async () => {
      if (!selectedOrganizationId) {
        setDepartments([]);
        setSelectedDepartmentId(undefined);
        return;
      }

      setIsLoadingDepartments(true);

      try {
        const token = await getToken();
        if (!token) return;

        const response = await axios.get(
          `/api/v1/department?organizationId=${selectedOrganizationId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setDepartments(response.data.departments);
        setSelectedDepartmentId(undefined); // Reset department selection
      } catch (err) {
        console.error('Error fetching departments:', err);
      } finally {
        setIsLoadingDepartments(false);
      }
    };

    fetchDepartments();
  }, [selectedOrganizationId]);

  // Fetch rankings data from API
  useEffect(() => {
    const fetchRankings = async () => {
      setLoading(true);
      setError(null);

      try {
        // Get auth token from localStorage or your auth context
        const token = await getToken();
        if (!token) {
          setError('Authentication required');
          setLoading(false);
          return;
        }

        // Check if user is admin/owner and requires organization selection
        if (userData && (userData.role.isAdmin || userData.role.isOwner)) {
          // For admin/owner users, organization selection is required
          if (!selectedOrganizationId) {
            setLoading(false);
            return; // Don't fetch rankings until organization is selected
          }
        }

        // Build the URL with query parameters
        let url = `/api/v1/ranking?period=${activeTab}`;

        // Add filters if organization is selected (required for admin/owner)
        if (selectedOrganizationId) {
          url += `&organizationId=${selectedOrganizationId}`;

          if (selectedDepartmentId) {
            url += `&departmentId=${selectedDepartmentId}`;
          }
        }

        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        setRankings(response.data.rankings);
      } catch (err) {
        console.error('Error fetching rankings:', err);
        setError('Failed to load rankings. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchRankings();
  }, [activeTab, selectedOrganizationId, selectedDepartmentId, userData]);

  return (
    <RankingContainer>
      <RankingHeader>
        <RankingTitle>
          <Trophy size={24} />
          Rankings
        </RankingTitle>
      </RankingHeader>

      {/* Filter controls for admin/owner users */}
      {userData && (userData.role.isAdmin || userData.role.isOwner) && (
        <FilterContainer>
          <FilterLabel>
            <Filter size={16} />
            Filters:
          </FilterLabel>

          <FilterSelect
            value={selectedOrganizationId || ''}
            onChange={e =>
              setSelectedOrganizationId(e.target.value ? Number(e.target.value) : undefined)
            }
            disabled={isLoadingOrganizations}
          >
            <option value="">All Organizations</option>
            {organizations.map(org => (
              <option key={org.id} value={org.id}>
                {org.name}
              </option>
            ))}
          </FilterSelect>

          <FilterSelect
            value={selectedDepartmentId || ''}
            onChange={e =>
              setSelectedDepartmentId(e.target.value ? Number(e.target.value) : undefined)
            }
            disabled={isLoadingDepartments || !selectedOrganizationId}
          >
            <option value="">All Departments</option>
            {departments.map(dept => (
              <option key={dept.id} value={dept.id}>
                {dept.name}
              </option>
            ))}
          </FilterSelect>
        </FilterContainer>
      )}

      <RankingContent>
        <ContentArea>
          <Tabs>
            <TabContainer>
              <Tab $active={activeTab === 'daily'} onClick={() => setActiveTab('daily')}>
                <TabText>Daily</TabText>
              </Tab>
            </TabContainer>

            <TabContainer>
              <Tab $active={activeTab === 'monthly'} onClick={() => setActiveTab('monthly')}>
                <TabText>Monthly</TabText>
              </Tab>
            </TabContainer>

            <TabContainer>
              <Tab $active={activeTab === 'alltime'} onClick={() => setActiveTab('alltime')}>
                <TabText>All Time</TabText>
              </Tab>
            </TabContainer>
          </Tabs>

          {loading ? (
            <LoadingContainer>
              <LoadingSpinner size={40} />
            </LoadingContainer>
          ) : error ? (
            <ErrorContainer>{error}</ErrorContainer>
          ) : rankings.length === 0 ? (
            <NoDataContainer>
              <NoDataIcon>🏆</NoDataIcon>
              <NoDataText>No ranking data available</NoDataText>
              <NoDataSubText>
                {activeTab === 'daily'
                  ? 'No activity recorded for today yet.'
                  : activeTab === 'monthly'
                    ? 'No activity recorded for this month yet.'
                    : 'No activity recorded yet.'}
              </NoDataSubText>
            </NoDataContainer>
          ) : (
            <>
              {/* Top 3 Ranking Cards */}
              {rankings.length > 0 && (
                <RankingCardContainer>
                  {rankings.slice(0, 3).map(ranking => (
                    <RankingCard key={`card-${ranking.place}`} $place={ranking.place}>
                      <Wave $place={ranking.place} />
                      <Wave $place={ranking.place} />
                      <Wave $place={ranking.place} />
                      <CardPosition>{ranking.place}</CardPosition>
                      <CardInfoTop>
                        <CardAvatar $imageUrl={ranking.imageUrl || undefined}>
                          {ranking.username.charAt(0)}
                        </CardAvatar>
                        {ranking.username}
                        <CardName>{ranking.department?.name || 'Department'}</CardName>
                        <CardOrganization>
                          {ranking.organization?.name || 'Organization'}
                        </CardOrganization>
                      </CardInfoTop>
                      <CardPoints>{ranking.points} points</CardPoints>
                    </RankingCard>
                  ))}
                </RankingCardContainer>
              )}

              <RankingTable>
                <TableHeader>
                  <div>Place</div>
                  <div>User</div>
                  <div>Organization</div>
                  <div>Department</div>
                  <div>Points</div>
                </TableHeader>
                <TableBody>
                  {rankings.map(ranking => (
                    <RankingRow key={ranking.place}>
                      <Place $place={ranking.place}>{ranking.place}</Place>
                      <UserCell>
                        <Avatar $imageUrl={ranking.imageUrl || undefined}>
                          {ranking.username.charAt(0)}
                        </Avatar>
                        <UserInfo>
                          <UserName>{ranking.username}</UserName>
                          <UserEmail>{ranking.email}</UserEmail>
                        </UserInfo>
                      </UserCell>
                      <Organization>{ranking.organization?.name || ''}</Organization>
                      <Department>{ranking.department?.name || ''}</Department>
                      <Points>{ranking.points}</Points>
                    </RankingRow>
                  ))}
                </TableBody>
              </RankingTable>
            </>
          )}

          {children}
        </ContentArea>
      </RankingContent>
    </RankingContainer>
  );
}
