'use client';

import React, { useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { Bell, Check, Archive, Clock, RefreshCw, Filter } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'next/navigation';
import useNotifications from '@/hooks/useNotifications';
import { appTheme } from '@/app/theme';

const PageContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${appTheme.spacing.xl};
  gap: ${appTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${appTheme.borderRadius.lg};

  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 0;
    gap: ${appTheme.spacing.xl};
  }
`;

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.lg};
  background-color: ${appTheme.colors.background.main};
  box-shadow: ${appTheme.shadows.sm};
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
`;

const FilterButton = styled.button<{ $active: boolean }>`
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid ${props => (props.$active ? '#3b82f6' : '#e5e7eb')};
  background-color: ${props => (props.$active ? '#ebf5ff' : 'white')};
  color: ${props => (props.$active ? '#3b82f6' : '#6b7280')};
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => (props.$active ? '#dbeafe' : '#f9fafb')};
  }
`;

const ActionButton = styled.button`
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background-color: #f9fafb;
  }
`;

const NotificationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const NotificationCard = styled.div<{ $isRead: boolean; $clickable?: boolean }>`
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: ${props => (props.$isRead ? 'white' : '#f9f9ff')};
  transition: all 0.2s;
  display: flex;
  gap: 16px;
  cursor: ${props => (props.$clickable ? 'pointer' : 'default')};

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    ${props =>
      props.$clickable &&
      `
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    `}
  }
`;

const NotificationIcon = styled.div<{ $color?: string }>`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: ${props => props.$color || '#e0e0e0'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const NotificationTitle = styled.h3<{ $isRead: boolean }>`
  font-size: 1rem;
  font-weight: ${props => (props.$isRead ? '500' : '600')};
  color: #333;
  margin: 0;
`;

const NotificationTime = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const NotificationMessage = styled.p`
  font-size: 0.9rem;
  color: #4b5563;
  margin: 0 0 12px 0;
  line-height: 1.5;
`;

const NotificationActions = styled.div`
  display: flex;
  gap: 8px;
`;

const NotificationAction = styled.button`
  background: none;
  border: none;
  padding: 4px 8px;
  font-size: 0.8rem;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
    color: #4b5563;
  }
`;

const EmptyState = styled.div`
  padding: 48px;
  text-align: center;
  color: #6b7280;
  border: 1px dashed #e5e7eb;
  border-radius: 8px;
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

const SkeletonBase = styled.div`
  background: #f0f0f0;
  background-image: linear-gradient(to right, #f0f0f0 0%, #e0e0e0 20%, #f0f0f0 40%, #f0f0f0 100%);
  background-size: 200px 100%;
  animation: ${shimmer} 3.5s infinite linear;
  border-radius: ${appTheme.borderRadius.md};
`;

const LoadingState = styled.div`
  padding: ${appTheme.spacing.lg};
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.md};
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
`;

const PaginationButton = styled.button<{ $active?: boolean }>`
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid ${props => (props.$active ? '#3b82f6' : '#e5e7eb')};
  background-color: ${props => (props.$active ? '#3b82f6' : 'white')};
  color: ${props => (props.$active ? 'white' : '#6b7280')};
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: ${props => (props.$active ? '#2563eb' : '#f9fafb')};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

type FilterType = 'all' | 'unread' | 'read' | 'archived';

const NotificationLayout = () => {
  const router = useRouter();
  const [currentFilter, setCurrentFilter] = useState<FilterType>('all');
  const [currentPage, setCurrentPage] = useState(1);

  // Determine filter options based on current filter
  const isRead = currentFilter === 'read' ? true : currentFilter === 'unread' ? false : undefined;
  const isArchived = currentFilter === 'archived' ? true : false;

  const {
    notifications,
    unreadCount,
    loading,
    error,
    pagination,
    fetchNotifications,
    markAsRead,
    archiveNotification,
  } = useNotifications({
    limit: 10,
    isRead,
    isArchived,
    autoRefresh: false,
  });

  // Handle filter change
  const handleFilterChange = (filter: FilterType) => {
    setCurrentFilter(filter);
    setCurrentPage(1);
    // The hook will automatically refetch with new parameters
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchNotifications(page);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchNotifications(currentPage);
  };

  // Handle notification actions
  const handleMarkAsRead = async (notificationId: number) => {
    const success = await markAsRead(notificationId);
    if (success) {
      // Dispatch custom event to notify header to refresh
      window.dispatchEvent(new CustomEvent('notificationRead'));
    }
  };

  const handleArchive = async (notificationId: number) => {
    const success = await archiveNotification(notificationId);
    if (success) {
      // Dispatch custom event to notify header to refresh
      window.dispatchEvent(new CustomEvent('notificationRead'));
    }
  };

  // Handle notification card click to navigate to task
  const handleNotificationClick = (notification: any) => {
    // Extract task ID from notification data
    let taskId: number | null = null;

    // Try to get task ID from entityId if entityType is 'task'
    if (notification.entityType === 'task' && notification.entityId) {
      taskId = notification.entityId;
    }
    // Fallback to data.taskId if available
    else if (notification.data && notification.data.taskId) {
      taskId = notification.data.taskId;
    }

    // Navigate to kanban task page if task ID is found
    if (taskId) {
      router.push(`/kanban/${taskId}`);
    }
  };

  // Generate pagination buttons
  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const buttons = [];
    const maxButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
    let endPage = Math.min(pagination.totalPages, startPage + maxButtons - 1);

    if (endPage - startPage + 1 < maxButtons) {
      startPage = Math.max(1, endPage - maxButtons + 1);
    }

    // Previous button
    buttons.push(
      <PaginationButton
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        Previous
      </PaginationButton>
    );

    // Page buttons
    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <PaginationButton key={i} $active={i === currentPage} onClick={() => handlePageChange(i)}>
          {i}
        </PaginationButton>
      );
    }

    // Next button
    buttons.push(
      <PaginationButton
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === pagination.totalPages}
      >
        Next
      </PaginationButton>
    );

    return <Pagination>{buttons}</Pagination>;
  };

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Notifications {unreadCount > 0 && `(${unreadCount} unread)`}</PageTitle>
        <ActionButton onClick={handleRefresh}>
          <RefreshCw size={16} />
          Refresh
        </ActionButton>
      </PageHeader>

      <FilterContainer>
        <FilterButton $active={currentFilter === 'all'} onClick={() => handleFilterChange('all')}>
          <Filter size={16} />
          All
        </FilterButton>
        <FilterButton
          $active={currentFilter === 'unread'}
          onClick={() => handleFilterChange('unread')}
        >
          <Bell size={16} />
          Unread
        </FilterButton>
        <FilterButton $active={currentFilter === 'read'} onClick={() => handleFilterChange('read')}>
          <Check size={16} />
          Read
        </FilterButton>
        <FilterButton
          $active={currentFilter === 'archived'}
          onClick={() => handleFilterChange('archived')}
        >
          <Archive size={16} />
          Archived
        </FilterButton>
      </FilterContainer>

      {loading ? (
        <LoadingState>
          {[...Array(5)].map((_, index) => (
            <NotificationCard key={index} $isRead={true} as="div" style={{ opacity: 0.7 }}>
              <SkeletonBase style={{ width: '48px', height: '48px', borderRadius: '50%' }} />
              <NotificationContent>
                <NotificationHeader>
                  <SkeletonBase style={{ width: '60%', height: '20px' }} />
                  <SkeletonBase style={{ width: '20%', height: '14px' }} />
                </NotificationHeader>
                <SkeletonBase style={{ width: '90%', height: '16px', marginBottom: '8px' }} />
                <SkeletonBase style={{ width: '80%', height: '16px', marginBottom: '12px' }} />
                <NotificationActions>
                  <SkeletonBase style={{ width: '100px', height: '24px' }} />
                </NotificationActions>
              </NotificationContent>
            </NotificationCard>
          ))}
        </LoadingState>
      ) : error ? (
        <EmptyState>
          <p>Error loading notifications: {error}</p>
        </EmptyState>
      ) : notifications.length === 0 ? (
        <EmptyState>
          <p>No notifications to display</p>
        </EmptyState>
      ) : (
        <NotificationsContainer>
          {notifications.map(item => {
            // Check if notification is task-related and clickable
            const isTaskRelated =
              item.notification.entityType === 'task' ||
              (item.notification.data && item.notification.data.taskId);

            return (
              <NotificationCard
                key={item.id}
                $isRead={item.isRead}
                $clickable={isTaskRelated}
                onClick={() => isTaskRelated && handleNotificationClick(item.notification)}
              >
                <NotificationIcon $color={item.notification.type.color}>
                  <Bell size={20} />
                </NotificationIcon>
                <NotificationContent>
                  <NotificationHeader>
                    <NotificationTitle $isRead={item.isRead}>
                      {item.notification.title}
                    </NotificationTitle>
                    <NotificationTime>
                      <Clock size={14} />
                      {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
                    </NotificationTime>
                  </NotificationHeader>
                  <NotificationMessage>{item.notification.content}</NotificationMessage>
                  <NotificationActions>
                    {!item.isRead && (
                      <NotificationAction
                        onClick={e => {
                          e.stopPropagation(); // Prevent card click when clicking action button
                          handleMarkAsRead(item.notification.id);
                        }}
                      >
                        <Check size={14} />
                        Mark as read
                      </NotificationAction>
                    )}
                    {!item.isArchived && (
                      <NotificationAction
                        onClick={e => {
                          e.stopPropagation(); // Prevent card click when clicking action button
                          handleArchive(item.notification.id);
                        }}
                      >
                        <Archive size={14} />
                        Archive
                      </NotificationAction>
                    )}
                  </NotificationActions>
                </NotificationContent>
              </NotificationCard>
            );
          })}
        </NotificationsContainer>
      )}

      {renderPagination()}
    </PageContainer>
  );
};

export default NotificationLayout;
