'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useParams, useRouter } from 'next/navigation';
import SettingsLayout from '../../../components/SettingsLayout';
import { Plus, ArrowLeft, MoreHorizontal, X, Loader2, Users } from 'lucide-react';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-hot-toast';
import { appTheme as settingsTheme, appStyles as commonStyles } from '@/app/theme';

const DepartmentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: ${settingsTheme.spacing.xl};
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  box-shadow: ${settingsTheme.shadows.md};

  @media (min-width: ${settingsTheme.breakpoints['2xl']}) {
    max-width: 1400px;
  }

  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.base};
    gap: ${settingsTheme.spacing.base};
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const BackButton = styled.button`
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
  color: ${settingsTheme.colors.text.tertiary};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  cursor: pointer;
  padding: ${settingsTheme.spacing.sm} 0;
  margin-bottom: ${settingsTheme.spacing.lg};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.primary};
  }
`;

const Title = styled.h2`
  ${commonStyles.sectionTitle}
`;

const AddButton = styled.button`
  ${commonStyles.button.primary}
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.sm};
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: ${settingsTheme.shadows.sm};
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
`;

const THead = styled.thead`
  background-color: ${settingsTheme.colors.background.light};
`;

const TH = styled.th`
  text-align: left;
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const TR = styled.tr`
  transition: ${settingsTheme.transitions.default};
  &:hover {
    background-color: ${settingsTheme.colors.background.light};
  }
`;

const TD = styled.td`
  padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.base};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  color: ${settingsTheme.colors.text.secondary};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    background-color: ${settingsTheme.colors.background.lighter};
    color: ${settingsTheme.colors.primary};
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: ${settingsTheme.colors.background.main};
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: ${settingsTheme.spacing['2xl']};
  width: 100%;
  max-width: 500px;
  box-shadow: ${settingsTheme.shadows.lg};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.xl};
`;

const ModalTitle = styled.h3`
  font-size: ${settingsTheme.typography.fontSizes.xl};
  font-weight: ${settingsTheme.typography.fontWeights.semibold};
  color: ${settingsTheme.colors.text.primary};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: ${settingsTheme.colors.text.tertiary};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${settingsTheme.spacing.xs};
  border-radius: ${settingsTheme.borderRadius.sm};
  transition: ${settingsTheme.transitions.default};

  &:hover {
    color: ${settingsTheme.colors.text.secondary};
    background-color: ${settingsTheme.colors.background.lighter};
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.base};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.sm};
`;

const Label = styled.label`
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${settingsTheme.typography.fontWeights.medium};
  color: ${settingsTheme.colors.text.secondary};
`;

const Input = styled.input`
  ${commonStyles.input}
`;

const TextArea = styled.textarea`
  ${commonStyles.input}
  font-size: 0.875rem;
  width: 100%;
  min-height: 100px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: ${settingsTheme.spacing.md};
  margin-top: ${settingsTheme.spacing.xl};
`;

const CancelButton = styled.button`
  ${commonStyles.button.secondary}
`;

const SubmitButton = styled.button`
  ${commonStyles.button.primary}
`;

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  padding: ${settingsTheme.spacing.xs} ${settingsTheme.spacing.sm};
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e0f2fe;
  color: #0369a1;
`;

const OrganizationInfo = styled.div`
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 1.5rem;
`;

const OrgName = styled.h3`
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const OrgDescription = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
`;

const LoadingContainer = styled.div`
  width: 100%;
  min-height: 200px;
`;

const SkeletonContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${settingsTheme.spacing.xl};
  width: 100%;
`;

const SkeletonBackButton = styled.div`
  height: 20px;
  width: 120px;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.lg};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonOrgInfo = styled.div`
  padding: 1rem;
  background-color: ${settingsTheme.colors.background.lighter};
  border-radius: 8px;
  margin-bottom: 1.5rem;
  height: 80px;
`;

const SkeletonOrgTitle = styled.div`
  height: 24px;
  width: 40%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin-bottom: ${settingsTheme.spacing.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonOrgDescription = styled.div`
  height: 16px;
  width: 70%;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${settingsTheme.spacing.lg};
`;

const SkeletonTitle = styled.div`
  height: 28px;
  width: 150px;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonButton = styled.div`
  height: 38px;
  width: 140px;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.md};
  animation: pulse 1.5s ease-in-out infinite;
`;

const SkeletonTable = styled.div`
  width: 100%;
  border-radius: ${settingsTheme.borderRadius.md};
  overflow: hidden;
  border: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonTableHeader = styled.div`
  height: 48px;
  width: 100%;
  background-color: ${settingsTheme.colors.background.lighter};
  display: flex;
  padding: 0 ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonTableHeaderCell = styled.div`
  height: 16px;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin: auto 0;
  animation: pulse 1.5s ease-in-out infinite;
  &:nth-child(1) {
    width: 40%;
  }
  &:nth-child(2) {
    width: 20%;
  }
  &:nth-child(3) {
    width: 15%;
  }
`;

const SkeletonTableRow = styled.div`
  height: 56px;
  width: 100%;
  display: flex;
  padding: 0 ${settingsTheme.spacing.base};
  border-bottom: 1px solid ${settingsTheme.colors.border};
`;

const SkeletonTableCell = styled.div`
  height: 16px;
  background-color: ${settingsTheme.colors.background.light};
  border-radius: ${settingsTheme.borderRadius.sm};
  margin: auto 0;
  animation: pulse 1.5s ease-in-out infinite;
  &:nth-child(1) {
    width: 40%;
  }
  &:nth-child(2) {
    width: 20%;
  }
  &:nth-child(3) {
    width: 15%;
  }
`;

const GlobalStyle = styled.div`
  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 0.6;
    }
  }
`;

const ErrorContainer = styled.div`
  padding: ${settingsTheme.spacing.md};
  background-color: ${settingsTheme.colors.error.light};
  border: 1px solid ${settingsTheme.colors.error.main};
  border-radius: ${settingsTheme.borderRadius.md};
  color: ${settingsTheme.colors.error.main};
  margin-bottom: ${settingsTheme.spacing.base};
`;

// Define types for our data
interface Organization {
  id: number;
  name: string;
  description: string;
  ownerUserId: number;
}

interface Department {
  id: number;
  name: string;
  description: string;
  organizationId: number;
  createdAt: string;
  updatedAt: string;
  _count?: {
    members: number;
  };
}

// Modal Props Interfaces
interface CreateDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string;
  onSuccess: () => void;
}

interface EditDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  department: Department | null;
  onSuccess: () => void;
}

interface DeleteDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  department: Department | null;
  onSuccess: () => void;
}

function CreateDepartmentModal({
  isOpen,
  onClose,
  organizationId,
  onSuccess,
}: CreateDepartmentModalProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getToken } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const token = await getToken();
      const response = await fetch('/api/v1/department', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name,
          description,
          organizationId: Number(organizationId),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create department');
      }

      const data = await response.json();
      toast.success('Department created successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating department:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create department');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Create New Department</ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="name">Department Name *</Label>
            <Input
              id="name"
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter department name"
              required
              autoFocus
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="description">Description</Label>
            <TextArea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Enter department description"
            />
          </FormGroup>
          <ButtonGroup>
            <CancelButton type="button" onClick={onClose}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={isSubmitting || !name.trim()}>
              {isSubmitting ? 'Creating...' : 'Create Department'}
            </SubmitButton>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
}

// Edit Department Modal Component
function EditDepartmentModal({ isOpen, onClose, department, onSuccess }: EditDepartmentModalProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { getToken } = useAuth();

  // Initialize form with department data when modal opens or department changes
  useEffect(() => {
    if (department) {
      setName(department.name);
      setDescription(department.description || '');
    }
  }, [department]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!department) return;

    setIsSubmitting(true);

    try {
      const token = await getToken();
      const response = await fetch('/api/v1/department', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          id: department.id,
          name,
          description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update department');
      }

      const data = await response.json();
      toast.success('Department updated successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error updating department:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update department');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !department) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Edit Department</ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>
        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="name">Department Name *</Label>
            <Input
              id="name"
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="Enter department name"
              required
              autoFocus
            />
          </FormGroup>
          <FormGroup>
            <Label htmlFor="description">Description</Label>
            <TextArea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Enter department description"
            />
          </FormGroup>
          <ButtonGroup>
            <CancelButton type="button" onClick={onClose}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={isSubmitting || !name.trim()}>
              {isSubmitting ? 'Updating...' : 'Update Department'}
            </SubmitButton>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </ModalOverlay>
  );
}

// Delete Department Modal Component
function DeleteDepartmentModal({
  isOpen,
  onClose,
  department,
  onSuccess,
}: DeleteDepartmentModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [hasMembers, setHasMembers] = useState(false);
  const [isCheckingMembers, setIsCheckingMembers] = useState(false);
  const { getToken } = useAuth();

  // Check if department has members when modal opens
  useEffect(() => {
    const checkDepartmentMembers = async () => {
      if (!department || !isOpen) return;

      setIsCheckingMembers(true);
      try {
        const token = await getToken();
        // Get the detailed department info including members count
        const response = await fetch(`/api/v1/department?id=${department.id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to check department members');
        }

        const data = await response.json();
        const memberCount = data.department?._count?.members || 0;
        setHasMembers(memberCount > 0);
      } catch (error) {
        console.error('Error checking department members:', error);
        toast.error('Failed to check department members');
      } finally {
        setIsCheckingMembers(false);
      }
    };

    checkDepartmentMembers();
  }, [department, isOpen, getToken]);

  const handleDelete = async () => {
    if (!department) return;

    // Double-check for members before deletion
    if (hasMembers) {
      toast.error(
        'Cannot delete department with existing members. Please remove all members first.'
      );
      return;
    }

    setIsDeleting(true);

    try {
      const token = await getToken();
      const response = await fetch(`/api/v1/department?id=${department.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete department');
      }

      toast.success('Department deleted successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error deleting department:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete department');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen || !department) return null;

  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Delete Department</ModalTitle>
          <CloseButton onClick={onClose} aria-label="Close modal">
            <X size={18} />
          </CloseButton>
        </ModalHeader>
        <div style={{ marginBottom: settingsTheme.spacing.xl }}>
          {isCheckingMembers ? (
            <p>Checking department members...</p>
          ) : hasMembers ? (
            <div>
              <p
                style={{
                  marginBottom: settingsTheme.spacing.base,
                  color: settingsTheme.colors.error.main,
                }}
              >
                <strong>Cannot delete department with existing members.</strong>
              </p>
              <p>
                This department has members assigned to it. Please remove all members from this
                department before deleting it.
              </p>
            </div>
          ) : (
            <>
              <p style={{ marginBottom: settingsTheme.spacing.base }}>
                Are you sure you want to delete the department <strong>{department.name}</strong>?
              </p>
              <p
                style={{
                  color: settingsTheme.colors.error.main,
                  fontSize: settingsTheme.typography.fontSizes.sm,
                }}
              >
                This action cannot be undone. All department data will be permanently removed.
              </p>
            </>
          )}
        </div>
        <ButtonGroup>
          <CancelButton type="button" onClick={onClose}>
            {hasMembers ? 'Close' : 'Cancel'}
          </CancelButton>
          {!hasMembers && !isCheckingMembers && (
            <SubmitButton
              type="button"
              onClick={handleDelete}
              disabled={isDeleting}
              style={{
                backgroundColor: settingsTheme.colors.error.main,
                borderColor: settingsTheme.colors.error.main,
              }}
            >
              {isDeleting ? 'Deleting...' : 'Delete Department'}
            </SubmitButton>
          )}
        </ButtonGroup>
      </ModalContent>
    </ModalOverlay>
  );
}

export default function DepartmentPage() {
  const params = useParams();
  const router = useRouter();
  const organizationId = params.id as string;
  const { getToken } = useAuth();

  const [organization, setOrganization] = useState<Organization | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [userRole, setUserRole] = useState<number | null>(null);
  const [isOwner, setIsOwner] = useState(false);

  // Fetch user role and check ownership
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const token = await getToken();
        const response = await fetch('/api/v1/me', {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setUserRole(data.user?.role?.id || null);
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
      }
    };

    fetchUserRole();
  }, [getToken]);

  // Check if user is owner of the organization
  useEffect(() => {
    if (organization && userRole) {
      const fetchUserData = async () => {
        try {
          const token = await getToken();
          const response = await fetch('/api/v1/me', {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            setIsOwner(organization.ownerUserId === data.user?.id);
          }
        } catch (error) {
          console.error('Error checking ownership:', error);
        }
      };

      fetchUserData();
    }
  }, [organization, userRole, getToken]);

  // Fetch organization details
  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const token = await getToken();
        const response = await fetch(`/api/v1/organization?id=${organizationId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch organization details');
        }

        const data = await response.json();
        // The API returns an array of organizations, so we need to find the one with the matching ID
        if (data.organizations) {
          const org = data.organizations.find((org: any) => org.id === Number(organizationId));
          setOrganization(org || null);
        } else if (data.organization) {
          // If the API returns a single organization object
          setOrganization(data.organization);
        } else {
          setOrganization(null);
        }
      } catch (err) {
        setError('Error fetching organization details');
        console.error(err);
      }
    };

    if (organizationId) {
      fetchOrganization();
    }
  }, [organizationId, getToken]);

  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      setIsLoading(true);
      try {
        const token = await getToken();
        const response = await fetch(`/api/v1/department?organizationId=${organizationId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch departments');
        }

        const data = await response.json();
        setDepartments(data.departments || []);
      } catch (err) {
        setError('Error fetching departments');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (organizationId) {
      fetchDepartments();
    }
  }, [organizationId, getToken]);

  const handleBack = () => {
    router.push('/settings/organizations');
  };

  // Use all departments
  const filteredDepartments = departments;

  // Handle adding a new department
  const handleAddDepartment = () => {
    setIsCreateModalOpen(true);
  };

  // Handle editing a department
  const handleEditDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setIsEditModalOpen(true);
  };

  // Handle deleting a department
  const handleDeleteDepartment = (department: Department) => {
    setSelectedDepartment(department);
    setIsDeleteModalOpen(true);
  };

  if (isLoading) {
    return (
      <SettingsLayout>
        <GlobalStyle />
        <DepartmentContainer>
          <SkeletonContainer>
            <SkeletonBackButton />

            <SkeletonOrgInfo>
              <SkeletonOrgTitle />
              <SkeletonOrgDescription />
            </SkeletonOrgInfo>

            <SkeletonHeader>
              <SkeletonTitle />
              <SkeletonButton />
            </SkeletonHeader>

            <SkeletonTable>
              <SkeletonTableHeader>
                <SkeletonTableHeaderCell style={{ marginRight: '20px' }} />
                <SkeletonTableHeaderCell style={{ marginRight: '20px' }} />
                <SkeletonTableHeaderCell />
              </SkeletonTableHeader>
              {[...Array(5)].map((_, index) => (
                <SkeletonTableRow key={index}>
                  <SkeletonTableCell style={{ marginRight: '20px' }} />
                  <SkeletonTableCell style={{ marginRight: '20px' }} />
                  <SkeletonTableCell />
                </SkeletonTableRow>
              ))}
            </SkeletonTable>
          </SkeletonContainer>
        </DepartmentContainer>
      </SettingsLayout>
    );
  }

  if (error) {
    return (
      <SettingsLayout>
        <div>{error}</div>
      </SettingsLayout>
    );
  }

  if (!organization) {
    return (
      <SettingsLayout>
        <div>Organization not found</div>
      </SettingsLayout>
    );
  }

  return (
    <SettingsLayout>
      <DepartmentContainer>
        <BackButton onClick={handleBack}>
          <ArrowLeft size={16} />
          Back to Organizations
        </BackButton>

        {organization && (
          <OrganizationInfo>
            <OrgName>{organization.name}</OrgName>
            <OrgDescription>{organization.description || 'No description provided'}</OrgDescription>
          </OrganizationInfo>
        )}

        <Header>
          <Title>Departments</Title>
          <div style={{ display: 'flex', gap: settingsTheme.spacing.md }}>
            <AddButton onClick={handleAddDepartment}>
              <Plus size={16} />
              Add Department
            </AddButton>
          </div>
        </Header>

        {/* Department Modals */}
        <CreateDepartmentModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          organizationId={organizationId}
          onSuccess={() => {
            // Refresh departments list after creation
            const fetchDepartments = async () => {
              setIsLoading(true);
              try {
                const token = await getToken();
                const response = await fetch(
                  `/api/v1/department?organizationId=${organizationId}`,
                  {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }
                );

                if (!response.ok) {
                  throw new Error('Failed to fetch departments');
                }

                const data = await response.json();
                setDepartments(data.departments || []);
              } catch (err) {
                setError('Error fetching departments');
                console.error(err);
              } finally {
                setIsLoading(false);
              }
            };

            fetchDepartments();
          }}
        />

        <EditDepartmentModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          department={selectedDepartment}
          onSuccess={() => {
            // Refresh departments list after update
            const fetchDepartments = async () => {
              setIsLoading(true);
              try {
                const token = await getToken();
                const response = await fetch(
                  `/api/v1/department?organizationId=${organizationId}`,
                  {
                    headers: {
                      Authorization: `Bearer ${token}`,
                    },
                  }
                );

                if (!response.ok) {
                  throw new Error('Failed to fetch departments');
                }

                const data = await response.json();
                setDepartments(data.departments || []);
              } catch (err) {
                setError('Error fetching departments');
                console.error(err);
              } finally {
                setIsLoading(false);
              }
            };

            fetchDepartments();
          }}
        />

        <DeleteDepartmentModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          department={selectedDepartment}
          onSuccess={() => {
            // Remove the deleted department from the state
            if (selectedDepartment) {
              setDepartments(departments.filter(dept => dept.id !== selectedDepartment.id));
            }
          }}
        />

        <Table>
          <THead>
            <tr>
              <TH>Name</TH>
              <TH>Members</TH>
              <TH>Actions</TH>
            </tr>
          </THead>
          <tbody>
            {filteredDepartments.length > 0 ? (
              filteredDepartments.map(dept => (
                <TR key={dept.id}>
                  <TD>{dept.name}</TD>
                  <TD>{dept._count?.members || 0} members</TD>
                  <TD>
                    <div style={{ display: 'flex', gap: '0.5rem' }}>
                      <ActionButton
                        onClick={() => handleEditDepartment(dept)}
                        aria-label="Edit Department"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <title>Edit</title>
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                      </ActionButton>
                      <ActionButton
                        onClick={() => handleDeleteDepartment(dept)}
                        aria-label="Delete Department"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <title>Delete</title>
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                      </ActionButton>
                    </div>
                  </TD>
                </TR>
              ))
            ) : (
              <TR>
                <TD colSpan={3} style={{ textAlign: 'center' }}>
                  No departments found
                </TD>
              </TR>
            )}
          </tbody>
        </Table>
      </DepartmentContainer>
    </SettingsLayout>
  );
}
