'use client';

import React from 'react';
import styled from 'styled-components';
import { appTheme as settingsTheme } from '@/app/theme';

const TabContainer = styled.div`
  display: flex;
  border-bottom: 1px solid ${settingsTheme.colors.border};
  margin-bottom: ${settingsTheme.spacing.base};
  width: 100%;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    margin-bottom: ${settingsTheme.spacing.md};
    padding-bottom: ${settingsTheme.spacing.xs};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    margin-bottom: ${settingsTheme.spacing.sm};
    padding-bottom: ${settingsTheme.spacing.xs};
    gap: ${settingsTheme.spacing.xs};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    margin-bottom: ${settingsTheme.spacing.xs};
    border-bottom: 2px solid ${settingsTheme.colors.border};
  }
`;

interface TabItemProps {
  $active: boolean;
}

const TabItem = styled.button<TabItemProps>`
  padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
  font-size: ${settingsTheme.typography.fontSizes.sm};
  font-weight: ${props =>
    props.$active
      ? settingsTheme.typography.fontWeights.bold
      : settingsTheme.typography.fontWeights.medium};
  color: ${props =>
    props.$active ? settingsTheme.colors.primary : settingsTheme.colors.text.secondary};
  background-color: transparent;
  border: none;
  border-bottom: 3px solid
    ${props => (props.$active ? settingsTheme.colors.primary : 'transparent')};
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  flex-shrink: 0;

  &:hover {
    color: ${settingsTheme.colors.primary};
    background-color: ${settingsTheme.colors.background.light};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${settingsTheme.colors.primaryLight};
    border-radius: ${settingsTheme.borderRadius.sm};
  }

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.sm};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    font-size: ${settingsTheme.typography.fontSizes.base};
    border-bottom-width: 2px;
    min-height: 44px; /* Touch target size */
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    font-size: ${settingsTheme.typography.fontSizes.sm};
    min-height: 40px;
  }
`;

export interface Tab {
  id: string;
  label: string;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export default function TabNavigation({ tabs, activeTab, onTabChange }: TabNavigationProps) {
  return (
    <TabContainer>
      {tabs.map(tab => (
        <TabItem key={tab.id} $active={activeTab === tab.id} onClick={() => onTabChange(tab.id)}>
          {tab.label}
        </TabItem>
      ))}
    </TabContainer>
  );
}
