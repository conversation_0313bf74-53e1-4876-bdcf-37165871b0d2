'use client';

import React, { ReactNode } from 'react';
import styled from 'styled-components';
import { appTheme as settingsTheme } from '@/app/theme';

const SettingsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: ${settingsTheme.spacing.xl};
  gap: ${settingsTheme.spacing.xl};
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: ${settingsTheme.borderRadius.lg};

  /* Desktop (1280px+) */
  @media (min-width: ${settingsTheme.breakpoints.xl}) {
    padding: ${settingsTheme.spacing['2xl']};
    gap: ${settingsTheme.spacing['2xl']};
  }

  /* Tablet (768px - 1023px) */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.lg};
    gap: ${settingsTheme.spacing.lg};
    margin: 0 ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile (up to 767px) */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    gap: ${settingsTheme.spacing.lg};
    margin: 0 ${settingsTheme.spacing.sm};
    border-radius: ${settingsTheme.borderRadius.sm};
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Small mobile (up to 480px) */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm};
    gap: ${settingsTheme.spacing.md};
    margin: 0 ${settingsTheme.spacing.xs};
    border: none;
    border-radius: 0;
    background: transparent;
  }
`;

const SettingsHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${settingsTheme.spacing.lg};
  border-radius: ${settingsTheme.borderRadius.lg};
  background-color: ${settingsTheme.colors.background.main};
  box-shadow: ${settingsTheme.shadows.sm};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md} ${settingsTheme.spacing.lg};
    border-radius: ${settingsTheme.borderRadius.md};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    padding: ${settingsTheme.spacing.md};
    border-radius: ${settingsTheme.borderRadius.sm};
    box-shadow: ${settingsTheme.shadows.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    padding: ${settingsTheme.spacing.sm} ${settingsTheme.spacing.md};
    box-shadow: none;
    border: 1px solid ${settingsTheme.colors.border};
  }
`;

const SettingsTitle = styled.h1`
  font-size: ${settingsTheme.typography.fontSizes['3xl']};
  font-weight: ${settingsTheme.typography.fontWeights.bold};
  color: ${settingsTheme.colors.text.primary};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${settingsTheme.spacing.md};

  /* Tablet */
  @media (max-width: ${settingsTheme.breakpoints.lg}) and (min-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes['2xl']};
    gap: ${settingsTheme.spacing.sm};
  }

  /* Mobile */
  @media (max-width: ${settingsTheme.breakpoints.md}) {
    font-size: ${settingsTheme.typography.fontSizes.xl};
    gap: ${settingsTheme.spacing.sm};
  }

  /* Small mobile */
  @media (max-width: ${settingsTheme.breakpoints.sm}) {
    font-size: ${settingsTheme.typography.fontSizes.lg};
    gap: ${settingsTheme.spacing.xs};
  }
`;

const SettingsContent = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const ContentArea = styled.div`
  border-radius: ${settingsTheme.borderRadius.lg};
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  overflow: hidden;
`;

interface SettingsLayoutProps {
  children: ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <SettingsContainer>
      <SettingsHeader>
        <SettingsTitle>Settings</SettingsTitle>
      </SettingsHeader>
      <SettingsContent>
        <ContentArea>{children}</ContentArea>
      </SettingsContent>
    </SettingsContainer>
  );
}
