'use client';

import React, { useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { X, Loader2 } from 'lucide-react';
import { appTheme } from '@/app/theme';

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

const SpinningLoader = styled(Loader2)`
  animation: ${spin} 1s linear infinite;
`;

interface NextActionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  actions: string[];
  onGenerate: (selectedActions: string[]) => Promise<void>;
  isGenerating?: boolean;
}

const fadeIn = keyframes`
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
`;

const slideUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
`;

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => props.$isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${appTheme.spacing.md};
  animation: ${fadeIn} 0.2s ease-out;
`;

const ModalContainer = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: ${slideUp} 0.3s ease-out;
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg} ${appTheme.spacing.lg} ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};

  &::before {
    content: '⚡';
    font-size: 18px;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.md};
  color: ${appTheme.colors.text.light};
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
  overflow-y: auto;
  flex: 1;
`;

const SelectionCounter = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: ${appTheme.borderRadius.md};
  margin-bottom: ${appTheme.spacing.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.primary};
  font-weight: 500;
`;

const ActionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.sm};
  max-height: 400px;
  overflow-y: auto;
  padding-right: ${appTheme.spacing.xs};

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${appTheme.colors.background.light};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.text.light};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.secondary};
  }
`;

const ActionItem = styled.label`
  display: flex;
  align-items: flex-start;
  gap: ${appTheme.spacing.md};
  cursor: pointer;
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.lg};
  border: 1px solid ${appTheme.colors.border};
  transition: all 0.2s ease;
  background: ${appTheme.colors.background.main};

  &:hover {
    background: ${appTheme.colors.background.light};
    border-color: ${appTheme.colors.primary};
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:has(input:checked) {
    background: rgba(59, 130, 246, 0.05);
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 1px ${appTheme.colors.primary}20;
  }
`;

const CheckboxContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-top: 2px;
`;

const ActionCheckbox = styled.input`
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
  margin: 0;
`;

const CustomCheckbox = styled.div<{ $checked: boolean }>`
  width: 20px;
  height: 20px;
  border: 2px solid ${props => props.$checked ? appTheme.colors.primary : appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  background: ${props => props.$checked ? appTheme.colors.primary : 'transparent'};
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;

  &::after {
    content: '';
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg) scale(${props => props.$checked ? 1 : 0});
    transition: transform 0.2s ease;
    margin-top: -2px;
  }

  ${ActionItem}:hover & {
    border-color: ${appTheme.colors.primary};
    ${props => !props.$checked && `
      background: rgba(59, 130, 246, 0.1);
    `}
  }
`;

const ActionText = styled.span`
  flex: 1;
  font-size: ${appTheme.typography.fontSizes.sm};
  line-height: 1.5;
  color: ${appTheme.colors.text.primary};
  font-weight: 400;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg} ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  background: rgba(249, 250, 251, 0.5);
`;

const FooterInfo = styled.div`
  font-size: ${appTheme.typography.fontSizes.xs};
  color: ${appTheme.colors.text.secondary};
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${appTheme.spacing.sm};
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  position: relative;
  overflow: hidden;

  ${props => props.$variant === 'primary' ? `
    background: linear-gradient(135deg, ${appTheme.colors.primary} 0%, #4F46E5 100%);
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      background: ${appTheme.colors.text.light};
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  ` : `
    background: transparent;
    color: ${appTheme.colors.text.primary};
    border: 1px solid ${appTheme.colors.border};

    &:hover:not(:disabled) {
      background: ${appTheme.colors.background.light};
      border-color: ${appTheme.colors.primary};
    }
  `}
`;

const NextActionsModal: React.FC<NextActionsModalProps> = ({
  isOpen,
  onClose,
  actions,
  onGenerate,
  isGenerating = false
}) => {
  const [selectedActions, setSelectedActions] = useState<string[]>([]);

  const handleActionToggle = (action: string) => {
    setSelectedActions(prev => 
      prev.includes(action)
        ? prev.filter(a => a !== action)
        : [...prev, action]
    );
  };

  const handleGenerate = async () => {
    if (selectedActions.length === 0) return;
    
    try {
      await onGenerate(selectedActions);
      setSelectedActions([]);
      onClose();
    } catch (error) {
      console.error('Error generating response:', error);
    }
  };

  const handleClose = () => {
    if (!isGenerating) {
      setSelectedActions([]);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay $isOpen={isOpen} onClick={handleClose}>
      <ModalContainer onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Next Actions</ModalTitle>
          <CloseButton onClick={handleClose} disabled={isGenerating}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>
        
        <ModalBody>
          {selectedActions.length > 0 && (
            <SelectionCounter>
              <span>{selectedActions.length} action{selectedActions.length !== 1 ? 's' : ''} selected</span>
              <button
                onClick={() => setSelectedActions([])}
                style={{
                  background: 'none',
                  border: 'none',
                  color: appTheme.colors.primary,
                  cursor: 'pointer',
                  fontSize: appTheme.typography.fontSizes.xs,
                  textDecoration: 'underline'
                }}
                disabled={isGenerating}
              >
                Clear all
              </button>
            </SelectionCounter>
          )}

          <ActionsList>
            {actions.map((action, index) => (
              <ActionItem key={index}>
                <CheckboxContainer>
                  <ActionCheckbox
                    type="checkbox"
                    checked={selectedActions.includes(action)}
                    onChange={() => handleActionToggle(action)}
                    disabled={isGenerating}
                  />
                  <CustomCheckbox $checked={selectedActions.includes(action)} />
                </CheckboxContainer>
                <ActionText>{action}</ActionText>
              </ActionItem>
            ))}
          </ActionsList>
        </ModalBody>
        
        <ModalFooter>
          <FooterInfo>
            {selectedActions.length === 0
              ? 'Select at least one action to continue'
              : `${selectedActions.length} of ${actions.length} actions selected`
            }
          </FooterInfo>
          <ButtonGroup>
            <Button $variant="secondary" onClick={handleClose} disabled={isGenerating}>
              Cancel
            </Button>
            <Button
              $variant="primary"
              onClick={handleGenerate}
              disabled={selectedActions.length === 0 || isGenerating}
            >
              {isGenerating && <SpinningLoader size={16} />}
              {isGenerating ? 'Generating...' : 'Generate'}
            </Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContainer>
    </ModalOverlay>
  );
};

export default NextActionsModal;
