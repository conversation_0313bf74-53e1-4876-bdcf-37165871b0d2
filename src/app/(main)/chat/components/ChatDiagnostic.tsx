'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';

const DiagnosticContainer = styled.div`
  padding: 20px;
  background: ${appTheme.colors.background};
  border-radius: 8px;
  margin: 20px;
  border: 1px solid ${appTheme.colors.border};
`;

const Title = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  margin-right: 12px;
  margin-bottom: 12px;
  
  ${props => props.$variant === 'primary' ? `
    background: ${appTheme.colors.primary};
    color: white;
    
    &:hover {
      background: ${appTheme.colors.primaryHover};
    }
  ` : `
    background: ${appTheme.colors.secondary};
    color: ${appTheme.colors.text.primary};
    border: 1px solid ${appTheme.colors.border};
    
    &:hover {
      background: ${appTheme.colors.secondaryHover};
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ResultContainer = styled.div`
  margin-top: 16px;
  padding: 16px;
  background: ${appTheme.colors.background};
  border-radius: 6px;
  border: 1px solid ${appTheme.colors.border};
`;

const OrganizationItem = styled.div`
  margin-bottom: 16px;
  padding: 12px;
  background: ${appTheme.colors.background};
  border-radius: 6px;
  border: 1px solid ${appTheme.colors.border};
`;

const StatusBadge = styled.span<{ $status: 'good' | 'warning' | 'error' }>`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 8px;
  
  ${props => {
    switch (props.$status) {
      case 'good':
        return `
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        `;
      case 'warning':
        return `
          background: #fff3cd;
          color: #856404;
          border: 1px solid #ffeaa7;
        `;
      case 'error':
        return `
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        `;
    }
  }}
`;

const ErrorList = styled.ul`
  color: ${appTheme.colors.error};
  margin-top: 12px;
  padding-left: 20px;
`;

interface ChatDiagnosticProps {
  onRefreshChats?: () => void;
}

export default function ChatDiagnostic({ onRefreshChats }: ChatDiagnosticProps) {
  const [loading, setLoading] = useState(false);
  const [repairing, setRepairing] = useState(false);
  const [diagnosis, setDiagnosis] = useState<any>(null);
  const [repairResult, setRepairResult] = useState<any>(null);

  const runDiagnosis = async () => {
    setLoading(true);
    setDiagnosis(null);
    setRepairResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/v1/chat-membership-repair', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to run diagnosis');
      }

      const data = await response.json();
      setDiagnosis(data.diagnosis);
    } catch (error) {
      console.error('Diagnosis failed:', error);
      alert('Failed to run diagnosis. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const repairMembership = async () => {
    setRepairing(true);
    setRepairResult(null);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/v1/chat-membership-repair', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'repair' }),
      });

      if (!response.ok) {
        throw new Error('Failed to repair membership');
      }

      const data = await response.json();
      setRepairResult(data);
      
      // Refresh the diagnosis after repair
      setTimeout(() => {
        runDiagnosis();
        onRefreshChats?.();
      }, 1000);
    } catch (error) {
      console.error('Repair failed:', error);
      alert('Failed to repair membership. Please try again.');
    } finally {
      setRepairing(false);
    }
  };

  const getStatusBadge = (orgChat: any, deptChat?: any) => {
    if (deptChat) {
      if (!deptChat.exists) return <StatusBadge $status="warning">No Chat</StatusBadge>;
      if (!deptChat.isMember) return <StatusBadge $status="error">Not Member</StatusBadge>;
      return <StatusBadge $status="good">OK</StatusBadge>;
    }
    
    if (!orgChat.exists) return <StatusBadge $status="warning">No Chat</StatusBadge>;
    if (!orgChat.isMember) return <StatusBadge $status="error">Not Member</StatusBadge>;
    return <StatusBadge $status="good">OK</StatusBadge>;
  };

  return (
    <DiagnosticContainer>
      <Title>Chat Membership Diagnostic</Title>
      <p>Use this tool to diagnose and fix chat access issues.</p>
      
      <div>
        <Button $variant="primary" onClick={runDiagnosis} disabled={loading}>
          {loading ? 'Running Diagnosis...' : 'Run Diagnosis'}
        </Button>
        
        {diagnosis && (
          <Button $variant="secondary" onClick={repairMembership} disabled={repairing}>
            {repairing ? 'Repairing...' : 'Repair Membership'}
          </Button>
        )}
      </div>

      {diagnosis && (
        <ResultContainer>
          <h3>Diagnosis Results</h3>
          <p><strong>User ID:</strong> {diagnosis.userId}</p>
          <p><strong>Role:</strong> {diagnosis.userRole.isOwner ? 'Owner' : diagnosis.userRole.isAdmin ? 'Admin' : 'Member'}</p>
          
          <h4>Organizations:</h4>
          {diagnosis.organizations.map((org: any) => (
            <OrganizationItem key={org.id}>
              <div>
                <strong>{org.name}</strong> (ID: {org.id})
                {org.isOwner && <StatusBadge $status="good">Owner</StatusBadge>}
              </div>
              
              <div style={{ marginTop: '8px' }}>
                <strong>Organization Chat:</strong>
                {getStatusBadge(org.organizationChat)}
                {org.organizationChat.chatId && ` (Chat ID: ${org.organizationChat.chatId})`}
              </div>
              
              {org.departments.length > 0 && (
                <div style={{ marginTop: '12px' }}>
                  <strong>Department Chats:</strong>
                  {org.departments.map((dept: any) => (
                    <div key={dept.id} style={{ marginLeft: '16px', marginTop: '4px' }}>
                      {dept.name}: {getStatusBadge(null, dept.departmentChat)}
                      {dept.departmentChat.chatId && ` (Chat ID: ${dept.departmentChat.chatId})`}
                    </div>
                  ))}
                </div>
              )}
            </OrganizationItem>
          ))}
        </ResultContainer>
      )}

      {repairResult && (
        <ResultContainer>
          <h3>Repair Results</h3>
          <p><strong>Message:</strong> {repairResult.message}</p>
          <p><strong>Organization Chats Fixed:</strong> {repairResult.organizationChats}</p>
          <p><strong>Department Chats Fixed:</strong> {repairResult.departmentChats}</p>
          
          {repairResult.errors && repairResult.errors.length > 0 && (
            <div>
              <strong>Errors:</strong>
              <ErrorList>
                {repairResult.errors.map((error: string, index: number) => (
                  <li key={index}>{error}</li>
                ))}
              </ErrorList>
            </div>
          )}
        </ResultContainer>
      )}
    </DiagnosticContainer>
  );
}
