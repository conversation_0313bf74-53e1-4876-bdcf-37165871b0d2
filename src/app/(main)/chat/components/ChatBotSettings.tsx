'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { Bo<PERSON>, Clock, Settings, Loader2 } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface ChatBotSettingsProps {
  isBot: boolean;
  botDuration: number;
  onSettingsChange: (settings: { isBot: boolean; botDuration: number }) => Promise<void>;
  loading?: boolean;
  disabled?: boolean;
  compact?: boolean;
}

const SettingsContainer = styled.div<{ $compact?: boolean }>`
  background: ${appTheme.colors.background.lighter};
  border-radius: ${appTheme.borderRadius.md};
  padding: ${props => props.$compact ? appTheme.spacing.md : appTheme.spacing.lg};
  border: 1px solid ${appTheme.colors.border};
`;

const SettingsHeader = styled.div<{ $compact?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${props => props.$compact ? appTheme.spacing.sm : appTheme.spacing.md};
  font-weight: 500;
  color: ${appTheme.colors.text.primary};
  font-size: ${props => props.$compact ? '14px' : '16px'};
`;

const CheckboxContainer = styled.div<{ $compact?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  margin-bottom: ${props => props.$compact ? appTheme.spacing.sm : appTheme.spacing.md};
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: ${appTheme.colors.primary};
  cursor: pointer;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const CheckboxLabel = styled.label<{ $compact?: boolean }>`
  font-size: ${props => props.$compact ? '13px' : '14px'};
  color: ${appTheme.colors.text.primary};
  cursor: pointer;
  user-select: none;

  &:has(input:disabled) {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const DurationContainer = styled.div<{ $compact?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding-left: ${props => props.$compact ? '20px' : '24px'};
`;

const DurationInput = styled.input<{ $compact?: boolean }>`
  width: ${props => props.$compact ? '60px' : '80px'};
  padding: ${props => props.$compact ? 
    `${appTheme.spacing.xs} ${appTheme.spacing.sm}` : 
    `${appTheme.spacing.sm} ${appTheme.spacing.md}`
  };
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  font-size: ${props => props.$compact ? '12px' : '14px'};
  outline: none;

  &:focus {
    border-color: ${appTheme.colors.primary};
  }

  &:disabled {
    background: ${appTheme.colors.background.lighter};
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const DurationLabel = styled.span<{ $compact?: boolean }>`
  font-size: ${props => props.$compact ? '12px' : '14px'};
  color: ${appTheme.colors.text.secondary};
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  color: ${appTheme.colors.text.secondary};
  font-size: 13px;
`;

export default function ChatBotSettings({
  isBot,
  botDuration,
  onSettingsChange,
  loading = false,
  disabled = false,
  compact = false,
}: ChatBotSettingsProps) {
  const [localIsBot, setLocalIsBot] = useState(isBot);
  const [localBotDuration, setLocalBotDuration] = useState(botDuration);
  const [isDurationSaving, setIsDurationSaving] = useState(false);

  // Ref to store the debounce timeout
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Ref to store the latest duration value for debounced save
  const pendingDurationRef = useRef<number | null>(null);

  // Cleanup function to cancel pending debounced calls
  const cancelPendingDurationSave = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }
    pendingDurationRef.current = null;
    setIsDurationSaving(false);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelPendingDurationSave();
    };
  }, [cancelPendingDurationSave]);

  // Debounced save function for duration changes
  const debouncedSaveDuration = useCallback(async (duration: number) => {
    setIsDurationSaving(true);
    try {
      await onSettingsChange({
        isBot: localIsBot,
        botDuration: duration,
      });
    } catch (error) {
      // Revert on error
      setLocalBotDuration(botDuration);
      throw error;
    } finally {
      setIsDurationSaving(false);
      pendingDurationRef.current = null;
    }
  }, [localIsBot, onSettingsChange, botDuration]);

  const handleIsBotChange = async (checked: boolean) => {
    if (loading || disabled) return;

    // Cancel any pending duration saves when toggling bot mode
    cancelPendingDurationSave();

    setLocalIsBot(checked);
    const newDuration = checked ? (localBotDuration || 30) : 0;
    setLocalBotDuration(newDuration);

    try {
      await onSettingsChange({
        isBot: checked,
        botDuration: newDuration,
      });
    } catch (error) {
      // Revert on error
      setLocalIsBot(isBot);
      setLocalBotDuration(botDuration);
    }
  };

  const handleDurationChange = (duration: number) => {
    if (loading || disabled || !localIsBot) return;

    const validDuration = Math.max(1, Math.min(480, duration || 30));
    setLocalBotDuration(validDuration);

    // Cancel any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Store the pending duration value
    pendingDurationRef.current = validDuration;

    // Set up new debounced save with 500ms delay
    debounceTimeoutRef.current = setTimeout(() => {
      if (pendingDurationRef.current !== null) {
        debouncedSaveDuration(pendingDurationRef.current);
      }
    }, 500);

    // Show loading state immediately when user starts typing
    if (!isDurationSaving) {
      setIsDurationSaving(true);
    }
  };

  return (
    <SettingsContainer $compact={compact}>
      <SettingsHeader $compact={compact}>
        <Bot size={compact ? 16 : 18} />
        Bot Settings
        {(loading || isDurationSaving) && (
          <Loader2 size={14} className="animate-spin" />
        )}
      </SettingsHeader>

      <CheckboxContainer $compact={compact}>
        <Checkbox
          type="checkbox"
          id="chatBotToggle"
          checked={localIsBot}
          onChange={(e) => handleIsBotChange(e.target.checked)}
          disabled={loading || disabled}
        />
        <CheckboxLabel htmlFor="chatBotToggle" $compact={compact}>
          Enable Bot
        </CheckboxLabel>
      </CheckboxContainer>

      {localIsBot && (
        <DurationContainer $compact={compact}>
          <Clock size={compact ? 12 : 16} />
          <DurationLabel $compact={compact}>Session Duration:</DurationLabel>
          <DurationInput
            $compact={compact}
            type="number"
            min="1"
            max="480"
            value={localBotDuration}
            onChange={(e) => handleDurationChange(parseInt(e.target.value))}
            disabled={loading || disabled}
          />
          <DurationLabel $compact={compact}>minutes</DurationLabel>
        </DurationContainer>
      )}

      {(loading || isDurationSaving) && (
        <LoadingContainer>
          <Loader2 size={12} className="animate-spin" />
          {isDurationSaving ? 'Updating duration...' : 'Updating settings...'}
        </LoadingContainer>
      )}
    </SettingsContainer>
  );
}
