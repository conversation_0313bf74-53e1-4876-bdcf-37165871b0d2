// Application theme configuration
// Merged from kanban and settings themes

export const appTheme = {
  colors: {
    primary: '#6366f1',
    primaryHover: '#4f46e5',
    primaryLight: 'rgba(99, 102, 241, 0.15)',
    secondary: '#3b82f6',
    secondaryHover: '#2563eb',
    secondaryLight: 'rgba(59, 130, 246, 0.2)',
    text: {
      primary: '#111827',
      secondary: '#4b5563',
      tertiary: '#6b7280',
      light: '#9ca3af',
    },
    background: {
      main: '#ffffff',
      light: '#f9fafb',
      lighter: '#f3f4f6',
      lane: '#f5f5f5', // From kanban theme
    },
    border: '#e5e7eb', // Using settings theme value
    success: {
      main: '#10b981',
      light: '#d1fae5',
    },
    error: {
      main: '#ef4444',
      light: '#fee2e2',
    },
  },
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    card: '0 1px 3px rgba(0, 0, 0, 0.1)', // From kanban theme
    cardHover: '0 3px 6px rgba(0, 0, 0, 0.15)', // From kanban theme
  },
  borderRadius: {
    sm: '4px',
    md: '6px', // Compromise between kanban (6px) and settings (8px)
    lg: '8px', // Compromise between kanban (8px) and settings (12px)
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '0.75rem',
    base: '1rem',
    lg: '1.25rem',
    xl: '1.5rem',
    '2xl': '2rem',
  },
  typography: {
    fontSizes: {
      xs: '0.75rem', // Using settings theme value
      sm: '0.875rem', // Using settings theme value
      base: '1rem', // Using settings theme value
      lg: '1.125rem', // Using settings theme value
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem', // From settings theme
    },
    fontWeights: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  breakpoints: {
    sm: '480px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1600px',
  },
  transitions: {
    default: 'all 0.2s ease',
  },
};

// Common styled component mixins
export const appStyles = {
  // Card styles from both themes
  card: `
    background-color: ${appTheme.colors.background.main};
    border-radius: ${appTheme.borderRadius.md};
    box-shadow: ${appTheme.shadows.card};
    padding: ${appTheme.spacing.base};
    transition: ${appTheme.transitions.default};

    &:hover {
      transform: translateY(-2px);
      box-shadow: ${appTheme.shadows.cardHover};
    }
  `,
  // Kanban lane style
  lane: `
    background: ${appTheme.colors.background.lane};
    border-radius: ${appTheme.borderRadius.lg};
    width: 300px;
    min-width: 300px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: ${appTheme.shadows.sm};

    @media (max-width: ${appTheme.breakpoints.md}) {
      width: 280px;
      min-width: 280px;
    }

    @media (max-width: ${appTheme.breakpoints.sm}) {
      width: 260px;
      min-width: 260px;
    }
  `,
  // Input style from settings theme
  input: `
    padding: 0.75rem 1rem;
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.md};
    font-size: ${appTheme.typography.fontSizes.sm};
    width: 100%;
    background-color: ${appTheme.colors.background.light};
    transition: ${appTheme.transitions.default};

    &:focus {
      outline: none;
      border-color: ${appTheme.colors.primary};
      box-shadow: 0 0 0 3px ${appTheme.colors.primaryLight};
      background-color: ${appTheme.colors.background.main};
    }

    &:disabled {
      background-color: ${appTheme.colors.background.lighter};
      color: ${appTheme.colors.text.light};
      cursor: not-allowed;
    }

    @media (max-width: ${appTheme.breakpoints.sm}) {
      padding: 0.875rem 1rem;
      font-size: ${appTheme.typography.fontSizes.base};
    }
  `,
  // Button styles merged from both themes
  button: {
    primary: `
      background-color: ${appTheme.colors.primary};
      color: white;
      border: none;
      border-radius: ${appTheme.borderRadius.md};
      padding: 0.75rem 1.25rem;
      font-size: ${appTheme.typography.fontSizes.sm};
      font-weight: ${appTheme.typography.fontWeights.medium};
      cursor: pointer;
      transition: ${appTheme.transitions.default};
      box-shadow: 0 1px 3px ${appTheme.colors.primaryLight};

      &:hover {
        background-color: ${appTheme.colors.primaryHover};
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background-color: #a5b4fc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      @media (max-width: ${appTheme.breakpoints.sm}) {
        width: 100%;
        padding: 0.875rem 1.25rem;
        font-size: ${appTheme.typography.fontSizes.base};
      }
    `,
    secondary: `
      background-color: white;
      color: ${appTheme.colors.text.secondary};
      border: 1px solid ${appTheme.colors.border};
      border-radius: ${appTheme.borderRadius.md};
      padding: 0.75rem 1.25rem;
      font-size: ${appTheme.typography.fontSizes.sm};
      font-weight: ${appTheme.typography.fontWeights.medium};
      cursor: pointer;
      transition: ${appTheme.transitions.default};

      &:hover {
        background-color: ${appTheme.colors.background.light};
      }
    `,
  },
  // Section title from settings theme
  sectionTitle: `
    font-size: ${appTheme.typography.fontSizes.xl};
    font-weight: ${appTheme.typography.fontWeights.semibold};
    color: ${appTheme.colors.text.primary};
    padding-bottom: ${appTheme.spacing.sm};
  `,
};

// Export the old theme objects for backward compatibility
// These will reference the new appTheme to maintain consistency
export const kanbanTheme = appTheme;
export const kanbanStyles = {
  card: appStyles.card,
  lane: appStyles.lane,
  button: appStyles.button,
};

export const settingsTheme = appTheme;
export const commonStyles = {
  input: appStyles.input,
  button: appStyles.button,
  card: appStyles.card,
  sectionTitle: appStyles.sectionTitle,
};
