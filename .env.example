# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hiasangma?schema=public"

# Socket.IO Configuration
SOCKET_IO_PATH="/api/socket/io"

# JWT Configuration
JWT_SECRET="your-jwt-secret-change-in-production"
JWT_EXPIRES_IN="24h"

# AWS S3 Configuration
AWS_ACCESS_KEY_ID="your-aws-access-key-id"
AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
AWS_REGION="ap-southeast-1"
AWS_S3_BUCKET_NAME="your-s3-bucket-name"

# AWS CloudFront CDN URL
NEXT_PUBLIC_AWS_CDN_URL="https://your-cloudfront-distribution.cloudfront.net"

# LLM API Configuration
NEXT_PUBLIC_LITE_LLM_API_KEY="your-llm-api-key"
NEXT_PUBLIC_LITE_LLM_ENDPOINT="https://your-llm-endpoint.com"
