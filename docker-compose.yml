version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hia-suag-ma-app
    restart: unless-stopped
    environment:
      # Node Environment
      NODE_ENV: "production"
    ports:
      - "3000:3000"
    networks:
      - app-network
    volumes:
      - ./public/uploads:/app/public/uploads

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
